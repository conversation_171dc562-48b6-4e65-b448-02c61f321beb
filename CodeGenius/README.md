 [用户手册](https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/080e09aa2e624d6d80601411bde52185/view)


# 一、前置条件

- vscode版本: 1.93.0以上


# 二、使用简介

## 01模型选择

初次使用会打开模型选择的页面，一共有3个模型可选：

通义千问、DeepSeek、openai兼容模型

选择其中一个模型即可开始使用，建议选择其中的**通义千问**模型，通义千问使用默认配置即可，后续如果有配置变更会通知大家修改。

>注意：通义千问是我司部署的模型可以任意使用，如果配置DeepSeek或者其他外部模型，需要遵照**公司信息安全，备案**后才能使用。

|     |     |
| --- | --- |
| ![image.png](https://icenterapi.zte.com.cn/group3/M04/DE/47/CimMFmeWUFCAQokuAAB__EoUmok960.png) |  |

## 02 语言设置

插件默认中文，可以通过设置修改语言

|     |     |
| --- | --- |
| ![image.png](https://icenterapi.zte.com.cn/group3/M04/B4/B0/CimMFmePSReAPja_AAJHFjiWvqg131.png) |  |

## 03 使用简介

### 自动驾驶模式
默认是开启的自动驾驶模式，除非需要用户做必要的确认和信息补充，大模型会自动的生成代码和执行工具、命令；

|     |     |
| --- | --- |
| ![image.png](https://icenterapi.zte.com.cn/group3/M04/D2/EF/CimMFmeS_WqALsFiAAFDsU3vkF0165.png) |  |


### 选择文件/目录/问题

可以通过@的方式选择文件/目录/问题，类似cursor的功能，对某个文件/目录/问题进行提问；

|     |     |
| --- | --- |
| ![image.png](https://icenterapi.zte.com.cn/group3/M04/D7/C0/CimMFmeTTuGAMPrSAAEn33CWl20774.png) | ![image.png](https://icenterapi.zte.com.cn/group3/M04/D7/D4/CimMFmeTT56ALBCkAAFO1_abrrA134.png)  |

### 输入格式建议

- 任务描述建议使用md方式结构化描述，大模型更容易理解，效果也更好些;
- 如果希望以TDD的方式生成代码，需要明确指示出来;


```
@/daip-mms-dashboard/dashboard-server/dashboard-server-adapter/src/main/java/com/zte/daip/manager/mms/dashboard/server/adapter/controller/datasource/DataSourceController.java 
用户故事描述：
    - 完成DataourceController类的根据id删除数据源的功能
验收准则：
    - 当数据源不存在时，抛出DaipMmsException，同时给出提示信息
    - 当数据源存在，且不是默认数据源时，删除成功，返回删除的记录数
    - 当数据源存在，且是默认数据源时，删除失败，同时给出提示信息

注意采用TDD的方式，先生成单元测试，再生成生产代码
注意在原文件新增单元测试，不要修改或删除原有单元测试
```

## 04. 注意事项
### 支持的编程语言：
    C、C++、C#、GO、Java、PHP、Javascript、typescript、Python、ruby、rust、swift

### 遗留问题：

- 零号员工需要读取的单个文件超大（3000行左右）时，容易导致token超限而报错; 
- 通过@file明确指定文件上下文，有时会搜索不到存在的文件;
- 不能读取依赖的其它库文件作为本次代码生成的上下文知识;
