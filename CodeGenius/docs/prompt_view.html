<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt查看器</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
            overflow: hidden;
        }

        header {
            background: linear-gradient(to right, #2c3e50, #4a6491);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .file-panel {
            width: 280px;
            background-color: #f5f7fa;
            border-right: 1px solid #e0e6ed;
            padding: 20px;
            overflow-y: auto;
        }

        .file-panel h2 {
            color: #2c3e50;
            padding-bottom: 15px;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e6ed;
            font-size: 1.4rem;
        }

        .file-actions {
            margin-bottom: 20px;
        }

        .file-actions button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s;
            margin-bottom: 10px;
            font-size: 1rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .file-actions button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .file-actions button.secondary {
            background: #95a5a6;
        }

        .file-actions button.secondary:hover {
            background: #7f8c8d;
        }

        .file-list {
            list-style: none;
        }

        .file-item {
            padding: 12px 15px;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
            background-color: white;
            border: 1px solid #e0e6ed;
            display: flex;
            align-items: center;
        }

        .file-item:hover {
            background-color: #e3f2fd;
            border-color: #bbdefb;
            transform: translateX(5px);
        }

        .file-item.active {
            background-color: #d1e7ff;
            border-color: #90caf9;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.3);
        }

        .file-icon {
            margin-right: 12px;
            color: #3498db;
            font-size: 1.2rem;
        }

        .content-panel {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            background-color: white;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f4f8;
        }

        .content-header h2 {
            color: #2c3e50;
            font-size: 1.6rem;
        }

        .json-structure {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
            font-size: 0.9rem;
        }

        .message-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .message {
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }

        .message:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        }

        .role {
            font-weight: 600;
            font-size: 1.1rem;
            padding: 5px 5px;
            border-radius: 20px;
            color: white;
        }

        .system .role {
            background: linear-gradient(to right, #6a11cb, #2575fc);
        }

        .user .role {
            background: linear-gradient(to right, #11998e, #38ef7d);
        }

        .assistant .role {
            background: linear-gradient(to right, #ff512f, #f09819);
        }

        .toggle-btn {
            background: rgba(0, 0, 0, 0.05);
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
            color: #555;
            transition: all 0.2s;
        }

        .toggle-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .message-content {
            line-height: 1.6;
            font-size: 1.05rem;
            color: #444;
        }

        .text-content {
            white-space: pre-wrap;
            padding: 5px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .collapsed {
            max-height: 120px;
            overflow: hidden;
            position: relative;
        }

        .collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.9));
        }

        .system {
            background: linear-gradient(to right, #f0f4ff, #e6eeff);
            border-left: 4px solid #6a11cb;
        }

        .user {
            background: linear-gradient(to right, #f0fff4, #e6ffec);
            border-left: 4px solid #11998e;
        }

        .assistant {
            background: linear-gradient(to right, #fff8f0, #fff4e6);
            border-left: 4px solid #ff512f;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #bdc3c7;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .error {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        footer {
            text-align: center;
            padding: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.2);
            margin-top: 30px;
            border-radius: 0 0 12px 12px;
        }

        @media (max-width: 900px) {
            .main-content {
                flex-direction: column;
            }

            .file-panel {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e0e6ed;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>Prompt查看器</h1>
            <p class="subtitle">浏览和查看本地零号员工产生的prompt文件内容</p>
        </header>

        <div class="main-content">
            <div class="file-panel">
                <h2>文件管理</h2>

                <div class="file-actions">
                    <button id="add-files-btn"
                        title="零号员工插件产生的prompt文件保存路径:&#10;linux：用户目录/.config/Code/User/globalStorage/zeroagents-teams.zeroagents/prompts&#10;windows：用户目录\AppData\Roaming\Code\User\globalStorage\zeroagents-teams.zeroagents\prompts">添加prompt文件</button>
                    <button id="clear-files-btn" class="secondary">清空文件列表</button>
                </div>

                <ul class="file-list" id="file-list">
                    <li class="empty-state">暂无文件，请添加prompt文件</li>
                </ul>
            </div>

            <div class="content-panel">
                <div class="content-header">
                    <h2 id="current-file">文件内容</h2>
                </div>

                <div id="json-content">
                    <div class="empty-state">
                        <div>📁</div>
                        <h3>请选择文件查看内容</h3>
                        <p>左侧选择或添加Prompt文件，文件内容将在此处展示</p>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            <p>Prompt查看器 &copy; 2023 | 支持多文件管理和文本折叠功能</p>
        </footer>
    </div>

    <input type="file" id="file-input" accept=".json" multiple style="display: none;">

    <script>
        $(document).ready(function () {
            // 存储加载的文件
            const loadedFiles = {};

            // 添加文件按钮事件
            $("#add-files-btn").click(function () {
                $("#file-input").click();
            });

            // 清空文件列表
            $("#clear-files-btn").click(function () {
                $("#file-list").html('<li class="empty-state">暂无文件，请添加JSON文件</li>');
                $("#json-content").html('<div class="empty-state"><div>📁</div><h3>请选择文件查看内容</h3><p>左侧选择或添加JSON文件，文件内容将在此处展示</p></div>');
                $("#current-file").text("文件内容");
                // 清空加载的文件数据
                Object.keys(loadedFiles).forEach(key => delete loadedFiles[key]);
            });

            // 处理文件选择
            $("#file-input").change(function (e) {
                const files = e.target.files;

                const fileCount = files.length;

                if (!fileCount) return;

                // 清空空状态
                if ($("#file-list .empty-state").length) {
                    $("#file-list").empty();
                }


                // 处理每个文件
                Array.from(files).forEach((file, index) => {
                    const reader = new FileReader();

                    reader.onload = function (event) {
                        try {
                            // 解析JSON内容
                            const content = JSON.parse(event.target.result);

                            const date = new Date(file.lastModified);

                            const localTime = date.toLocaleString();
                            const file_date = `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}_`;

                            const curFile = file_date + file.name


                            // 存储文件内容
                            loadedFiles[curFile] = content;

                            if (index === fileCount - 1) {
                                sortFileList();
                            }


                        } catch (e) {
                            showError(`解析文件 ${file.name} 失败: ${e.message}`);
                        }
                    };

                    reader.onerror = function () {
                        showError(`读取文件 ${file.name} 失败`);
                    };

                    reader.readAsText(file);
                });

                // 重置文件输入，允许再次选择相同文件
                $(this).val('');
            });

            // 添加排序函数
            function sortFileList() {
                $("#file-list").empty(); // 清空列表

                Object.keys(loadedFiles).sort().forEach((curFile, index) => {
                    // 添加到文件列表（如果尚未添加）
                    if (!$(`.file-item[data-file="${curFile}"]`).length) {
                        const fileItem = $(`
                                    <li class="file-item" data-file="${curFile}">
                                        <span class="file-icon">📄</span>
                                        <span class="file-name">${curFile}</span>
                                    </li>
                                `);

                        $("#file-list").append(fileItem);

                        // 绑定点击事件
                        fileItem.click(function () {
                            $(".file-item").removeClass("active");
                            $(this).addClass("active");
                            displayFileContent(curFile);
                        });

                        // 如果是第一个文件，自动选择
                        if (index === 0) {
                            $(`.file-item[data-file="${curFile}"]`).click();
                        }
                    }
                })
            }


            // 显示文件内容
            function displayFileContent(fileName) {
                $("#current-file").text(fileName);

                const fileData = loadedFiles[fileName];
                if (!fileData) {
                    $("#json-content").html(`
                        <div class="error">
                            <h3>文件内容未加载</h3>
                            <p>无法显示 ${fileName} 的内容，请确保文件已正确加载</p>
                        </div>
                    `);
                    return;
                }

                // 检查数据结构
                if (!Array.isArray(fileData)) {
                    $("#json-content").html(`
                        <div class="error">
                            <h3>数据结构错误</h3>
                            <p>文件内容应该是数组格式</p>
                        </div>
                    `);
                    return;
                }



                let contentHtml = '<div class="message-container">';


                if ("conversationHistoryIndex" in fileData[0]) {
                    let curHIsId = -100;

                    fileData.forEach((item, index) => {
                        const conversationHistoryIndex = item.conversationHistoryIndex

                        if (conversationHistoryIndex !== curHIsId) {
                            const role = conversationHistoryIndex % 2 === 0 ? 'assistant' : "user";
                            const roleClass = role;

                            if (conversationHistoryIndex !== -1) {
                                contentHtml += `    </div>  </div>  `;
                            }

                            contentHtml += `
                            <div class="message ${roleClass}">
                                <div class="message-header">
                                    <span class="role">${role}  ${((conversationHistoryIndex + 3) / 2) | 0}</span>
                                    <button class="toggle-btn">展开</button>
                                </div>
                                <div class="message-content">
                            `;

                            curHIsId = conversationHistoryIndex;
                        } else {

                        }

                        const say = item.say

                        if (say !== 'text') {
                            let text = item.text
                            const ask = item.ask
                            if (say && text) {
                                contentHtml += `
                                        <div class="text-content collapsed">${say + ':<br>' + escapeHtml(text)}</div>
                                    `;
                            } else if (ask && text) {
                                contentHtml += `
                                        <div class="text-content collapsed">${ask + ':<br>' + escapeHtml(text)}</div>
                                    `;
                            }
                        }

                    });
                    contentHtml += `   </div>   </div> `;
                } else {
                    fileData.forEach((item, index) => {
                        const role = item.role;
                        const roleClass = role;

                        contentHtml += `
                        <div class="message ${roleClass}">
                            <div class="message-header">
                                <span class="role">${role}  ${(index / 2 | 0) + 1}</span>
                                <button class="toggle-btn">展开</button>
                            </div>
                            <div class="message-content">
                        `;

                        if (item.content) {
                            if (Array.isArray(item.content)) {
                                item.content.forEach((contentItem, contentIndex) => {
                                    if (contentItem.type === "text" && contentItem.text) {
                                        contentHtml += `
                                        <div class="text-content collapsed"> ${escapeHtml(contentItem.text)}</div>
                                    `;
                                    } else {
                                        // contentHtml += `
                                        // <div class="error">
                                        //     <p>内容块 #${contentIndex + 1} 缺少有效的文本内容</p>
                                        // </div>
                                        // `;
                                    }
                                });
                            } else {
                                contentHtml += `
                                        <div class="text-content collapsed">${escapeHtml(item.content)}</div>
                                    `;
                            }
                        } else {

                        }

                        contentHtml += `
                        </div>
                        </div>
                    `;

                    });
                }

                contentHtml += '</div>';
                $("#json-content").html(contentHtml);

                // 绑定折叠/展开事件
                $(".toggle-btn").click(function () {
                    const $content = $(this).closest(".message").find(".text-content");
                    const isCollapsed = $content.hasClass("collapsed");

                    if (isCollapsed) {
                        $content.removeClass("collapsed");
                        $(this).text("折叠");
                    } else {
                        $content.addClass("collapsed");
                        $(this).text("展开");
                    }
                });
            }

            // 显示错误信息
            function showError(message) {
                const errorHtml = `
                    <div class="error">
                        <h3>发生错误</h3>
                        <p>${message}</p>
                    </div>
                `;
                $("#json-content").html(errorHtml);
            }


            function escapeHtml(unsafe) {
                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;")
                    .replace(/\\n/g, "<br>")
                    .trim();
            }


            // 添加示例文件
            setTimeout(() => {
                // 清空空状态
                if ($("#file-list .empty-state").length) {
                    $("#file-list").empty();
                }

                // 默认选择第一个文件
                if ($(".file-item").length) {
                    $(".file-item").first().click();
                }
            }, 500);
        });
    </script>
</body>

</html>