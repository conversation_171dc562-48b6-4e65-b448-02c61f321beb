import * as path from "path"
import * as fs from "fs"
import execa from "execa"
import { BenchmarkAdapter, Task, VerificationResult } from "./types"

const EVALS_DIR = path.resolve(__dirname, "../../../")

/**
 * Adapter for the modified Exercism benchmark
 */
export class ExercismAdapter implements BenchmarkAdapter {
	name = "exercism"

	/**
	 * Set up the Exercism benchmark repository
	 */
	async setup(): Promise<void> {
		// Clone repository if needed
		const exercismDir = path.join(EVALS_DIR, "repositories", "exercism")

		if (!fs.existsSync(exercismDir)) {
			console.log(`Cloning Exercism repository to ${exercismDir}...`)
			await execa("git", ["clone", "https://github.com/pashpashpash/evals.git", exercismDir])
			console.log("Exercism repository cloned successfully")
		} else {
			console.log(`Exercism repository already exists at ${exercismDir}`)

			// Pull latest changes
			console.log("Pulling latest changes...")
			await execa("git", ["pull"], { cwd: exercismDir })
			console.log("Repository updated successfully")
		}
	}

	/**
	 * List all available tasks in the Exercism benchmark
	 */
	async listTasks(): Promise<Task[]> {
		const tasks: Task[] = []
		const exercisesDir = path.join(EVALS_DIR, "repositories", "exercism")

		// Ensure the repository exists
		if (!fs.existsSync(exercisesDir)) {
			throw new Error(`Exercism repository not found at ${exercisesDir}. Run setup first.`)
		}

		// Read language directories
		const languages = fs
			.readdirSync(exercisesDir)
			.filter((dir) => fs.statSync(path.join(exercisesDir, dir)).isDirectory())
			.filter((dir) => !dir.startsWith(".") && !["node_modules", ".git"].includes(dir))

		for (const language of languages) {
			const languageDir = path.join(exercisesDir, language)
			const lang_prompt_path = path.join(exercisesDir, "prompts", `${language}.md`)

			// Read exercise directories
			const exercises = fs.readdirSync(languageDir).filter((dir) => fs.statSync(path.join(languageDir, dir)).isDirectory())

			for (const exercise of exercises) {
				const exerciseDir = path.join(languageDir, exercise)

				// Read instructions
				let description = ""
				const instructionsPath = path.join(exerciseDir, "docs", "instructions.md")
				if (fs.existsSync(instructionsPath)) {
					// description = fs.readFileSync(instructionsPath, "utf-8")
					description = fs.readFileSync(lang_prompt_path, "utf-8")
				}

				// Determine test commands based on language
				let testCommands: string[] = []
				switch (language) {
					case "javascript":
						testCommands = ["npm test"]
						break
					case "python":
						testCommands = ["python -m pytest -o markers=task*_test.py"]
						break
					case "go":
						testCommands = ["go test"]
						break
					case "java":
						testCommands = ["gradle test"]
						break
					case "rust":
						testCommands = ["cargo test"]
						break
					default:
						testCommands = []
				}

				tasks.push({
					id: `exercism-${language}-${exercise}`,
					name: exercise,
					description,
					workspacePath: exerciseDir,
					setupCommands: [],
					verificationCommands: testCommands,
					metadata: {
						language,
						type: "exercism",
					},
				})
			}
		}

		return tasks
	}

	/**
	 * Prepare a specific task for execution
	 * @param taskId The ID of the task to prepare
	 */
	async prepareTask(taskId: string): Promise<Task> {
		const tasks = await this.listTasks()
		const task = tasks.find((t) => t.id === taskId)

		if (!task) {
			throw new Error(`Task ${taskId} not found`)
		}

		// Check if Git repository is already initialized
		const gitDirExists = fs.existsSync(path.join(task.workspacePath, ".git"))

		try {
			// Initialize Git repository if needed
			if (!gitDirExists) {
				await execa("git", ["init"], { cwd: task.workspacePath })
			}

			// Create a dummy file to ensure there's something to commit
			const dummyFilePath = path.join(task.workspacePath, ".eval-timestamp")
			fs.writeFileSync(dummyFilePath, new Date().toISOString())

			// Add all files and commit
			await execa("git", ["add", "."], { cwd: task.workspacePath })

			try {
				await execa("git", ["commit", "-m", "Initial commit"], { cwd: task.workspacePath })
			} catch (error: any) {
				// If commit fails because there are no changes, that's okay
				if (!error.stderr?.includes("nothing to commit")) {
					throw error
				}
			}
		} catch (error: any) {
			console.warn(`Warning: Git operations failed: ${error.message}`)
			console.warn("Continuing without Git initialization")
		}

		return task
	}

	/**
	 * Verify the result of a task execution
	 * @param task The task that was executed
	 * @param result The result of the task execution
	 */
	async verifyResult(task: Task, result: any): Promise<VerificationResult> {
		// Run verification commands
		let success = true
		let output = ""
		let finalCommand = ""
		for (const command of task.verificationCommands) {
			try {
				finalCommand = command
				const [cmd, ...args] = command.split(" ")
				const result = await execa(cmd, args, { cwd: task.workspacePath, all: true })
				output += result.all + "\n"
			} catch (error: any) {
				success = false
				if (error.stdout) {
					output += error.stdout + "\n"
				}
				if (error.stderr) {
					output += error.stderr + "\n"
				}
				return await this.parseTestResult(finalCommand, output, success)
			}
		}

		return await this.parseTestResult(finalCommand, output, success)
		// Parse test results
		// const testsPassed = (output.match(/✓/g) || []).length
    	// const testsFailed = (output.match(/✕/g) || []).length
		// const testsTotal = testsPassed + testsFailed

		// return {
		// 	success,
		// 	metrics: {
		// 		testsPassed,
		// 		testsFailed,
		// 		testsTotal,
		// 		functionalCorrectness: testsTotal > 0 ? testsPassed / testsTotal : 0,
		// 	},
		// }
	}

	async parseTestResult(command: string, output: string, success: boolean) {
		let testsPassed: number = 0, testsFailed: number = 0, testsTotal: number = 0
		switch (command) {
			case "npm test":
				testsPassed = (output.match(/✓/g) || []).length
				testsFailed = (output.match(/✕/g) || []).length
				testsTotal = testsPassed + testsFailed
				break
			case "python -m pytest -o markers=task*_test.py":
				  // 匹配 "15 passed" 之类的摘要
				  const passedMatch = output.match(/(\d+)\s+passed/);
				// 匹配 "1 failed" 之类的摘要
				const failedMatch = output.match(/(\d+)\s+failed/);
				testsPassed = passedMatch ? parseInt(passedMatch[1], 10) : 0
				testsFailed = failedMatch ? parseInt(failedMatch[1], 10) : 0
				testsTotal = testsPassed + testsFailed
				break
			case "gradle test":
				const summaryMatch = output.match(/(\d+)\s+tests completed(?:,\s+(\d+)\s+failed)?/);
				if (summaryMatch) {
					testsTotal = parseInt(summaryMatch[1], 10);
					testsFailed = summaryMatch[2] ? parseInt(summaryMatch[2], 10) : 0;
					testsPassed = testsTotal - testsFailed;
				} else {
					// 如果没有汇总信息，也可 fallback 到详情行计数
					const detailPassed = (output.match(/>\s+\S+\s+PASSED/g) || []).length;
					const detailFailed = (output.match(/>\s+\S+\s+FAILED/g) || []).length;
					testsPassed = detailPassed;
					testsFailed = detailFailed;
					testsTotal = detailPassed + detailFailed;
				}
				break;
	

		}
		return {
			success,
			metrics: {
				testsPassed,
				testsFailed,
				testsTotal,
				functionalCorrectness: testsTotal > 0 ? testsPassed / testsTotal : 0,
			},
		}
	}
}
