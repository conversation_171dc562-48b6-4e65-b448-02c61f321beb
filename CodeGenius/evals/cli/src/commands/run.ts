import * as path from "path"
import { v4 as uuidv4 } from "uuid"
import chalk from "chalk"
import ora from "ora"
import { getAdapter } from "../adapters"
import { ResultsDatabase } from "../db"
import { spawnVSCode, cleanupVSCode } from "../utils/vscode"
import { sendTaskToServer } from "../utils/task"
import { storeTaskResult } from "../utils/results"
import { VerificationResult } from "../adapters/types"
import * as fs from "fs"
import execa from "execa"

interface RunOptions {
	benchmark?: string
	model: string
	count?: number
	apiKey?: string
}

async function killPort() {
	try {
		const { stdout } = await execa('lsof', ['-t', `-i:9876`], { shell: true });
		const pids = stdout.split('\n').filter(pid => pid.trim());

		if (!pids.length) {
			console.log(`not exist 9876`)
			return
		}

		console.log(`exist 9876,to kill ...${stdout}`)

		await execa('kill', ['-9', ...pids], { shell: true });

	} catch (error: any) {
		console.info("no port 9876 occupied")
	}
}

/**
 * Handler for the run command
 * @param options Command options
 */
export async function runHandler(options: RunOptions): Promise<void> {
	// Determine which benchmarks to run
	const benchmarks = options.benchmark ? [options.benchmark] : ["exercism"] // Default to exercism for now
	const model = options.model
	const count = options.count || Infinity

	console.log(chalk.blue(`Running evaluations for model: ${model}`))
	console.log(chalk.blue(`Benchmarks: ${benchmarks.join(", ")}`))

	// Create a run for each benchmark
	for (const benchmark of benchmarks) {
		const runId = uuidv4()
		const db = new ResultsDatabase()

		console.log(chalk.green(`\nStarting run for benchmark: ${benchmark}`))

		// Create run in database
		db.createRun(runId, model, benchmark)

		// Get adapter for this benchmark
		try {
			const adapter = getAdapter(benchmark)

			// List tasks
			const spinner = ora("Listing tasks...").start()
			const tasks = await adapter.listTasks()
			spinner.succeed(`Found ${tasks.length} tasks for ${benchmark}`)

			// Limit number of tasks if specified
			const tasksToRun = tasks.slice(0, count)

			console.log(chalk.blue(`Running ${tasksToRun.length} tasks...`))

			// Run each task
			for (let i = 0; i < tasksToRun.length; i++) {
				killPort()

				const task = tasksToRun[i]

				console.log(chalk.cyan(`\n${new Date().toLocaleTimeString()} Task ${i + 1}/${tasksToRun.length}: ${task.name}`))

				// Prepare task
				const prepareSpinner = ora("Preparing task...").start()
				const preparedTask = await adapter.prepareTask(task.id)
				prepareSpinner.succeed("Task prepared")

				// Spawn VSCode
				console.log(`${new Date().toLocaleTimeString()} Spawning VSCode...`)
				await spawnVSCode(preparedTask.workspacePath, getVsixPath())

				// Send task to server
				const sendSpinner = ora(`${new Date().toLocaleTimeString()} Sending task to server...${i + 1}/${tasksToRun.length}: ${task.name}`).start()
				try {
					let result: boolean
					try {
						result = await sendTaskToServer(preparedTask.description, options.model)
						sendSpinner.succeed(`${new Date().toLocaleTimeString()} Task completed`)
					} catch (error: any) {
						result = false
						sendSpinner.fail(`${new Date().toLocaleTimeString()} Task failed: ${error.message}`)
						console.error(chalk.red(error.stack))
					}

					// Verify result
					const verifySpinner = ora("Verifying result...").start()
					const verification = await Promise.race([
						adapter.verifyResult(preparedTask, result),
						new Promise<VerificationResult>((resolve) =>
							setTimeout(resolve, 60000, {
								success: false,
								metrics: {
									testsPassed: 0,
									testsFailed: 0,
									testsTotal: 0,
									functionalCorrectness: 0,
								}
							})
						)
					])

					if (verification.success) {
						verifySpinner.succeed(
							`Verification successful: ${verification.metrics.testsPassed}/${verification.metrics.testsTotal} tests passed`,
						)
					} else {
						verifySpinner.fail(
							`Verification failed: ${verification.metrics.testsPassed}/${verification.metrics.testsTotal} tests passed`,
						)
					}

					// Store result
					const storeSpinner = ora("Storing result...").start()
					await storeTaskResult(runId, preparedTask, result, verification)
					storeSpinner.succeed("Result stored")

					console.log(chalk.green(`Task completed. Success: ${verification.success}`))

					// Clean up VS Code and temporary files
					const cleanupSpinner = ora("Cleaning up...").start()
					try {
						await cleanupVSCode(preparedTask.workspacePath)
						cleanupSpinner.succeed("Cleanup completed")
					} catch (cleanupError: any) {
						cleanupSpinner.fail(`Cleanup failed: ${cleanupError.message}`)
						console.error(chalk.yellow(cleanupError.stack))
					}
				} catch (error: any) {
					sendSpinner.fail(`Task failed: ${error.message}`)
					console.error(chalk.red(error.stack))

					// Clean up VS Code and temporary files even if the task failed
					const cleanupSpinner = ora("Cleaning up...").start()
					try {
						await cleanupVSCode(preparedTask.workspacePath)
						cleanupSpinner.succeed("Cleanup completed")
					} catch (cleanupError: any) {
						cleanupSpinner.fail(`Cleanup failed: ${cleanupError.message}`)
						console.error(chalk.yellow(cleanupError.stack))
					}
				}
			}

			// Mark run as complete
			db.completeRun(runId)

			console.log(chalk.green(`\nRun complete for benchmark: ${benchmark}`))
		} catch (error: any) {
			console.error(chalk.red(`Error running benchmark ${benchmark}: ${error.message}`))
			console.error(error.stack)
		}
	}

	console.log(chalk.green("\nAll evaluations complete"))
}

export function getVsixPath() {
	const clineRoot = path.resolve(process.cwd(), "..", "..")
	const files = fs.readdirSync(clineRoot)
	const vsixFiles = files.filter((file) => file.endsWith(".vsix"))
	if (vsixFiles.length > 0) {
		// Get file stats to find the most recent one
		const vsixFilesWithStats = vsixFiles.map((file) => {
			const filePath = path.join(clineRoot, file)
			return {
				file,
				path: filePath,
				mtime: fs.statSync(filePath).mtime,
			}
		})

		// Sort by modification time (most recent first)
		vsixFilesWithStats.sort((a, b) => b.mtime.getTime() - a.mtime.getTime())

		// Use the most recent VSIX
		const vsixPath = vsixFilesWithStats[0].path
		console.log(`find vsix path: ${vsixPath}`)
		return vsixPath
	} else {
		console.log('Could not find generated VSIX file, Building VSIX...')
		return ""
	}
}