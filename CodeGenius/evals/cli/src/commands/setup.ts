import * as path from "path"
import * as fs from "fs"
import execa from "execa"
import chalk from "chalk"
import ora from "ora"
import { getAllAdapters } from "../adapters/index"
import { BenchmarkAdapter } from "../adapters/types"

interface SetupOptions {
	benchmarks: string
}

/**
 * Handler for the setup command
 * @param options Command options
 */
export async function setupHandler(options: SetupOptions): Promise<void> {
	const benchmarks = options.benchmarks.split(",")

	console.log(chalk.blue(`Setting up benchmarks: ${benchmarks.join(", ")}`))

	// Create directories
	const evalsDir = path.resolve(__dirname, "../../../")
	const reposDir = path.join(evalsDir, "repositories")
	const resultsDir = path.join(evalsDir, "results")

	const spinner = ora("Creating directory structure").start()

	try {
		fs.mkdirSync(reposDir, { recursive: true })
		fs.mkdirSync(resultsDir, { recursive: true })
		fs.mkdirSync(path.join(resultsDir, "runs"), { recursive: true })
		fs.mkdirSync(path.join(resultsDir, "reports"), { recursive: true })
		spinner.succeed("Directory structure created")
	} catch (error) {
		spinner.fail(`Failed to create directory structure: ${(error as Error).message}`)
		throw error
	}

	// Set up each benchmark
	try {
		const adapters = getAllAdapters().filter((adapter: BenchmarkAdapter) => benchmarks.includes(adapter.name))

		if (adapters.length === 0) {
			console.warn(chalk.yellow("No valid benchmarks specified. Available benchmarks:"))
			console.warn(
				chalk.yellow(
					getAllAdapters()
						.map((a: BenchmarkAdapter) => a.name)
						.join(", "),
				),
			)
			return
		}

		for (const adapter of adapters) {
			const setupSpinner = ora(`Setting up ${adapter.name}...`).start()
			try {
				await adapter.setup()
				setupSpinner.succeed(`${adapter.name} setup complete`)
			} catch (error) {
				setupSpinner.fail(`Failed to set up ${adapter.name}: ${(error as Error).message}`)
				throw error
			}
		}

		await setUpGradleProperties()

		console.log(chalk.green("Setup complete"))
	} catch (error) {
		console.error(chalk.red(`Setup failed: ${(error as Error).message}`))
		throw error
	}
}

export async function setUpGradleProperties(): Promise<void> {
	const evalsDir = path.resolve(__dirname, "../../../")
	const gradlePropertiesFile = path.join(evalsDir, "cli", "src", "properties", "build.gradle")
	const targetDir = path.join(evalsDir, "repositories", "exercism", "java")

	// Read the content of the gradle properties file
	const fileContent = fs.readFileSync(gradlePropertiesFile, "utf-8")

	// Get all subdirectories in targetDir
	const subdirectories = fs.readdirSync(targetDir).filter(subdir => {
		const fullPath = path.join(targetDir, subdir)
		return fs.statSync(fullPath).isDirectory()
	})

	// Copy the file to each subdirectory
	for (const subdir of subdirectories) {
		const targetPath = path.join(targetDir, subdir, "build.gradle")
		fs.writeFileSync(targetPath, fileContent)
	}
}