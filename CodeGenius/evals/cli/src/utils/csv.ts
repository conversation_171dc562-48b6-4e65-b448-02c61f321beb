import fs from 'fs';
import path from 'path';

export function generateCSVReports(benchmarkReports: Record<string, any>, reportDir: string) {
 
  
  for (const [benchmark, report] of Object.entries(benchmarkReports)) {
    const javaRows = [];
    const pythonRows = [];
    
    // CSV headers
    javaRows.push(['id', '任务id', '用例通过数量', '用例失败数量', '用例总数', '得分']);
    pythonRows.push(['id', '任务id', '用例通过数量', '用例失败数量', '用例总数', '得分']);
    
    // Filter tasks by language
    for (const task of report.taskInfo) {
      const row = [task.id, task.task_id, task.testsPassed, task.testsFailed, task.testsTotal, task.functionalCorrectness.toFixed(2)];
      if (task.task_id.includes('java')) {
        javaRows.push(row);
      } else if (task.task_id.includes('python')) {
        pythonRows.push(row);
      }
    }

    const timestamp = new Date().toISOString().replace(/:/g, "-")
    
    // Write Java CSV
    if (javaRows.length > 1) {
      const javaContent = javaRows.map(r => r.join(',')).join('\n');
      fs.writeFileSync(path.join(reportDir, `${benchmark}_java_${timestamp}.csv`), javaContent);
    }
    
    // Write Python CSV
    if (pythonRows.length > 1) {
      const pythonContent = pythonRows.map(r => r.join(',')).join('\n');
      fs.writeFileSync(path.join(reportDir, `${benchmark}_python_${timestamp}.csv`), pythonContent);
    }
  }
}