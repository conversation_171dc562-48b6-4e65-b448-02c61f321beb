

export function validateMarkerSequencing(lines: string[]): { success: boolean; error?: string; result?: string[] } {
    enum State {
        START,
        AFTER_SEARCH,
        AFTER_SEPARATOR,
    }
    const state = { current: State.START, line: 0, previousMarker: "" }

    const SEARCH = "<<<<<<< SEARCH"
    const SEP = "======="
    const REPLACE = ">>>>>>> REPLACE"
    const SEARCH_PREFIX = "<<<<<<<"
    const REPLACE_PREFIX = ">>>>>>>"
    const SEARCH_REGEX = /^<{3,}\s*SEARCH$/
    const REPLACE_REGEX = /^>{3,}\s*REPLACE$/

    const reportMergeConflictError = (found: string, expected: string) => ({
        success: false,
        error:
            `ERROR: Special marker '${found}' found in your diff content at line ${state.line}:\n` +
            "\n" +
            `remove merge conflict markers like '${found}' from files, Expected:` + 
            "\n" +
            "CORRECT FORMAT:\n\n" +
            "<<<<<<< SEARCH\n" +
            "[exact content to find including whitespace]\n" +
            "=======\n" +
            "[new content to replace with]\n" +
            ">>>>>>> REPLACE\n",
    })

    const reportInvalidDiffError = (found: string, expected: string) => ({
        success: false,
        error:
            `ERROR: Diff block is malformed: marker '${found}' found in your diff content at line ${state.line}. Expected: ${expected}\n` +
            "\n" +
            "CORRECT FORMAT:\n\n" +
            "<<<<<<< SEARCH\n" +
            "[exact content to find including whitespace]\n" +
            "=======\n" +
            "[new content to replace with]\n" +
            ">>>>>>> REPLACE\n",
    })

    const searchCount = lines.filter((l) => l.trim() === SEARCH).length
    const sepCount = lines.filter((l) => l.trim() === SEP).length
    const replaceCount = lines.filter((l) => l.trim() === REPLACE).length

    const likelyBadStructure = searchCount !== replaceCount || sepCount < searchCount

    let arr: string[] = []

    for (const line of lines) {
        state.line++
        const marker = line.trim()

        switch (state.current) {
            case State.START:
                if (marker === SEP) {
                    return likelyBadStructure
                        ? reportInvalidDiffError(SEP, SEARCH)
                        : reportMergeConflictError(SEP, SEARCH)
                }
                if (REPLACE_REGEX.test(marker)) { return reportInvalidDiffError(REPLACE, SEARCH) }
                if (marker.startsWith(REPLACE_PREFIX)) { return reportMergeConflictError(marker, SEARCH) }
                if (SEARCH_REGEX.test(marker)) {
                    state.current = State.AFTER_SEARCH
                    arr.push(SEARCH)
                    state.previousMarker = marker
                    continue
                }
                else if (marker.startsWith(SEARCH_PREFIX)) { return reportMergeConflictError(marker, SEARCH) }
                if (marker !== '') { state.previousMarker = marker }
                arr.push(line)
                break

            case State.AFTER_SEARCH:
                if (SEARCH_REGEX.test(marker)) { return reportInvalidDiffError(SEARCH, SEP) }
                if (marker.startsWith(SEARCH_PREFIX)) { return reportMergeConflictError(marker, SEARCH) }
                if (REPLACE_REGEX.test(marker)) { return reportInvalidDiffError(REPLACE, SEP) }
                if (marker.startsWith(REPLACE_PREFIX)) { return reportMergeConflictError(marker, SEARCH) }
                if (marker === SEP) {
                    state.current = State.AFTER_SEPARATOR
                    state.previousMarker = marker
                    arr.push(SEP)
                    continue
                }
                if (marker !== '') { state.previousMarker = marker }
                arr.push(line)
                break

            case State.AFTER_SEPARATOR:
                if (SEARCH_REGEX.test(marker)) { return reportInvalidDiffError(SEARCH, REPLACE) }
                if (marker.startsWith(SEARCH_PREFIX)) { return reportMergeConflictError(marker, REPLACE) }
                if (marker === SEP) {
                    if (state.previousMarker === SEP) {
                        state.previousMarker = marker
                        continue
                    } else {
                        return likelyBadStructure
                            ? reportInvalidDiffError(SEP, REPLACE)
                            : reportMergeConflictError(SEP, REPLACE)
                    }
                }
                if (REPLACE_REGEX.test(marker)) {
                    state.current = State.START
                    state.previousMarker = marker
                    arr.push(REPLACE)
                    continue
                }
                else if (marker.startsWith(REPLACE_PREFIX)) { return reportMergeConflictError(marker, REPLACE) }
                if (marker !== '') { state.previousMarker = marker }
                arr.push(line)
                break
        }
    }


    return state.current === State.START
        ? { success: true, result: arr }
        : {
            success: false,
            error: `ERROR: Unexpected end of sequence: Expected '${state.current === State.AFTER_SEARCH ? SEP : REPLACE}' was not found.`,
        }
}