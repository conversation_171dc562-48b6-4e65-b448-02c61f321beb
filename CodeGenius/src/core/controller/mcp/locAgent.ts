import * as vscode from "vscode"
import * as os from "os"
import * as childProcess from "child_process"
import * as path from "path"
import * as fs from "fs"
import * as net from "net"
import type { McpHub } from "@/services/mcp/McpHub"
import { ZeroLogger } from "@/utils/logger"
import type { Controller } from "../index"

const logger = new ZeroLogger("locAgent")

interface LocAgentOptions {
  readonly mcpHub: McpHub
  readonly controller: Controller
  readonly localePath: string
  readonly outputChannel: vscode.OutputChannel
}

export class LocAgent {
  private static mcpHub: McpHub
  private static controller: Controller
  private static port: number = 5002
  private static serverName = "LocAgent"
  private static host = "127.0.0.1"
  private static serverUrl = "http://{host}:{port}/sse"
  private static repo: string
  private static cwd: string
  private static pid: number | undefined = undefined
  private static outputChannel: vscode.OutputChannel

  static async install(options: LocAgentOptions) {
    try {
      const { mcpHub, controller, localePath, outputChannel } = options

      LocAgent.mcpHub = mcpHub
      LocAgent.controller = controller
      LocAgent.cwd = path.join(localePath, LocAgent.serverName)
      LocAgent.outputChannel = outputChannel
      await LocAgent.dispose()
      await LocAgent.mcpHub.addRemoteServer(LocAgent.serverName, LocAgent.serverUrl.replace(/{host}/g, LocAgent.host).replace(/{port}/g, LocAgent.port.toString()))

      const folders = vscode.workspace.workspaceFolders
      if (Array.isArray(folders) && folders.length) {
        LocAgent.repo = folders[0].uri.fsPath
        LocAgent.localeServer()
        await LocAgent.waitForServer({ port: LocAgent.port })
        await LocAgent.connectServer()
      } else {
        throw new Error(`当前Workspace为空`)
      }

      await LocAgent.controller.postMessageToWebview({
        type: "addRemoteServerResult",
        addRemoteServerResult: {
          success: true,
          serverName: LocAgent.serverName,
        },
      })
    } catch (error) {
      logger.error(`LocAgent安装失败: ${error}`)
      await LocAgent.controller.postMessageToWebview({
        type: "addRemoteServerResult",
        addRemoteServerResult: {
          success: false,
          serverName: LocAgent.serverName,
          error: error.message,
        },
      })
    }
  }

  static async init(options: LocAgentOptions) {
    try {
      const { mcpHub, controller, localePath, outputChannel } = options

      LocAgent.mcpHub = mcpHub
      LocAgent.controller = controller
      LocAgent.cwd = path.join(localePath, LocAgent.serverName)
      LocAgent.outputChannel = outputChannel
      await LocAgent.dispose()

      const mcpSettingsFilePath = await LocAgent.mcpHub.getMcpSettingsFilePath()
      if (!mcpSettingsFilePath) {
        return
      }
      const mcpSettings = JSON.parse(fs.readFileSync(mcpSettingsFilePath, "utf-8"))
      if (!mcpSettings?.mcpServers || !mcpSettings.mcpServers[LocAgent.serverName]) {
        return
      }

      const folders = vscode.workspace.workspaceFolders
      if (Array.isArray(folders) && folders.length) {
        LocAgent.repo = folders[0].uri.fsPath
        LocAgent.localeServer()
        await LocAgent.waitForServer({ port: LocAgent.port })
        await LocAgent.connectServer()
      } else {
        throw new Error(`当前Workspace为空`)
      }

      await LocAgent.controller.postMessageToWebview({
        type: "addRemoteServerResult",
        addRemoteServerResult: {
          success: true,
          serverName: LocAgent.serverName,
        },
      })
    } catch (error) {
      logger.error(`LocAgent启动失败: ${error}`)
      await LocAgent.controller.postMessageToWebview({
        type: "addRemoteServerResult",
        addRemoteServerResult: {
          success: false,
          serverName: LocAgent.serverName,
          error: error.message,
        },
      })
    }
  }

  static workspaceChanged() {
    return vscode.workspace.onDidChangeWorkspaceFolders(async () => {
      if (!LocAgent.initalized()) {
        return
      }

      const folders = vscode.workspace.workspaceFolders
      if (Array.isArray(folders) && folders.length) {
        const repo = folders[0].uri.fsPath
        if (repo !== LocAgent.repo) {
          LocAgent.repo = repo
          await LocAgent.dispose()
          LocAgent.localeServer()
          await LocAgent.waitForServer({ port: LocAgent.port })
          await LocAgent.connectServer()
        } else {
          throw new Error(`当前Workspace为空`)
        }
      }
    });
  }

  static async connectServer() {
    try {
      const servers = LocAgent.mcpHub.getServers()
      if (servers.some(server => server.name === LocAgent.serverName)) {
        await LocAgent.mcpHub.restartConnection(LocAgent.serverName)
      }
    } catch (error) {
      logger.error(`LocAgent服务连接失败: ${error}`)
    }
  }

  static async dispose() {
    try {
      if (LocAgent.pid) {
        LocAgent.killProcess(LocAgent.pid)
        LocAgent.outputChannel.appendLine(`[LocAgent]本地服务进程已停止 PID: ${LocAgent.pid}`)
      } else if (await LocAgent.isPortTaken(LocAgent.port)) {
        const pid = LocAgent.getPidByPort(LocAgent.port)
        pid && LocAgent.killProcess(pid)
        LocAgent.outputChannel.appendLine(`[LocAgent]本地服务进程已停止 PID: ${pid}`)
      }
    } catch (error) {
      logger.error(`LocAgent本地服务停止失败: ${error}`)
      LocAgent.outputChannel.appendLine(`[LocAgent]本地服务进程停止失败: ${error}`)
    } finally {
      LocAgent.pid = undefined
    }
  }

  private static localeServer() {
    const { exec } = childProcess
    const cmd = `sh ./run_mcp.sh --repo ${LocAgent.repo} --port ${LocAgent.port} --disable_vector_retrieve`
    LocAgent.outputChannel?.appendLine(`[LocAgent]本地服务进程启动中...`)
    const child = exec(cmd, { cwd: LocAgent.cwd }, (error, stdout, stderr) => {
      if (error) {
        logger.error(`run_mcp.sh执行失败:${error}`)
        LocAgent.outputChannel?.appendLine(`[LocAgent]本地服务进程启动失败: ${error}`)
        if (LocAgent.pid) {
          LocAgent.dispose()
        }
        throw error
      }
      if (stderr) {
        logger.error(`stderr: ${stderr}`)
        LocAgent.outputChannel?.appendLine(`[LocAgent]本地服务进程 stderr: ${stderr}`)
      }
      logger.info(`stdout: ${stdout}`)
      LocAgent.outputChannel?.appendLine(`[LocAgent]本地服务进程 stdout: ${stdout}`)
    });
    const { pid } = child
    if (LocAgent.pid) {
      LocAgent.dispose()
    }
    LocAgent.pid = pid
  }

  private static waitForServer(serverOptions: {
    port: number
    timeout?: number
  }): Promise<void> {
    const { port, timeout, host } = Object.assign({ host: LocAgent.host, timeout: 600_000 }, serverOptions)
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => reject(new Error('端口超时')), timeout);
      const tryConnect = () => {
        const sock = net.connect(port, host);
        sock.on('connect', () => {
          clearTimeout(timer);
          sock.destroy();
          resolve();
        });
        sock.on('error', () => setTimeout(tryConnect, 3000));
      };
      tryConnect();
    });
  }

  private static async isPortTaken(port: number): Promise<boolean> {
    const host = LocAgent.host
    return new Promise((resolve) => {
      const socket = net.createConnection({ host, port }, () => {
        socket.destroy()
        resolve(true)
      });
      socket.on('error', () => resolve(false))
    });
  }

  private static getPidByPort(port: number): number | null {
    const { execSync } = childProcess
    try {
      if (os.platform() === 'win32') {
        // Windows: netstat -ano
        const out = execSync(`netstat -ano -p tcp | findStr ":${port} "`, { encoding: 'utf8' })
        const line = out.split('\n')[0].trim()
        return line ? Number(line.split(/\s+/).pop()) : null
      } else {
        // Linux / macOS: lsof -ti
        const out = execSync(`lsof -ti:${port}`, { encoding: 'utf8' })
        return Number(out.trim())
      }
    } catch (e) {
      return null
    }
  }

  private static killProcess(pid: number) {
    const { execSync } = childProcess
    try {
      if (os.platform() === 'win32') {
        execSync(`taskkill /PID ${pid} /F`)
      } else {
        process.kill(pid, 'SIGTERM')
      }
      logger.info(`✅ 已终止 PID ${pid}`);
    } catch (error) {
      logger.error(`❌ 终止 PID ${pid} 失败: ${error}`);
    }
  }

  private static initalized(): boolean {
    return (!!LocAgent.controller && !!LocAgent.mcpHub)
  }
}