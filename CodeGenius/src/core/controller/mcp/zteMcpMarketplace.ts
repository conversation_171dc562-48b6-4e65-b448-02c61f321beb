import * as vscode from "vscode"
import * as fs from "fs"
import * as path from "path"
import * as childProcess from "child_process"
import { ZeroLogger } from "@/utils/logger"
import SftpClient from "ssh2-sftp-client"
import { McpMarketplaceItem } from "@/shared/mcp"
import { McpHub } from "@/services/mcp/McpHub"
import { LocAgent } from "./locAgent"
import type { Controller } from "../index"

const logger = new ZeroLogger("ZteMcpMarketplace")
const remoteMcpCatalogPath = "/zeroCode/mcpTools/mcpDescriptions"
const remoteMcpPackagesPath = "/zeroCode/mcpTools/mcpPackages"
const mcpCatalogFileName = "mcpMarketplaceCatalog.json"
export class ZteMcpMarketplace {
  private sftpConfig = {
    host: "************",
    port: 22,
    username: "zeroagent",
    password: Buffer.from("REZTQHVzczEwMA==", 'base64').toString('utf-8'),
    retries: 1,
    timeout: 1000
  }
  private sftp: SftpClient | null = null
  private localePath: string
  private mcpMarketplaceList: McpMarketplaceItem[] = []

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly mcpHub: McpHub,
    private readonly controller: Controller,
    private readonly outputChannel: vscode.OutputChannel
  ) {
    const globalStorageUrl = this.context.globalStorageUri?.fsPath
    this.localePath = globalStorageUrl ? path.join(globalStorageUrl, "mcpPackages") : ""
  }

  async downloadMarketplaceFromFtp(): Promise<McpMarketplaceItem[]> {
    !this.sftp && (this.sftp = await this.createFtp())

    if (!this.sftp) {
      return this.mcpMarketplaceList
    }

    if (!this.localePath) {
      logger.error(`本地缓存目录不存在`)
      return this.mcpMarketplaceList
    }

    const remote = `${remoteMcpCatalogPath}/${mcpCatalogFileName}`
    const locale = path.join(this.localePath, mcpCatalogFileName)

    try {
      if (!fs.existsSync(this.localePath)) {
        fs.mkdirSync(this.localePath, { recursive: true })
      }
      await this.sftp.fastGet(
        remote,
        locale
      )
      const content = fs.readFileSync(locale, "utf-8")?.trim()
      if (!content) {
        throw new Error("MCP目录文件为空")
      }
      let items = JSON.parse(content)
      if (Array.isArray(items) && items.length) {
        items = items.map((item: any) => ({
          ...item,
          githubStars: item.githubStars ?? 0,
          downloadCount: item.downloadCount ?? 0,
          tags: (item.tags ?? []).filter((tag: string) => tag !== item.category),
          employeeZeroOffical: true
        }))
        this.mcpMarketplaceList = []
        this.mcpMarketplaceList.push(...items)
      }
      logger.info(`成功读取到MCP目录`)
    } catch (error) {
      logger.error(`下载MCP目录文件失败: ${error}`)
    } finally {
      return this.mcpMarketplaceList
    }
  }

  async installMcp(mcp: McpMarketplaceItem): Promise<void> {
    !this.sftp && (this.sftp = await this.createFtp())

    try {
      if (!this.sftp) {
        throw new Error("连接到SFTP服务器失败")
      }
      const { mcpId, installCmd, mcpSettingConfig ,githubUrl } = mcp
 
      const remote = `${remoteMcpPackagesPath}/${githubUrl}`
      const locale = path.join(this.localePath, githubUrl)

      if (githubUrl) {
        await this.sftp.fastGet(
          remote,
          locale
        )
      }
      if (!fs.existsSync(locale)) {
        throw new Error("MCP插件未下载到本地")
      }
      if (installCmd) {
        await this.execSync(this.localePath, installCmd)
      }
      if (mcpId === "LocAgent" && githubUrl) {
        await LocAgent.install({ mcpHub: this.mcpHub, controller: this.controller, localePath: this.localePath, outputChannel: this.outputChannel })
        return Promise.resolve()
      }
      const settingPath = path.join(this.localePath, mcpSettingConfig?? "")
      if (fs.existsSync(settingPath)) {
        const setting = JSON.parse(fs.readFileSync(settingPath, "utf-8"))
        const mcpSettingsFilePath = await this.mcpHub.getMcpSettingsFilePath()
        if (mcpSettingsFilePath) {
          let mcpSettings = JSON.parse(fs.readFileSync(mcpSettingsFilePath, "utf-8"))
          if (mcpSettings.mcpServers) {
            mcpSettings.mcpServers = Object.assign(mcpSettings.mcpServers, setting)
          } else {
            mcpSettings = { mcpServers: setting }
          }
          fs.writeFileSync(mcpSettingsFilePath, JSON.stringify(mcpSettings, null, "\t"), "utf-8")
          await this.mcpHub.mcpSettingsFileChanged()
          return Promise.resolve()
        }
      }
    } catch (error) {
      logger.error(`安装MCP插件失败: ${error}`)
      return Promise.reject(error)
    }

  }

  getMcpInfo(mcpId: string): McpMarketplaceItem | null {
    return this.mcpMarketplaceList.find(mcp => mcp.mcpId === mcpId) ?? null
  }

  private async createFtp(): Promise<SftpClient | null> {
    const sftp = new SftpClient();
    try {
      logger.info(`尝试连接到SFTP服务器`)
      await sftp.connect(this.sftpConfig)
      logger.info(`成功连接到SFTP服务器`)
      return sftp
    } catch (error) {
      logger.error(`连接到SFTP服务器失败`)
      return null;
    }
  }

  private execSync(cwd: string, cmd: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const output = childProcess.execSync(
          cmd,
          {
            cwd,
            encoding: "utf-8",
          }
        )
        resolve(output.trim())
      } catch (error) {
        logger.error(`安装脚本执行失败: ${error}`)
        reject(error)
      }
    })
  }
}