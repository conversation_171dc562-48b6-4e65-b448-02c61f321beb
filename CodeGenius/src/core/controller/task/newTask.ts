import { Controller } from ".."
import { Empty } from "../../../shared/proto/common"
import { NewTaskRequest } from "../../../shared/proto/task"
import * as fs from "fs"

/**
 * Creates a new task with the given text and optional images
 * @param controller The controller instance
 * @param request The new task request containing text and optional images
 * @returns Empty response
 */
export async function newTask(controller: Controller, request: NewTaskRequest): Promise<Empty> {
	if (fs.existsSync(`${controller.context.globalStorageUri.fsPath}/settings/needAutoPlanAct`)) {
		const { chatSettings } = await controller.getStateToPostToWebview()
		if (chatSettings.mode === "act") {
			// Switch to Act mode if currently in Plan mode
		//	await controller.togglePlanActModeWithChatSettings({ mode: "plan" })
		}

	}
	await controller.initTask(request.text, request.images)
	return Empty.create()
}
