import * as vscode from "vscode"
import { DEFAULT_CHAT_SETTINGS } from "@shared/ChatSettings"
import { DEFAULT_BROWSER_SETTINGS } from "@shared/BrowserSettings"
import { DEFAULT_AUTO_APPROVAL_SETTINGS } from "@shared/AutoApprovalSettings"
import { GlobalStateKey, <PERSON>Key } from "./state-keys"
import { ApiConfiguration, ApiProvider, ModelInfo } from "@shared/api"
import { HistoryItem } from "@shared/HistoryItem"
import { AutoApprovalSettings } from "@shared/AutoApprovalSettings"
import { BrowserSettings } from "@shared/BrowserSettings"
import { ChatSettings } from "@shared/ChatSettings"
import { TelemetrySetting } from "@shared/TelemetrySetting"
import { UserInfo } from "@shared/UserInfo"
import { ClineRulesToggles } from "@shared/cline-rules"
/*
	Storage
	https://dev.to/kompotkot/how-to-use-secretstorage-in-your-vscode-extensions-2hco
	https://www.eliostruyf.com/devhack-code-extension-storage-options/
	*/

// global

export async function updateGlobalState(context: vscode.ExtensionContext, key: GlobalStateKey, value: any) {
	await context.globalState.update(key, value)
}

export async function getGlobalState(context: vscode.ExtensionContext, key: GlobalStateKey) {
	let value = await context.globalState.get(key)
	if (value === undefined && key === "qwenModelApiUrl") {
		return "http://************:30804/qwen3-235b-fp8-openai-server/v1/v1"
	} else if (value === undefined && key === "qwenModelId") {
		return "/Qwen2-235B-A22B-FP8"
	} else if (value === undefined && key === "nebulaModelApiUrl") {
		return "http://nebulacoder.dev.zte.com.cn:40081/v1"
	} else if (value === undefined && key === "nebulaModelId") {
		return "nebulacoder-lite-v7.0"
	} else if (value === undefined && key === "nebulaReasonModelApiUrl") {
		return "http://nebulacoder.dev.zte.com.cn:40081/v1"
	} else if (value === undefined && key === "nebulaReasonModelId") {
		return "nebulacoder-lite-cot-v7.0"
	} else if (value === undefined && key === "apiProvider") {
		return "nebula"
	}
	return value
}

// secrets

export async function storeSecret(context: vscode.ExtensionContext, key: SecretKey, value?: string) {
	if (value) {
		await context.secrets.store(key, value)
	} else {
		await context.secrets.delete(key)
	}
}

export async function getSecret(context: vscode.ExtensionContext, key: SecretKey) {
	let value = await context.secrets.get(key)
	if (value === undefined && key === 'qwenModelApiKey') {
		return "xxxx"
	} else if (value === undefined && key === 'nebulaModelApiKey') {
		return "xxxx"
	} else if (value === undefined && key === 'nebulaReasonModelApiKey') {
		return "EMPTY"
	}
	return value
}

// workspace

export async function updateWorkspaceState(context: vscode.ExtensionContext, key: string, value: any) {
	await context.workspaceState.update(key, value)
}

export async function getWorkspaceState(context: vscode.ExtensionContext, key: string) {
	return await context.workspaceState.get(key)
}

export async function getAllExtensionState(context: vscode.ExtensionContext) {
	const [
		storedApiProvider,
		apiModelId,
		apiKey,
		openRouterApiKey,
		clineApiKey,
		awsAccessKey,
		awsSecretKey,
		awsSessionToken,
		awsRegion,
		awsUseCrossRegionInference,
		awsBedrockUsePromptCache,
		awsBedrockEndpoint,
		awsProfile,
		awsUseProfile,
		vertexProjectId,
		vertexRegion,
		openAiBaseUrl,
		openAiApiKey,
		openAiModelId,
		openAiModelInfo,
		openAiHeaders,
		ollamaModelId,
		ollamaBaseUrl,
		ollamaApiOptionsCtxNum,
		lmStudioModelId,
		lmStudioBaseUrl,
		anthropicBaseUrl,
		geminiApiKey,
		geminiBaseUrl,
		openAiNativeApiKey,
		deepSeekApiKey,
		requestyApiKey,
		requestyModelId,
		requestyModelInfo,
		togetherApiKey,
		togetherModelId,
		qwenApiKey,
		doubaoApiKey,
		mistralApiKey,
		azureApiVersion,
		openRouterModelId,
		openRouterModelInfo,
		openRouterProviderSorting,
		lastShownAnnouncementId,
		customInstructions,
		taskHistory,
		autoApprovalSettings,
		browserSettings,
		chatSettings,
		vsCodeLmModelSelector,
		liteLlmBaseUrl,
		liteLlmModelId,
		liteLlmUsePromptCache,
		userInfo,
		previousModeApiProvider,
		previousModeModelId,
		previousModeModelInfo,
		previousModeVsCodeLmModelSelector,
		previousModeThinkingBudgetTokens,
		previousModeReasoningEffort,
		qwenApiLine,
		liteLlmApiKey,
		telemetrySetting,
		asksageApiKey,
		asksageApiUrl,
		xaiApiKey,
		thinkingBudgetTokens,
		reasoningEffort,
		sambanovaApiKey,
		planActSeparateModelsSettingRaw,
		favoritedModelIds,
		globalClineRulesToggles,
		qwenModelApiKey,
		qwenModelApiUrl,
		qwenModelId,
		nebulaModelApiKey,
		nebulaModelApiUrl,
		nebulaModelId,
		nebulaReasonModelApiKey,
		nebulaReasonModelApiUrl,
		nebulaReasonModelId,
		enableCodebaseIndexing,
		qwenModelTokenLength,
		nebulaModelTokenLength,
		nebulaReasonModelTokenLength

	] = await Promise.all([
		getGlobalState(context, "apiProvider") as Promise<ApiProvider | undefined>,
		getGlobalState(context, "apiModelId") as Promise<string | undefined>,
		getSecret(context, "apiKey") as Promise<string | undefined>,
		getSecret(context, "openRouterApiKey") as Promise<string | undefined>,
		getSecret(context, "clineApiKey") as Promise<string | undefined>,
		getSecret(context, "awsAccessKey") as Promise<string | undefined>,
		getSecret(context, "awsSecretKey") as Promise<string | undefined>,
		getSecret(context, "awsSessionToken") as Promise<string | undefined>,
		getGlobalState(context, "awsRegion") as Promise<string | undefined>,
		getGlobalState(context, "awsUseCrossRegionInference") as Promise<boolean | undefined>,
		getGlobalState(context, "awsBedrockUsePromptCache") as Promise<boolean | undefined>,
		getGlobalState(context, "awsBedrockEndpoint") as Promise<string | undefined>,
		getGlobalState(context, "awsProfile") as Promise<string | undefined>,
		getGlobalState(context, "awsUseProfile") as Promise<boolean | undefined>,
		getGlobalState(context, "vertexProjectId") as Promise<string | undefined>,
		getGlobalState(context, "vertexRegion") as Promise<string | undefined>,
		getGlobalState(context, "openAiBaseUrl") as Promise<string | undefined>,
		getSecret(context, "openAiApiKey") as Promise<string | undefined>,
		getGlobalState(context, "openAiModelId") as Promise<string | undefined>,
		getGlobalState(context, "openAiModelInfo") as Promise<ModelInfo | undefined>,
		getGlobalState(context, "openAiHeaders") as Promise<Record<string, string> | undefined>,
		getGlobalState(context, "ollamaModelId") as Promise<string | undefined>,
		getGlobalState(context, "ollamaBaseUrl") as Promise<string | undefined>,
		getGlobalState(context, "ollamaApiOptionsCtxNum") as Promise<string | undefined>,
		getGlobalState(context, "lmStudioModelId") as Promise<string | undefined>,
		getGlobalState(context, "lmStudioBaseUrl") as Promise<string | undefined>,
		getGlobalState(context, "anthropicBaseUrl") as Promise<string | undefined>,
		getSecret(context, "geminiApiKey") as Promise<string | undefined>,
		getGlobalState(context, "geminiBaseUrl") as Promise<string | undefined>,
		getSecret(context, "openAiNativeApiKey") as Promise<string | undefined>,
		getSecret(context, "deepSeekApiKey") as Promise<string | undefined>,
		getSecret(context, "requestyApiKey") as Promise<string | undefined>,
		getGlobalState(context, "requestyModelId") as Promise<string | undefined>,
		getGlobalState(context, "requestyModelInfo") as Promise<ModelInfo | undefined>,
		getSecret(context, "togetherApiKey") as Promise<string | undefined>,
		getGlobalState(context, "togetherModelId") as Promise<string | undefined>,
		getSecret(context, "qwenApiKey") as Promise<string | undefined>,
		getSecret(context, "doubaoApiKey") as Promise<string | undefined>,
		getSecret(context, "mistralApiKey") as Promise<string | undefined>,
		getGlobalState(context, "azureApiVersion") as Promise<string | undefined>,
		getGlobalState(context, "openRouterModelId") as Promise<string | undefined>,
		getGlobalState(context, "openRouterModelInfo") as Promise<ModelInfo | undefined>,
		getGlobalState(context, "openRouterProviderSorting") as Promise<string | undefined>,
		getGlobalState(context, "lastShownAnnouncementId") as Promise<string | undefined>,
		getGlobalState(context, "customInstructions") as Promise<string | undefined>,
		getGlobalState(context, "taskHistory") as Promise<HistoryItem[] | undefined>,
		getGlobalState(context, "autoApprovalSettings") as Promise<AutoApprovalSettings | undefined>,
		getGlobalState(context, "browserSettings") as Promise<BrowserSettings | undefined>,
		getGlobalState(context, "chatSettings") as Promise<ChatSettings | undefined>,
		getGlobalState(context, "vsCodeLmModelSelector") as Promise<vscode.LanguageModelChatSelector | undefined>,
		getGlobalState(context, "liteLlmBaseUrl") as Promise<string | undefined>,
		getGlobalState(context, "liteLlmModelId") as Promise<string | undefined>,
		getGlobalState(context, "liteLlmUsePromptCache") as Promise<boolean | undefined>,
		getGlobalState(context, "userInfo") as Promise<UserInfo | undefined>,
		getGlobalState(context, "previousModeApiProvider") as Promise<ApiProvider | undefined>,
		getGlobalState(context, "previousModeModelId") as Promise<string | undefined>,
		getGlobalState(context, "previousModeModelInfo") as Promise<ModelInfo | undefined>,
		getGlobalState(context, "previousModeVsCodeLmModelSelector") as Promise<vscode.LanguageModelChatSelector | undefined>,
		getGlobalState(context, "previousModeThinkingBudgetTokens") as Promise<number | undefined>,
		getGlobalState(context, "previousModeReasoningEffort") as Promise<string | undefined>,
		getGlobalState(context, "qwenApiLine") as Promise<string | undefined>,
		getSecret(context, "liteLlmApiKey") as Promise<string | undefined>,
		getGlobalState(context, "telemetrySetting") as Promise<TelemetrySetting | undefined>,
		getSecret(context, "asksageApiKey") as Promise<string | undefined>,
		getGlobalState(context, "asksageApiUrl") as Promise<string | undefined>,
		getSecret(context, "xaiApiKey") as Promise<string | undefined>,
		getGlobalState(context, "thinkingBudgetTokens") as Promise<number | undefined>,
		getGlobalState(context, "reasoningEffort") as Promise<string | undefined>,
		getSecret(context, "sambanovaApiKey") as Promise<string | undefined>,
		getGlobalState(context, "planActSeparateModelsSetting") as Promise<boolean | undefined>,
		getGlobalState(context, "favoritedModelIds") as Promise<string[] | undefined>,
		getGlobalState(context, "globalClineRulesToggles") as Promise<ClineRulesToggles | undefined>,

		getSecret(context, "qwenModelApiKey") as Promise<string | undefined>,
		getGlobalState(context, "qwenModelApiUrl") as Promise<string | undefined>,
		getGlobalState(context, "qwenModelId") as Promise<string | undefined>,
		getSecret(context, "nebulaModelApiKey") as Promise<string | undefined>,
		getGlobalState(context, "nebulaModelApiUrl") as Promise<string | undefined>,
		getGlobalState(context, "nebulaModelId") as Promise<string | undefined>,
		getSecret(context, "nebulaReasonModelApiKey") as Promise<string | undefined>,
		getGlobalState(context, "nebulaReasonModelApiUrl") as Promise<string | undefined>,
		getGlobalState(context, "nebulaReasonModelId") as Promise<string | undefined>,
		getGlobalState(context, "enableCodebaseIndexing") as Promise<boolean | undefined>,
		getGlobalState(context, "qwenModelTokenLength") as Promise<number | undefined>,
		getGlobalState(context, "nebulaModelTokenLength") as Promise<number | undefined>,
		getGlobalState(context, "nebulaReasonModelTokenLength") as Promise<number | undefined>
	])

	let apiProvider: ApiProvider
	if (storedApiProvider) {
		apiProvider = storedApiProvider
	} else {
		// Either new user or legacy user that doesn't have the apiProvider stored in state
		// (If they're using OpenRouter or Bedrock, then apiProvider state will exist)
		if (apiKey) {
			apiProvider = "anthropic"
		} else {
			// New users should default to openrouter, since they've opted to use an API key instead of signing in
			apiProvider = "openrouter"
		}
	}

	const localClineRulesToggles = (await getWorkspaceState(context, "localClineRulesToggles")) as ClineRulesToggles

	const o3MiniReasoningEffort = vscode.workspace.getConfiguration("employeeZero.modelSettings.o3Mini").get("reasoningEffort", "medium")

	const mcpMarketplaceEnabled = vscode.workspace.getConfiguration("employeeZero").get<boolean>("mcpMarketplace.enabled", true)

	// Plan/Act separate models setting is a boolean indicating whether the user wants to use different models for plan and act. Existing users expect this to be enabled, while we want new users to opt in to this being disabled by default.
	// On win11 state sometimes initializes as empty string instead of undefined
	let planActSeparateModelsSetting: boolean | undefined = undefined
	if (planActSeparateModelsSettingRaw === true || planActSeparateModelsSettingRaw === false) {
		planActSeparateModelsSetting = planActSeparateModelsSettingRaw
	} else {
		// default to true for existing users
		if (storedApiProvider) {
			planActSeparateModelsSetting = true
		} else {
			// default to false for new users
			planActSeparateModelsSetting = false
		}
		// this is a special case where it's a new state, but we want it to default to different values for existing and new users.
		// persist so next time state is retrieved it's set to the correct value.
		await updateGlobalState(context, "planActSeparateModelsSetting", planActSeparateModelsSetting)
	}

	return {
		apiConfiguration: {
			apiProvider,
			apiModelId,
			apiKey,
			openRouterApiKey,
			clineApiKey,
			awsAccessKey,
			awsSecretKey,
			awsSessionToken,
			awsRegion,
			awsUseCrossRegionInference,
			awsBedrockUsePromptCache,
			awsBedrockEndpoint,
			awsProfile,
			awsUseProfile,
			vertexProjectId,
			vertexRegion,
			openAiBaseUrl,
			openAiApiKey,
			openAiModelId,
			openAiModelInfo,
			openAiHeaders: openAiHeaders || {},
			ollamaModelId,
			ollamaBaseUrl,
			ollamaApiOptionsCtxNum,
			lmStudioModelId,
			lmStudioBaseUrl,
			anthropicBaseUrl,
			geminiApiKey,
			geminiBaseUrl,
			openAiNativeApiKey,
			deepSeekApiKey,
			requestyApiKey,
			requestyModelId,
			requestyModelInfo,
			togetherApiKey,
			togetherModelId,
			qwenApiKey,
			qwenApiLine,
			doubaoApiKey,
			mistralApiKey,
			azureApiVersion,
			openRouterModelId,
			openRouterModelInfo,
			openRouterProviderSorting,
			vsCodeLmModelSelector,
			o3MiniReasoningEffort,
			thinkingBudgetTokens,
			reasoningEffort,
			liteLlmBaseUrl,
			liteLlmModelId,
			liteLlmApiKey,
			liteLlmUsePromptCache,
			asksageApiKey,
			asksageApiUrl,
			xaiApiKey,
			sambanovaApiKey,
			favoritedModelIds,
			storedApiProvider,
			lastShownAnnouncementId,
			customInstructions,
			taskHistory,
			autoApprovalSettings,
			qwenModelApiKey,
			qwenModelApiUrl,
			qwenModelId,
			nebulaModelApiKey,
			nebulaModelApiUrl,
			nebulaModelId,
			nebulaReasonModelApiKey,
			nebulaReasonModelApiUrl,
			nebulaReasonModelId,
			chatSettings,
			previousModeApiProvider,
			previousModeModelId,
			previousModeModelInfo,
		},
		lastShownAnnouncementId,
		customInstructions,
		taskHistory,
		autoApprovalSettings: autoApprovalSettings || DEFAULT_AUTO_APPROVAL_SETTINGS, // default value can be 0 or empty string
		globalClineRulesToggles: globalClineRulesToggles || {},
		localClineRulesToggles: localClineRulesToggles || {},
		browserSettings: { ...DEFAULT_BROWSER_SETTINGS, ...browserSettings }, // this will ensure that older versions of browserSettings (e.g. before remoteBrowserEnabled was added) are merged with the default values (false for remoteBrowserEnabled)
		chatSettings: chatSettings || DEFAULT_CHAT_SETTINGS,
		userInfo,
		previousModeApiProvider,
		previousModeModelId,
		previousModeModelInfo,
		previousModeVsCodeLmModelSelector,
		previousModeThinkingBudgetTokens,
		previousModeReasoningEffort,
		mcpMarketplaceEnabled,
		telemetrySetting: telemetrySetting || "unset",
		planActSeparateModelsSetting,
		enableCodebaseIndexing
	}
}

export async function updateApiConfiguration(context: vscode.ExtensionContext, apiConfiguration: ApiConfiguration) {
	const {
		apiProvider,
		apiModelId,
		apiKey,
		openRouterApiKey,
		awsAccessKey,
		awsSecretKey,
		awsSessionToken,
		awsRegion,
		awsUseCrossRegionInference,
		awsBedrockUsePromptCache,
		awsBedrockEndpoint,
		awsProfile,
		awsUseProfile,
		vertexProjectId,
		vertexRegion,
		openAiBaseUrl,
		openAiApiKey,
		openAiModelId,
		openAiModelInfo,
		openAiHeaders,
		ollamaModelId,
		ollamaBaseUrl,
		ollamaApiOptionsCtxNum,
		lmStudioModelId,
		lmStudioBaseUrl,
		anthropicBaseUrl,
		geminiApiKey,
		geminiBaseUrl,
		openAiNativeApiKey,
		deepSeekApiKey,
		requestyApiKey,
		requestyModelId,
		requestyModelInfo,
		togetherApiKey,
		togetherModelId,
		qwenApiKey,
		doubaoApiKey,
		mistralApiKey,
		azureApiVersion,
		openRouterModelId,
		openRouterModelInfo,
		openRouterProviderSorting,
		vsCodeLmModelSelector,
		liteLlmBaseUrl,
		liteLlmModelId,
		liteLlmApiKey,
		liteLlmUsePromptCache,
		qwenApiLine,
		asksageApiKey,
		asksageApiUrl,
		xaiApiKey,
		thinkingBudgetTokens,
		reasoningEffort,
		clineApiKey,
		sambanovaApiKey,
		favoritedModelIds,
		qwenModelApiKey,
		qwenModelApiUrl,
		qwenModelId,
		nebulaModelApiKey,
		nebulaModelApiUrl,
		nebulaModelId,
		nebulaReasonModelApiKey,
		nebulaReasonModelApiUrl,
		nebulaReasonModelId,
	} = apiConfiguration
	await updateGlobalState(context, "apiProvider", apiProvider)
	await updateGlobalState(context, "apiModelId", apiModelId?.trim())
	await storeSecret(context, "apiKey", apiKey?.trim())

	// await storeSecret(context, "openRouterApiKey", openRouterApiKey)
	// await storeSecret(context, "awsAccessKey", awsAccessKey)
	// await storeSecret(context, "awsSecretKey", awsSecretKey)
	// await storeSecret(context, "awsSessionToken", awsSessionToken)
	// await updateGlobalState(context, "awsRegion", awsRegion)
	// await updateGlobalState(context, "awsUseCrossRegionInference", awsUseCrossRegionInference)
	// await updateGlobalState(context, "awsBedrockUsePromptCache", awsBedrockUsePromptCache)
	// await updateGlobalState(context, "awsBedrockEndpoint", awsBedrockEndpoint)
	// await updateGlobalState(context, "awsProfile", awsProfile)
	// await updateGlobalState(context, "awsUseProfile", awsUseProfile)
	// await updateGlobalState(context, "vertexProjectId", vertexProjectId)
	// await updateGlobalState(context, "vertexRegion", vertexRegion)
	 await updateGlobalState(context, "openAiBaseUrl", openAiBaseUrl?.trim())

	await storeSecret(context, "openAiApiKey", openAiApiKey?.trim())
	await updateGlobalState(context, "openAiModelId", openAiModelId?.trim())
	await updateGlobalState(context, "openAiModelInfo", openAiModelInfo)
	await updateGlobalState(context, "openAiHeaders", openAiHeaders || {})
	await storeSecret(context, "deepSeekApiKey", deepSeekApiKey?.trim())

	// await updateGlobalState(context, "ollamaModelId", ollamaModelId)
	// await updateGlobalState(context, "ollamaBaseUrl", ollamaBaseUrl)
	// await updateGlobalState(context, "ollamaApiOptionsCtxNum", ollamaApiOptionsCtxNum)
	// await updateGlobalState(context, "lmStudioModelId", lmStudioModelId)
	// await updateGlobalState(context, "lmStudioBaseUrl", lmStudioBaseUrl)
	// await updateGlobalState(context, "anthropicBaseUrl", anthropicBaseUrl)
	// await storeSecret(context, "geminiApiKey", geminiApiKey)
	// await updateGlobalState(context, "geminiBaseUrl", geminiBaseUrl)

	// await storeSecret(context, "openAiNativeApiKey", openAiNativeApiKey)
	// await storeSecret(context, "requestyApiKey", requestyApiKey)
	// await storeSecret(context, "togetherApiKey", togetherApiKey)
	// await storeSecret(context, "qwenApiKey", qwenApiKey)
	// await storeSecret(context, "doubaoApiKey", doubaoApiKey)
	// await storeSecret(context, "mistralApiKey", mistralApiKey)
	// await storeSecret(context, "liteLlmApiKey", liteLlmApiKey)
	// await storeSecret(context, "xaiApiKey", xaiApiKey)
	// await updateGlobalState(context, "azureApiVersion", azureApiVersion)
	// await updateGlobalState(context, "openRouterModelId", openRouterModelId)
	// await updateGlobalState(context, "openRouterModelInfo", openRouterModelInfo)
	// await updateGlobalState(context, "openRouterProviderSorting", openRouterProviderSorting)
	// await updateGlobalState(context, "vsCodeLmModelSelector", vsCodeLmModelSelector)
	// await updateGlobalState(context, "liteLlmBaseUrl", liteLlmBaseUrl)
	// await updateGlobalState(context, "liteLlmModelId", liteLlmModelId)
	// await updateGlobalState(context, "liteLlmUsePromptCache", liteLlmUsePromptCache)
	// await updateGlobalState(context, "qwenApiLine", qwenApiLine)
	// await updateGlobalState(context, "requestyModelId", requestyModelId)
	// await updateGlobalState(context, "requestyModelInfo", requestyModelInfo)
	// await updateGlobalState(context, "togetherModelId", togetherModelId)
	// await storeSecret(context, "asksageApiKey", asksageApiKey)
	// await updateGlobalState(context, "asksageApiUrl", asksageApiUrl)
	// await updateGlobalState(context, "thinkingBudgetTokens", thinkingBudgetTokens)
	// await updateGlobalState(context, "reasoningEffort", reasoningEffort)
	// await storeSecret(context, "clineApiKey", clineApiKey)
	// await storeSecret(context, "sambanovaApiKey", sambanovaApiKey)
	// await updateGlobalState(context, "favoritedModelIds", favoritedModelIds)

	await storeSecret(context, "qwenModelApiKey", qwenModelApiKey?.trim() || "XXXX")
	await updateGlobalState(context, "qwenModelApiUrl", qwenModelApiUrl?.trim() || "http://************:30804/qwen3-235b-fp8-openai-server/v1/v1")
	await updateGlobalState(context, "qwenModelId", qwenModelId?.trim() || "/Qwen2-235B-A22B-FP8")
	await storeSecret(context, "nebulaModelApiKey", nebulaModelApiKey?.trim() || "XXXX")
	await updateGlobalState(context, "nebulaModelApiUrl", nebulaModelApiUrl?.trim() || "http://nebulacoder.dev.zte.com.cn:40081/v1")
	await updateGlobalState(context, "nebulaModelId", nebulaModelId?.trim() || "nebulacoder-lite-v7.0")
	await storeSecret(context, "nebulaReasonModelApiKey", nebulaReasonModelApiKey?.trim() || "EMPTY")
	await updateGlobalState(context, "nebulaReasonModelApiUrl", nebulaReasonModelApiUrl?.trim() || "http://nebulacoder.dev.zte.com.cn:40081/v1")
	await updateGlobalState(context, "nebulaReasonModelId", nebulaReasonModelId?.trim() || "nebulacoder-lite-cot-v7.0")
}

export async function resetExtensionState(context: vscode.ExtensionContext) {
	for (const key of context.globalState.keys()) {
		await context.globalState.update(key, undefined)
	}
	const secretKeys: SecretKey[] = [
		"apiKey",
		"openRouterApiKey",
		"awsAccessKey",
		"awsSecretKey",
		"awsSessionToken",
		"openAiApiKey",
		"geminiApiKey",
		"openAiNativeApiKey",
		"deepSeekApiKey",
		"requestyApiKey",
		"togetherApiKey",
		"qwenApiKey",
		"doubaoApiKey",
		"mistralApiKey",
		"clineApiKey",
		"liteLlmApiKey",
		"asksageApiKey",
		"xaiApiKey",
		"sambanovaApiKey",
		"qwenModelApiKey",
		"nebulaModelApiKey",
		"nebulaReasonModelApiKey"
	]
	for (const key of secretKeys) {
		await storeSecret(context, key, undefined)
	}
}
