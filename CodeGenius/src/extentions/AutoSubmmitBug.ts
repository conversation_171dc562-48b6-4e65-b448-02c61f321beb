import { version } from '../../package.json'; 
import axios from 'axios'
import open from 'open'
import { getGitUsername } from "./gitUtils";
import * as vscode from 'vscode'
import { WebviewProvider } from "@core/webview"
import { Task } from "@core/task"
//import { Task } from '../../evals/cli/src/adapters/types';
import { get_model_name } from "@/metrics/pushToGateway"
import { userInfo } from 'os';
import { Controller } from "@core/controller/index"
import * as path from 'path';


export function SubmmitBugToRDC(req: any, res: any) {
	// Parse the request body
	let body = ""
	req.on("data", (chunk: any) => {
		body += chunk.toString()
	})
	req.on("end", async () => {
		try {
			let url_teamsetting = "https://rdcloud.zte.com.cn/api/rdc/zeroAgents/bdv_1046165/_apis/work/TeamSettings/currentIteration?apikey=3XprvQHluf4NPH4wTPTt3840gP9Vnymd"
			let url_bugsubmmit = "https://rdcloud.zte.com.cn/api/wic/workspaces/zeroAgents/work_items?apikey=3XprvQHluf4NPH4wTPTt3840gP9Vnymd"
			let url_bugresponsePrefix = "https://studio.zte.com.cn/uportal/rd-uportal/workspaces/zeroAgents/apps/wim/allWorkItems/"
			let url_bugresponse = ""
			
			//get version like V1.5.0
			let version_whole = `V${version}`;

			//get currentSprint
			let currentSprint = ""
			const response = await axios.get(url_teamsetting)
			if (response.status  === 200) {
				currentSprint = response.data?.bo?.name
			} else {
				console.log(` 请求失败，状态码：${response.status}`)
				res.end(JSON.stringify({ error: "currentSprint found error" }))
				return
			}
			console.log(" version:", version_whole); 
			console.log(" currentSprint:", currentSprint); 

			//buid bug info
			const bugJson = {"workItemTypeKey": "Bug", 
							"fields":[	{"key": "BelongSubSystem", "value": "zeroAgents"},
										{"key": "System_Tag", "value": "文本用例转自动化用例"}, // 标签
										{"key": "BelongProduct", "value": ""},
										{"key": "Team", "value": "光码"}, //团队
										{"key": "RemainingWork", "value": "8"},
										{"key": "OriginalEstimate", "value": "8"},
										{"key": "EffectDegree", "value": "项目内"},
										{"key": "System_WorkItemTypeKey", "value": "Bug"},
										{"key": "DefectLevel", "value": "C-一般"},
										{"key": "VersionInfo", "value": version_whole},
										{"key": "System_AreaPath","value": "zeroAgents"},
										{"key": "IsAiGeneratedCode", "value": "是"},
										{"key": "System_Title", "value": "【非故障】测试TS自动提单功能V2"},
										{"key": "System_Description_html", "value": `插件版本：${version_whole}<br>使用的模型：xxx<br>任务ID：xxx<br>问题现象描述：xxx<br>截图：xxx`},
										{"key": "System_IterationPath", "value": currentSprint}
										//{"Key":"AttachedFile", "value": "是"},
										]
							}
			//submmit bug
			try {
				const response = await axios.post(url_bugsubmmit,  bugJson, {headers: {
																	"X-Tenant-Id": "ZTE",
																	"X-Emp-No": "10186829"}})
				console.log(" 响应状态:", response.status); 
				console.log(" 响应数据:", response.data); 
				if (response.data.bo  && 'id' in response.data.bo)  {
					url_bugresponse = url_bugresponsePrefix + response.data.bo.id
					console.log(" 故障网址:", url_bugresponse)
					await open(url_bugresponse); 
				}
				else{
					console.log(" response.data中未分析到网址"); 
					res.writeHead(500, { "Content-Type": "application/json" })
					res.end(JSON.stringify({ error: "response.data中未分析到网址" }))
					return
				}
			} catch (error) {
				console.error(" 请求失败:", error.response?.data  || error.message)
				res.writeHead(500, { "Content-Type": "application/json" })
				res.end(JSON.stringify({ error: error.response?.data  || error.message }))
				return
			}

			res.writeHead(200, { "Content-Type": "application/json" });
			res.end(
				JSON.stringify({
					request_id: 1,
					bugJson:bugJson,
					web_url:url_bugresponse
				}),
			)
		}catch (error: any) {
			res.writeHead(500)
			res.end(JSON.stringify({ error: `Error Submmiting Bug` }))
			return
		}
	})
}


export async function handleRdcSubmission(
	controller: Controller,
	request: {
		title: string
		taskId: string
		ts: any
		userId: string
		model: string
		},) 
{
	try {
		if(!request.title){
			console.log(` bug title is empty`)
			throw new Error(` bug title is empty`)
		}
		if(!request.taskId){
			console.log(` bug taskId is empty`)
			throw new Error(` bug taskId is empty`)
		}
		if(!request.ts){
			console.log(` bug ts is empty`)
			throw new Error(` bug ts is empty`)
		}
		if(!request.userId){
			console.log(` bug userId is empty`)
			throw new Error(` bug userId is empty`)
		}
		
		let url_teamsetting = "https://rdcloud.zte.com.cn/api/rdc/zeroAgents/bdv_1046165/_apis/work/TeamSettings/currentIteration?apikey=3XprvQHluf4NPH4wTPTt3840gP9Vnymd"
		let url_bugsubmmit = "https://rdcloud.zte.com.cn/api/wic/workspaces/zeroAgents/work_items?apikey=3XprvQHluf4NPH4wTPTt3840gP9Vnymd"
		let url_bugresponsePrefix = "https://studio.zte.com.cn/uportal/rd-uportal/workspaces/zeroAgents/apps/wim/allWorkItems/"
		let url_bugresponse = ""


		//get ZeroAgents instance then get ModelID and workDir
		const visibleWebview = WebviewProvider.getVisibleInstance()
		if (!visibleWebview || !visibleWebview.controller) {
			console.log(` 未正常运行`)
			throw new Error(`No active ZeroAgents instance found`)
		}
		//const taskHistory = await visibleWebview.controller.getStateToPostToWebview()
		const ModelBASEURL = visibleWebview.controller.task?.getBaseUrl()
		const ModelID = visibleWebview.controller.task?.getModelID()
		const workDir = visibleWebview.context.globalStorageUri?.fsPath || '';

		//get version like V1.5.0
		let version_whole = `V${version}`;
		console.log(" version:", version_whole); 

		//get currentSprint
		let currentSprint = ""
		const response = await axios.get(url_teamsetting)
		if (response.status  === 200) {
			currentSprint = response.data?.bo?.name
		} else {
			console.log(` 请求失败，状态码：${response.status}`)
			throw new Error(`get currentSprint Failed`)
		}
		console.log(" currentSprint:", currentSprint); 

		//get Date from taskId
		const date = new Date(Number(request.taskId));
		const year = date.getFullYear();   // 2025 
		const month = String(date.getMonth()  + 1).padStart(2, '0');  // 07 
		const day = String(date.getDate()).padStart(2,  '0');         // 16 
		
		//get Time from timestamp
		const ts_time = new Date(Number(request.ts));
		const hours = String(ts_time.getHours()).padStart(2,  '0');      // 14 
		const minutes = String(ts_time.getMinutes()).padStart(2,  '0'); // 02 
		const seconds = String(ts_time.getSeconds()).padStart(2,  '0'); // 35 
		console.log(" formattedTime:", `${hours}:${minutes}:${seconds}`);

		console.log(" formattedDate:", `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`);

		//generate jsonDir and bugDescription
		let jsonDir = path.join(workDir,  'prompts', `${year}${month}${day}`, request.taskId); 
		let bugDescription = `插件版本：${version_whole}<br>使用的模型ID：${ModelID}<br>使用的模型URL：${ModelBASEURL}<br>任务ID：${request.taskId}<br>请求号：${request.ts}<br>请求时间：${hours}${minutes}${seconds}<br>附件位置（需手动上传）：${jsonDir}<br>问题现象描述：(用户手动补充内容)<br>截图：(若需要，用户手动补充提供)`
		console.log(" jsonDir:", jsonDir);
		console.log(" TaskId:", request.taskId);
		console.log(" ModelID:", ModelID);
		console.log(" ModelID_FromButton:", request.model);
		console.log(" title:", request.title);
		console.log(" ts:", request.ts);
		console.log(" Message:", bugDescription);

		//buid bug info
		const bugJson = {"workItemTypeKey": "Bug", 
						"fields":[	{"key": "BelongSubSystem", "value": "zeroAgents"},
									{"key": "System_Tag", "value": "零号员工插件按钮提单"}, // 标签
									{"key": "BelongProduct", "value": ""},
									{"key": "Team", "value": "光码"}, //团队
									{"key": "RemainingWork", "value": "8"},
									{"key": "OriginalEstimate", "value": "8"},
									{"key": "EffectDegree", "value": "项目内"},
									{"key": "System_WorkItemTypeKey", "value": "Bug"},
									{"key": "DefectLevel", "value": "C-一般"},
									{"key": "VersionInfo", "value": version_whole},
									{"key": "System_AreaPath","value": "zeroAgents"},
									{"key": "IsAiGeneratedCode", "value": "是"},
									{"key": "System_Title", "value": request.title},
									{"key": "System_Description_html", "value": bugDescription},
									{"key": "System_IterationPath", "value": currentSprint}
									//{"Key":"AttachedFile", "value": "是"},
									]
						}
		
		//submmit bug
		try {
			const headers = {
				'X-Tenant-Id': 'ZTE',
				'X-Emp-No': request.userId,
				'Content-Type': 'application/json'
			};

			const submitResponse = await axios.post(url_bugsubmmit, bugJson, { headers });
			console.log('提交响应状态:', submitResponse.status);

			if (submitResponse.data.bo && submitResponse.data.bo.id) {
				url_bugresponse = `${url_bugresponsePrefix}${submitResponse.data.bo.id}`;
				console.log('缺陷网址:', url_bugresponse);
				await open(url_bugresponse);
			} else {
				throw new Error('响应数据中未找到缺陷ID');
			}
		} catch (error) {
			console.error('提交缺陷失败:', error.response?.data || error.message);
			throw new Error(`Rdc submmit bug Failed`)
		}
		/*
		*/
		// Send the response back to the webview
		await controller.postMessageToWebview({
			type: "rdc_submmit_response",
			rdc_submmit_response: {
				request_id: request.userId,
			},
		})
	} catch (error) {
		// Send error response
		await controller.postMessageToWebview({
			type: "rdc_submmit_response", 
			rdc_submmit_response: {
				error: error instanceof Error ? error.message : String(error),
				request_id: request.userId,
			},
		})
	}
}
		