import fetch from "node-fetch"
import { ZeroLogger } from "../utils/logger";
const Logger = new ZeroLogger("DynamicConfigParamHelper.ts");

export interface DynamicConfigParam {
    qwenModelTokenLength?: number
    nebulaModelTokenLength?: number
    nebulaReasonModelTokenLength?: number
}

export async function getDynamicConfigParam(fileName: string, paramsString?: string): Promise<DynamicConfigParam> {
    const preUrlPath = "https://zdsp.zx.zte.com.cn/ai/ZeroAgents/static/";
    paramsString = paramsString ? "?" + paramsString : "";
    const url = preUrlPath + fileName + paramsString;
    Logger.info("model token config url=" + url);

    try {
        const response = await fetch(url);

        // 检查响应状态是否为200系列（成功）
        if (!response.ok) {
            Logger.error("model token request status=" + response.status);
            return {}
        }

        const result = await response.json();
        Logger.info("model token request result=" + JSON.stringify(result));
        return result; // 返回JSON响应体
    } catch (error) {
        Logger.error("model token request error=" + error);
        return {}; // 确保返回值类型一致
    }
}