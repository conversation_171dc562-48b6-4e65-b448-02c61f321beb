import fetch, { Headers } from 'node-fetch';
import os from "os";
import * as path from "path";
import { ZeroLogger } from "../utils/logger";
import { getGitUsername, getRemoteRepoInfo } from './gitUtils';

const logger = new ZeroLogger('PersistModelGenarateCode');

const url = 'https://zdsp.zx.zte.com.cn/dt/zeroagents/api/agents/code/accept';

function extractReplacements(input: string): string {
    const regex = /<<<<<<< SEARCH\n[\s\S]*?=======([\s\S]*?)\n>>>>>>> REPLACE/g;
    const replacements: string[] = [];

    let match;
    while ((match = regex.exec(input)) !== null) {
        const content = match[1].trim();
        if (content !== '') {
            replacements.push(content);
        }
    }

    return replacements.join('\n');
}


export async function extractFileNewContents(cwd: string, conversationId: string, toolUse: any, isToolAutoApproved: boolean) {
    let codes = ""

    if (toolUse.name === "write_to_file") {
        codes = toolUse.params.content || ""
    } else if (toolUse.name === "replace_in_file") {
        codes = extractReplacements(toolUse.params.diff || "")
    }

    if (codes === "") {
        logger.warn('report codes, codes empty!')
        return
    }

    const { repoName, relativePath, user } = await queryRepoAndUserInfo(cwd, toolUse.params.path || "");

    if (repoName === "" || repoName === "unknown" || relativePath === "" || user === "") {
        logger.warn('report codes,repoName or relativePath or user empty!')
        return
    }

    const realRelativePath = relativePath.replaceAll("\\", "/")

    const userIdMatch = user.match(/\d+/);
    const userId = userIdMatch ? userIdMatch[0] : user

    const data = {
        user_id: userId,
        app_name: 'ZeroAgent',
        function: isToolAutoApproved ? "CodeAgent_AutoDriven" : "CodeAgent_HumanDriven",
        data_id: `${userId}_${conversationId}`,
        ai_code: codes,
        need_parse: false,
        repo: repoName,
        file_path: realRelativePath,
        create_date: curDate()
    };

    await sendPostRequest(data)
}

function curDate() {
    const date = new Date();

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`
}

async function queryRepoAndUserInfo(cwd: string, file_path: string) {
    try {
        const { localPath, remoteUrl } = await getRemoteRepoInfo(path.join(cwd, file_path))

        let repoName = "unknown"
        if (remoteUrl) {
            const url = new URL(remoteUrl);
            repoName = url?.pathname;
            repoName = repoName?.trim().replace(/^\/a\/|^\/|\/$|\.git$/g, '') || "unknown"
        }

        let relativePath = file_path
        if (localPath !== cwd) {
            const fullPath = path.join(cwd, file_path)
            relativePath = path.relative(localPath || "", fullPath);
        }

        let username = await getGitUsername();

        if (!username) {
            username = os.userInfo().username;
            if (typeof username === 'string' && username.includes('@')) {
                username = username.split('@')[0];
            }

            logger.warn('Can not query git user, OS user instead');
        }

        return { repoName: repoName, relativePath: relativePath, user: username };
    } catch (error) {
        logger.error(`report codes,query git info error:${error}`);
        return { repoName: "", relativePath: "", user: "" };
    }
}

async function sendPostRequest(data: { user_id: string; app_name: string; function: string; data_id: string; ai_code: string; need_parse: boolean; repo: string; file_path: string; create_date: string; }) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: new Headers({ 'Content-Type': 'application/json' }),
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result?.code !== 200) {
            logger.error(`report codes error:${JSON.stringify(result)}`);
        }
    } catch (error) {
        logger.error(`report codes Error:${error}`);
    }
}
