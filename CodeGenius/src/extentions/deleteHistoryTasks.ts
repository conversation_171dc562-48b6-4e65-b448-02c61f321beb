import fs from "fs/promises";
import cron from 'node-cron';
import os from "os";
import path from 'path';

import { ExtensionContext } from 'vscode';

import { GlobalFileNames } from "@/core/storage/disk";
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { ZeroLogger } from "../utils/logger";


const logger = new ZeroLogger("deleteLocalHistory");

export async function deleteLocalHistory(workspace: string) {
    del3MonthAgoTasks(workspace)
    del1monthAgoPrompts(workspace)
    del3monthAgoLogs(workspace)
}

async function del3MonthAgoTasks(workspace: string) {
    // 删除tasks目录下三个月前的文件夹
    const tasksDir = path.join(workspace, 'tasks');
    try {
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

        const threeAgoStr = threeMonthsAgo.getTime().toString();

        let count = 0

        const entries = await fs.readdir(tasksDir, { withFileTypes: true });
        for (const entry of entries) {
            if (entry.isDirectory()) {
                if (entry.name < threeAgoStr) {
                    const fullPath = path.join(tasksDir, entry.name);
                    await fs.rm(fullPath, { recursive: true, force: true });
                    count++
                }
            }
        }

        logger.info(`deleting history tasks count: ${count}`);
    } catch (error) {
        logger.error('Error deleting history tasks:', error);
    }
}

async function del1monthAgoPrompts(workspace: string) {
    const promptsDir = path.join(workspace, 'prompts');
    try {
        // 生成一个月前的yyyymmdd字符串
        const currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 1);
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        const oneMonthAgoStr = `${year}${month}${day}`;

        let count = 0

        const entries = await fs.readdir(promptsDir, { withFileTypes: true });
        for (const entry of entries) {
            if (entry.isDirectory()) {
                // 字符串直接比较代替时间戳比较
                if (entry.name < oneMonthAgoStr) {
                    const fullPath = path.join(promptsDir, entry.name);
                    await fs.rm(fullPath, { recursive: true, force: true });
                    count++
                }
            }
        }
        logger.info('deleting history prompts count: ' + count);
    } catch (error) {
        logger.error('Error deleting history prompts:', error);
    }
}

//zero-2025-05-21.log
async function del3monthAgoLogs(workspace: string) {
    const logsDir = path.join(workspace, 'logs','backup');
    try {
        // 生成一个月前的yyyymmdd字符串
        const currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 3);
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        const oneMonthAgoStr = `zero-${year}-${month}-${day}.`;

        let count = 0

        const entries = await fs.readdir(logsDir, { withFileTypes: true });
        for (const entry of entries) {
            if (entry.isFile()) {
                // 字符串直接比较代替时间戳比较
                if (entry.name < oneMonthAgoStr) {
                    const fullPath = path.join(logsDir, entry.name);
                    await fs.rm(fullPath, { recursive: true, force: true });
                    count++
                }
            }
        }
        logger.info('deleting history logs count: ' + count);
    } catch (error) {
        logger.error('Error deleting history logs:', error);
    }
}

