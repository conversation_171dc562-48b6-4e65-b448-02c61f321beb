import { Anthropic } from "@anthropic-ai/sdk"
import * as fs from "fs"
import OpenAI from "openai"
import { convertToOpenAiMessages } from "../api/transform/openai-format";

export async function printPromptZte(
	systemPrompt: string, 
	messages: Anthropic.Messages.MessageParam[], 
	workDir: string, 
	taskId: string, 
	lastTs: number
): Promise<void> {
	if (!systemPrompt || !messages || !workDir || !taskId) {
		throw new Error('Missing required parameters');
	}
	const finalPrompt = messages.length === 1 ? systemPrompt : '###systemPrompt###'

	const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
		{ role: "system", content: finalPrompt },
		...convertToOpenAiMessages(messages),
	]

	const fileName = await obtainFilePath(workDir, taskId, lastTs);

	const formattedMessages = JSON.stringify(openAiMessages, null, 2);
	try {
		await fs.promises.writeFile(fileName, formattedMessages);
	} catch (error) {
		console.error(`Failed to write prompt file: ${error.message}`);
		throw error;
	}
}

async function obtainFilePath(workDir: string, taskId: string, lastTs: number): Promise<string> {
	const date = new Date(lastTs)

	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');

	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');

	await fs.promises.mkdir(`${workDir}/prompts/${year}${month}${day}/${taskId}`, { recursive: true });

	return `${workDir}/prompts/${year}${month}${day}/${taskId}/${hours}${minutes}${seconds}.json`;
}