export default {
    employee_zero: "Emp<PERSON><PERSON><PERSON><PERSON>",
    answer_lang: " ",
    no_results_found: "No results found",
    add_to_employee<PERSON><PERSON>: "Add to employee<PERSON><PERSON>",
    context_window_exceeded: "Context window exceeded. Zero Agents retry to truncate the conversation and try again.",
    duplicate_file_read_notice: "[[NOTE] This file read has been removed to save space in the context window. Refer to the latest file read for the most up to date version of this file.]",
    context_truncation_notice : "[NOTE] Some previous conversation history with the user has been removed to maintain optimal context window length. The initial user task and the most recent exchanges have been retained for continuity, while intermediate conversation history has been removed. Please keep this in mind as you continue assisting the user.",
    task: {
        instance_aborted: "Task instance aborted",
        missing_history_item_or_task_para: "Either historyItem or task/images must be provided",
        unable_to_access_extension_context: "Unable to access extension context",
        last_message_is_not_a_user_or_assistant_message: "Unexpected: Last message is not a user or assistant message",
        no_existing_API_conversation_history : "Unexpected: No existing API conversation history",
        api_request_failed : "API request failed",
        restore_task: "Task messages have been restored to the checkpoint",
        restore_workspace: "Workspace files have been restored to the checkpoint",
        restore_workspace_and_task: "Task and workspace have been restored to the checkpoint",
        ask_continue_the_task: "ZeroAgent is having trouble. Would you like to continue the task?",
        mistake_limit_reached_tips: "ZeroAgent uses complex prompts and iterative task execution that may be challenging for less capable models. For best results, it's recommended to use Claude 3.7 Sonnet for its advanced agentic coding capabilities.",
        
    },
}