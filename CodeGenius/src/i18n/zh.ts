export default {
    employee_zero: "零号员工",
    answer_lang: "Answer in Chinese as much as possible.",
    no_results_found: "没有找到结果",
    add_to_employeeZero: "添加至零号员工",
    fix_with_employeeZ<PERSON>: "使用零号员工修复",
    context_window_exceeded: "上下文窗口Token超限， 零号员工想要清理历史会话，请点击重试按钮再次尝试",
    duplicate_file_read_notice : "[[注意] 为节省上下文窗口空间，此文件读取已被删除。请参阅最新文件读取以获取此文件的最新版本。]",
    context_truncation_notice : "[注意] 为保持最佳上下文窗口长度，部分先前与用户的对话历史记录已被移除。初始用户任务和最近的对话将被保留以保持连续性，而中间对话历史记录已被移除。请在继续协助用户时牢记这一点。",
    task: {
        instance_aborted: "任务实例已终止", 
        missing_history_item_or_task_para: "必须提供 historyItem 或 task/images参数",
        unable_to_access_extension_context: "无法访问插件上下文",
        last_message_is_not_a_user_or_assistant_message: "Error: 最后一条消息不是用户消息或大模型返回的消息",
        no_existing_API_conversation_history : "Error: 缺少历史会话记录",
        api_request_failed : "API 请求失败",
        restore_task: "任务已恢复至检查点",
        restore_workspace: "工作区文件已恢复至检查点",
        restore_workspace_and_task: "任务和工作区已恢复到检查点",
        ask_continue_the_task: "零号员工遇到一些问题。您要继续执行该任务吗？",
        mistake_limit_reached_tips: "零号员工使用复杂的提示和迭代任务执行，这对于性能较弱的模型来说可能具有挑战性。为了获得最佳效果，建议使用deepseek等高阶模型"
    },
}