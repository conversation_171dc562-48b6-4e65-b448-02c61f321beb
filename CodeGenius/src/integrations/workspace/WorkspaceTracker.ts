import * as vscode from "vscode"
import * as path from "path"
import { listFiles } from "@services/glob/list-files"
import { ExtensionMessage } from "@shared/ExtensionMessage"
import { getExcludePartens as getExcludePatterns, getSettingValueByKey, outputChannel } from "../../extension"
import * as fs from 'node:fs';

const cwd = vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath).at(0)

// Note: this is not a drop-in replacement for listFiles at the start of tasks, since that will be done for Desktops when there is no workspace selected
class WorkspaceTracker {
	private disposables: vscode.Disposable[] = []
	private filePaths: Set<string> = new Set()

	constructor(private readonly postMessageToWebview: (message: ExtensionMessage) => Promise<void>) {
		this.postMessageToWebview = postMessageToWebview
		this.registerListeners()
	}

	async populateFilePaths() {
		outputChannel?.appendLine(`根路径: ${cwd}`)
		// Use VSCode's findFiles with comprehensive exclusion
		const startTime = Date.now();
		outputChannel?.appendLine("开始加载文件")

		const excludePatternsStr = getExcludePatterns().join(",");
		const files = await vscode.workspace.findFiles('**/*', `{${excludePatternsStr}}`);
		files.forEach((fileUri) => this.filePaths.add(this.normalizeFilePath(fileUri.fsPath)))
		outputChannel?.appendLine("文件总数: " + files.length)

		const allPath = await this.findWorkSpaceAllDirs();
		allPath.forEach((fileUri) => this.filePaths.add(fileUri))
		outputChannel?.appendLine("目录总数: " + allPath.length)

		// 计算总用时
		const endTime = Date.now();
		const totalTime = Math.floor((endTime - startTime) / 1000); // 转换为秒
		const minutes = Math.floor(totalTime / 60);
		const seconds = totalTime % 60;
		outputChannel?.appendLine(`加载文件总用时: ${minutes}分钟${seconds}秒`);

		// 老代码调用listFiles效率很力
		// const [files, _] = await listFiles(cwd, true, 5_000)
		// files.forEach((file) => this.filePaths.add(this.normalizeFilePath(file)))

		// should not auto get filepaths for desktop since it would immediately show permission popup before cline ever creates a file
		// if (!cwd) {
		// 	return
		// }
		// const [files, _] = await listFiles(cwd, true, 1_000)
		// files.forEach((file) => this.filePaths.add(this.normalizeFilePath(file)))
		this.workspaceDidUpdate()
	}

	// 获取当前工作区的所有文件夹
	private async findWorkSpaceAllDirs(): Promise<string[]> {
		const folders = vscode.workspace.workspaceFolders;
		if (folders && folders.length > 0) {
			const root = folders[0].uri.fsPath;
			const dirs = this.getAllSubdirectories(root, new Set<string>(getSettingValueByKey("excludeDirectories") || []));
			return dirs;
		}
		return [];
	}

	private readonly defaultExcludeDirReg = new RegExp("^\\..*$");

	// 获取路径下所有子文件夹
	private getAllSubdirectories(dirPath: string, excludedDirs: Set<string>): string[] {
		let subdirectories: string[] = [];
		try {
			const files = fs.readdirSync(dirPath);
			files.forEach(file => {
				const fullPath = path.join(dirPath, file);
				// 排除文件夹
				if (fs.statSync(fullPath).isDirectory() && !this.defaultExcludeDirReg.test(file) && !excludedDirs.has(file)) {
					subdirectories.push(fullPath + '/');
					// 递归调用获取子文件夹
					subdirectories = subdirectories.concat(this.getAllSubdirectories(fullPath, excludedDirs));
				}
			});
		} catch (err) {
			vscode.window.showErrorMessage(`Error read directory: ${err}`);
			return [];
		}
		return subdirectories;
	}

	private registerListeners() {
		// Listen for file creation
		// .bind(this) ensures the callback refers to class instance when using this, not necessary when using arrow function
		this.disposables.push(vscode.workspace.onDidCreateFiles(this.onFilesCreated.bind(this)))

		// Listen for file deletion
		this.disposables.push(vscode.workspace.onDidDeleteFiles(this.onFilesDeleted.bind(this)))

		// Listen for file renaming
		this.disposables.push(vscode.workspace.onDidRenameFiles(this.onFilesRenamed.bind(this)))

		this.watchWorkspaceFile()

		this.listenExcludePathOrFileConfigChange();


		/*
		 An event that is emitted when a workspace folder is added or removed.
		 **Note:** this event will not fire if the first workspace folder is added, removed or changed,
		 because in that case the currently executing extensions (including the one that listens to this
		 event) will be terminated and restarted so that the (deprecated) `rootPath` property is updated
		 to point to the first workspace folder.
		 */
		// In other words, we don't have to worry about the root workspace folder ([0]) changing since the extension will be restarted and our cwd will be updated to reflect the new workspace folder. (We don't care about non root workspace folders, since cline will only be working within the root folder cwd)
		// this.disposables.push(vscode.workspace.onDidChangeWorkspaceFolders(this.onWorkspaceFoldersChanged.bind(this)))
	}

	private async onFilesCreated(event: vscode.FileCreateEvent) {
		await Promise.all(
			event.files.map(async (file) => {
				await this.addFilePath(file.fsPath)
			}),
		)
		this.workspaceDidUpdate()
	}

	private async onFilesDeleted(event: vscode.FileDeleteEvent) {
		let updated = false
		await Promise.all(
			event.files.map(async (file) => {
				if (await this.removeFilePath(file.fsPath)) {
					updated = true
				}
			}),
		)
		if (updated) {
			this.workspaceDidUpdate()
		}
	}

	private async onFilesRenamed(event: vscode.FileRenameEvent) {
		await Promise.all(
			event.files.map(async (file) => {
				await this.removeFilePath(file.oldUri.fsPath)
				await this.addFilePath(file.newUri.fsPath)
			}),
		)
		this.workspaceDidUpdate()
	}

	private workspaceDidUpdate() {
		if (!cwd) {
			return
		}
		this.postMessageToWebview({
			type: "workspaceUpdated",
			filePaths: Array.from(this.filePaths).map((file) => {
				const relativePath = path.relative(cwd, file).toPosix()
				return file.endsWith("/") ? relativePath + "/" : relativePath
			}),
		})
	}

	private normalizeFilePath(filePath: string): string {
		const resolvedPath = cwd ? path.resolve(cwd, filePath) : path.resolve(filePath)
		return filePath.endsWith("/") ? resolvedPath + "/" : resolvedPath
	}

	private async addFilePath(filePath: string): Promise<string> {
		const normalizedPath = this.normalizeFilePath(filePath)
		try {
			const stat = await vscode.workspace.fs.stat(vscode.Uri.file(normalizedPath))
			const isDirectory = (stat.type & vscode.FileType.Directory) !== 0
			const pathWithSlash = isDirectory && !normalizedPath.endsWith("/") ? normalizedPath + "/" : normalizedPath
			this.filePaths.add(pathWithSlash)
			return pathWithSlash
		} catch {
			// If stat fails, assume it's a file (this can happen for newly created files)
			this.filePaths.add(normalizedPath)
			return normalizedPath
		}
	}

	private async removeFilePath(filePath: string): Promise<boolean> {
		const normalizedPath = this.normalizeFilePath(filePath)
		return this.filePaths.delete(normalizedPath) || this.filePaths.delete(normalizedPath + "/")
	}

	public dispose() {
		this.disposables.forEach((d) => d.dispose())

		// Clear debounce timer
		if (this.indexingDebounceTimer) {
			clearTimeout(this.indexingDebounceTimer);
			this.indexingDebounceTimer = null;
		}
	}

	// 系统级文件创建/删除监听兜底方法
	private doNotWatch: boolean = false;
	private indexingDebounceTimer: NodeJS.Timeout | null = null;
	private readonly INDEXING_DEBOUNCE_DELAY = 3000; // 3 seconds debounce

	private watchWorkspaceFile() {
		const watcher = vscode.workspace.createFileSystemWatcher('**/*');
		watcher.onDidCreate(async uri => {
			await this.waitFor(1000);
			if (!this.doNotWatch) {
				const event = { files: [uri] }
				await this.onFilesCreated(event)
			}
			this.doNotWatch = false;
		});
		watcher.onDidDelete(async uri => {
			await this.waitFor(1000);
			if (!this.doNotWatch) {
				const event = { files: [uri] }
				await this.onFilesDeleted(event)
			}
			this.doNotWatch = false;
		});
		watcher.onDidChange(async () => {
		})

		this.disposables.push(watcher)
	}

	private async waitFor(milliseconds: number) {
		return new Promise(resolve => setTimeout(resolve, milliseconds))
	}

	public listenExcludePathOrFileConfigChange() {
		try {
			// 监听排除的目录或文件设置的变化
			vscode.workspace.onDidChangeConfiguration(event => {
				if (event.affectsConfiguration('employeeZero.excludeDirectories') || event.affectsConfiguration('employeeZero.excludeFiles')) {
					this.filePaths.clear();
					this.populateFilePaths();
				}
			});
		} catch (error) {
			vscode.window.showErrorMessage(`The registration of the listener for configuration changes failed: ${error}`);
		}
	}

	public getFilePaths(): Set<string> {
		return this.filePaths
	}
}

export default WorkspaceTracker
