import { Counter, Pushgateway, Registry } from 'prom-client';
import { ApiHandler } from '../api';
import { <PERSON>wenHandler } from '../api/providers/qwen';
import { DeepSeekHandler } from '../api/providers/deepseek';
import { OpenAiHandler } from '../api/providers/openai';
const os_local = require('os');
import { ZeroLogger } from "../utils/logger";
import { NebulaReasonHandler } from '../api/providers/nebula-reason-coder';
import { getGitUsername } from '../extentions/gitUtils';

const logger = new ZeroLogger('pushToGateway.');

//https://github.com/siimon/prom-client/blob/master/example/counter.js
const register = new Registry();
const gateway = new Pushgateway('https://zdsp.zx.zte.com.cn/dt/zero-agent/prom-aggregation-gateway', [], register);
const jobName = 'my_job';

const usageCounter = new Counter({
  name: 'usage_count',
  help: '零号员工使用统计',
  labelNames: ['user_id', 'os'],
  registers: [register]
});

export async function push_to_vm() {
  try {
    usageCounter.reset();
    usageCounter.inc({ user_id: os_local.userInfo().username, os: os_local.platform() });
  } catch (error) {
    logger.error(`任务统计上报失败: ${error}`);
  }
}

const modelUsageCounter = new Counter({
  name: 'model_usage_count',
  help: '模型使用统计',
  labelNames: ['user_id', 'model_name'],
  registers: [register]
});

export function get_model_name(api_handler: ApiHandler): string {
  let model_name = 'Nebula';
  
  if (api_handler instanceof QwenHandler) {
    model_name = 'Qwen';
  } else if (api_handler instanceof DeepSeekHandler) {
    model_name = 'DeepSeek';
  } else if (api_handler instanceof OpenAiHandler) {
    model_name = 'OpenAi';
  } else if (api_handler instanceof NebulaReasonHandler) {
    model_name = 'Nebula-Reason';
  }
  return model_name;
}

export async function push_model_usage_counter_to_vm(api_handler: ApiHandler) {
  try {
    modelUsageCounter.reset();
    const model_name = get_model_name(api_handler);
    modelUsageCounter.inc({ user_id: os_local.userInfo().username, model_name });
    await gateway.push({ jobName });
    
    usageCounter.reset();
    modelUsageCounter.reset();
    pluginVersionCounter.reset();
  } catch (error) {
    logger.error(`指标上报失败: ${error}`);
  }
}


const pluginVersionCounter = new Counter({
  name: 'zero_version',
  help: 'Current plugin version',
  labelNames: ['user_id', 'version'],
  registers: [register]
});

export async function push_plugin_version_to_vm(version: string) {
  try {
    logger.info(`上报版本号: ${version}`);

    let user_id = os_local.userInfo().username;

    if (user_id === 'root') {
      user_id = getGitUsername() || 'unknown';
    }

    pluginVersionCounter.inc({ user_id, version }, 1);
  } catch (error) {
    logger.error(`Failed to report plugin version: ${error}`);
  }
}
