# 文档生成服务

本服务提供为项目生成文档模板的功能。它允许用户从一组预定义的文档模板中进行选择，并在目标目录中生成这些文档。

## 功能特点

- 从预定义的模板集合中生成文档模板
- 基于"代码业务知识显式化方法.md"默认选择关键模板
- 跳过已存在的文件并通知用户
- 防止在"docs"目录中生成文档，避免嵌套的文档目录
- 支持右键点击文件（使用父目录）

## 使用方法

文档生成服务在VS Code中注册为一个命令：

```typescript
vscode.commands.registerCommand('employeeZero.generateDocsStructure', async (uri: vscode.Uri) => {
  // 实现...
});
```

用户可以通过以下方式访问此功能：
1. 在资源管理器视图中右键点击文件夹
2. 在资源管理器视图中右键点击文件（将使用父目录）
3. 选择"Generate Documentation Skeleton / 生成文档骨架"

## 实现细节

### 模板集合

模板内容完全通过 VS Code 设置进行自定义和管理。所有模板都存储在 `employeeZero.docgen.templates` 配置项中，没有硬编码在代码中的模板。

默认情况下，配置项中包含以下模板：
- README.md：项目概述
- docs/architecture.md：架构设计文档
- docs/glossary.md：术语表
- CONTRIBUTING.md：贡献指南
- CHANGELOG.md：更新日志
- 以及其他更专业的文档模板

这些模板可以通过编辑 settings.json 文件或使用提供的模板编辑器命令进行自定义。

### 自定义模板

用户可以通过编辑 settings.json 文件自定义文档模板内容：

1. 打开 VS Code 设置（文件 > 首选项 > 设置）
2. 点击右上角的"打开设置(JSON)"图标
3. 添加或修改 `employeeZero.docgen.templates` 配置

示例配置：

```json
"employeeZero.docgen.templates": {
  "README.md": "# 我的项目\n\n这是一个自定义的README模板。\n",
  "docs/architecture.md": "# 架构设计\n\n这是一个自定义的架构文档模板。\n",
  "docs/custom.md": "# 自定义文档\n\n这是一个新增的自定义文档模板。\n"
}
```

您可以：
- 修改现有模板的内容
- 添加新的模板
- 删除不需要的模板

模板键是相对于目标目录的文件路径，值是文件内容。

**注意**：此设置只能在 settings.json 文件中编辑，不会在 VS Code 的图形化设置界面中显示编辑选项。

#### 使用模板编辑器命令

为了更方便地编辑模板内容，我们提供了两个特殊命令：

1. **编辑文档模板**：
   - 命令：`employeeZero: Edit Document Template / 编辑文档模板`
   - 功能：在普通文本编辑器中打开现有模板进行编辑
   - 使用方法：
     1. 打开命令面板（按 `Ctrl+Shift+P` 或 `Cmd+Shift+P`）
     2. 输入 `Edit Document Template` 并选择命令
     3. 从列表中选择要编辑的模板
     4. 在打开的编辑器中编辑模板内容
     5. 保存并关闭编辑器，系统会自动将内容更新到设置中

2. **创建新文档模板**：
   - 命令：`employeeZero: Create New Document Template / 创建新文档模板`
   - 功能：创建新的文档模板
   - 使用方法：
     1. 打开命令面板（按 `Ctrl+Shift+P` 或 `Cmd+Shift+P`）
     2. 输入 `Create New Document Template` 并选择命令
     3. 输入新模板的文件路径（例如 `docs/custom.md`）
     4. 在打开的编辑器中编写模板内容
     5. 保存并关闭编辑器，系统会自动将内容添加到设置中

这些命令使您可以在普通文本编辑器中编辑模板内容，无需手动处理换行符（`\n`）。

#### 手动编辑多行文本的技巧

如果您仍然希望直接在 settings.json 中编辑模板内容，以下是一些编辑多行文本的技巧：

1. **使用临时文件**：
   - 在 VS Code 中创建一个临时文件
   - 编写您的模板内容，使用实际的换行而不是 `\n`
   - 使用 VS Code 的"转义字符串"功能（可以通过扩展实现）或手动将文本转换为带有 `\n` 的字符串
   - 将转换后的字符串复制到 settings.json 中

2. **使用在线工具**：
   - 使用在线工具将多行文本转换为带有 `\n` 的 JSON 字符串
   - 例如：[JSON Escape / Unescape](https://www.freeformatter.com/json-escape.html)

3. **使用 VS Code 扩展**：
   - 安装支持多行字符串编辑的 VS Code 扩展
   - 例如："JSON Tools"、"Edit csv" 等

### 默认模板

以下模板默认被预选（如果它们尚不存在）：
- README.md
- docs/architecture.md
- docs/glossary.md

这些基于"代码业务知识显式化方法.md"中的建议。

### 工作流程

1. 当命令被触发时：
   - 如果选择了文件，使用其父目录作为目标
   - 检查目标目录是否是或包含"docs"目录
   - 如果是，显示警告并阻止文档生成
   - 检查目标目录中已经存在哪些模板
   - 为每个已存在的文件显示通知
   - 显示仅包含不存在模板的选择列表
   - 预选默认模板
   - 生成选定的模板

### 处理已存在的文件

- 服务检查每个模板是否已经存在于目标目录中
- 对于每个已存在的文件，向用户显示通知
- 选择列表中只显示不存在的文件
- 如果所有模板都已存在，则显示消息并且不显示选择列表

### 目录结构保护

为了防止创建嵌套的文档目录，服务会检查目标目录是否名为"docs"或包含"docs"目录。如果是，服务会显示警告消息并阻止文档生成。

## 代码结构

- `index.ts`：文档生成服务的主要实现
- `template-editor.ts`：提供编辑模板的命令和功能
- `README.md`：本文档，提供服务的说明和使用方法

## 关键函数

### `registerDocGenCommand`

向VS Code注册文档生成命令并处理初始命令执行。

### `generateDocumentationStructure`

主要功能：
1. 从配置中读取模板内容
2. 检查已存在的文件
3. 显示已存在文件的通知
4. 显示不存在文件的选择列表
5. 生成选定的模板

### `registerTemplateEditorCommands`

注册用于编辑模板的命令：
1. `employeeZero.editDocTemplate`：编辑现有模板
2. `employeeZero.newDocTemplate`：创建新模板

## 未来增强

该服务的潜在改进：

1. **模板自定义**：允许用户在生成前自定义模板
2. **项目特定模板**：检测项目类型并建议适当的模板
3. **模板预览**：允许用户在生成前预览模板
4. **模板分类**：按类别对模板进行分组，便于选择
5. **模板变量**：支持模板中的变量（例如，项目名称，作者）
6. **模板编辑**：用于修改模板的集成编辑器
7. **模板共享**：允许用户共享自定义模板
8. **模板导入/导出**：从/到文件导入/导出模板
9. **模板版本控制**：跟踪模板版本并允许更新
10. **模板搜索**：用于查找特定模板的搜索功能

## 维护注意事项

修改此服务时，请考虑以下几点：

1. 保持UI简单直观
2. 保持与现有模板的向后兼容性
3. 确保适当的错误处理
4. 遵循VS Code扩展最佳实践
5. 在进行重大更改时更新此README
