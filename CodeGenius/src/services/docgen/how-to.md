# 文档生成功能使用指南

## 概述

文档生成功能允许您快速为项目创建标准化的文档结构。通过简单的右键菜单操作，您可以生成包括README、架构设计、API文档等在内的多种文档模板，帮助您更好地记录和管理项目知识。

## 功能特点

- 一键生成多种文档模板
- 自动跳过已存在的文件，避免覆盖
- 支持自定义文档模板内容
- 提供默认选中的核心文档模板
- 防止在docs目录中嵌套创建文档

## 基本使用

### 生成文档

1. 在VS Code的资源管理器中，右键点击目标文件夹或文件
2. 在上下文菜单中选择"Generate Documentation Skeleton / 生成文档骨架"
3. 在弹出的选择框中，勾选您需要生成的文档（默认已选择推荐文档）
4. 点击确定，系统将在目标目录中生成所选文档

> **注意**：如果您选择的是文件而非文件夹，系统会在该文件所在的目录中生成文档。

### 默认选中的文档

以下文档默认被选中（如果它们尚不存在）：
- README.md - 项目/模块整体介绍
- docs/architecture.md - 架构设计文档
- docs/api/api_overview.md - API概览文档
- docs/tutorials/getting_started.md - 入门指南
- docs/glossary.md - 术语表
- docs/development.md - 开发指南

这些文档基于[代码业务知识显式化方法](https://i.zte.com.cn/#/shared/db2bc11e40324859bde032b212536fd5/wiki/page/d4a0ea7b0487496d99291ca44f42229e/view)中的建议，涵盖了项目文档的核心内容。

### 已存在文件的处理

当您尝试生成的文档已经存在时：
- 系统会显示通知，告知您哪些文件已经存在
- 已存在的文件不会出现在选择列表中
- 系统只会生成尚不存在的文档

### 目录限制

为避免创建嵌套的文档结构，当您尝试在"docs"目录或其子目录中生成文档时，系统会自动将目标目录调整为"docs"目录的父目录。

例如：
- 如果您在 `/project/docs` 目录中右键选择生成文档，实际生成位置将是 `/project`
- 如果您在 `/project/docs/api` 目录中右键选择生成文档，实际生成位置也将是 `/project`

这样可以确保文档始终生成在正确的位置，避免创建嵌套的文档结构。

## 自定义文档模板

您可以根据自己的需求自定义文档模板内容。有两种方式可以实现这一点：

### 方法一：使用模板编辑器命令（推荐）

1. **编辑现有模板**：
   - 打开命令面板（按 `Ctrl+Shift+P` 或 `Cmd+Shift+P`）
   - 输入 `Edit Document Template` 并选择命令
   - 从列表中选择要编辑的模板
   - 在打开的编辑器中编辑模板内容
   - 保存并关闭编辑器，系统会自动将内容更新到设置中

2. **创建新模板**：
   - 打开命令面板（按 `Ctrl+Shift+P` 或 `Cmd+Shift+P`）
   - 输入 `Create New Document Template` 并选择命令
   - 输入新模板的文件路径（例如 `docs/custom.md`）
   - 在打开的编辑器中编写模板内容
   - 保存并关闭编辑器，系统会自动将内容添加到设置中

### 方法二：直接编辑settings.json

1. 打开VS Code设置（文件 > 首选项 > 设置）
2. 点击右上角的"打开设置(JSON)"图标
3. 添加或修改 `employeeZero.docgen.templates` 配置

示例配置：

```json
"employeeZero.docgen.templates": {
  "README.md": "# 我的项目\n\n这是一个自定义的README模板。\n",
  "docs/architecture.md": "# 架构设计\n\n这是一个自定义的架构文档模板。\n",
  "docs/custom.md": "# 自定义文档\n\n这是一个新增的自定义文档模板。\n"
}
```

> **注意**：此设置只能在 settings.json 文件中编辑，不会在VS Code的图形化设置界面中显示编辑选项。

## 模板语法

模板内容使用Markdown格式，并支持以下特性：

- 标准Markdown语法（标题、列表、表格等）
- 换行符使用`\n`表示
- 可以包含代码块和其他Markdown高级格式

示例：

```json
"README.md": "# 项目名称\n\n## 简介\n\n这是项目简介。\n\n## 功能特点\n\n- 功能1\n- 功能2\n- 功能3\n"
```

## 常见问题

### Q: 如何一次性生成所有文档？

A: 在选择文档的对话框中，您可以手动选择所有未被选中的文档，或者使用快捷键 `Ctrl+A`（Windows/Linux）或 `Cmd+A`（Mac）全选。

### Q: 我可以在生成后修改文档内容吗？

A: 是的，生成的文档是普通的Markdown文件，您可以随时编辑它们。模板只是提供了初始内容。

### Q: 如何添加项目特定的信息到模板中？

A: 您可以自定义模板内容，加入项目特定的信息。目前需要手动编辑模板，你可以使用大模型帮助生成这些内容，但需要人工校验。

### Q: 为什么我看不到"Generate Documentation Skeleton"选项？

A: 请确保您已安装并启用了插件，并且您正在资源管理器视图中右键点击文件或文件夹。

### Q: 如何恢复默认模板？

A: 从settings.json中删除`employeeZero.docgen.templates`配置项，插件将使用默认模板。

## 最佳实践

1. **在项目根目录生成文档**：为了保持标准的项目结构，建议在项目根目录生成文档。对于大型项目，亦可以在子模块目录生成文档，此时README.md中描述的是子模块的文档。

2. **根据项目需求选择文档**：不是所有项目都需要所有类型的文档，根据项目规模和性质选择适当的文档。

3. **定期更新文档**：生成文档后，请确保随着项目发展定期更新文档内容。

4. **自定义团队模板**：为团队创建标准化的文档模板，确保所有项目文档风格一致。

5. **结合代码注释**：文档应与代码注释相辅相成，共同构成完整的项目知识体系。

---

如有任何问题或建议，请通过项目仓库的Issues页面反馈。