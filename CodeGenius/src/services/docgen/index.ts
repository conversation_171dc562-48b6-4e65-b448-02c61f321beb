/**
 * Document generation service
 * Migrated from code-knowledge-docgen extension
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '../logging/Logger';
import { registerTemplateEditorCommands } from './template-editor';

/**
 * Generate documentation structure based on selected templates
 * @param targetDir The target directory to generate documentation in
 * @returns Promise<void>
 */
export async function generateDocumentationStructure(
  targetDir: string
): Promise<void> {
  try {
    // Get templates from configuration
    const config = vscode.workspace.getConfiguration('employeeZero');
    const templates = config.get<{ [key: string]: string }>('docgen.templates') || {};

    // If no templates are defined, show an error message
    if (Object.keys(templates).length === 0) {
      vscode.window.showErrorMessage('未找到文档模板配置，请在 settings.json 中配置 employeeZero.docgen.templates / No document templates found, please configure employeeZero.docgen.templates in settings.json');
      return;
    }

    // Get all template paths
    const allTemplatePaths = Object.keys(templates);

    // Check which templates already exist in the target directory
    const existingFiles: string[] = [];
    for (const templatePath of allTemplatePaths) {
      const fullPath = path.join(targetDir, templatePath);
      if (fs.existsSync(fullPath)) {
        existingFiles.push(templatePath);
      }
    }

    // If all templates already exist, show a message and return
    if (existingFiles.length === allTemplatePaths.length) {
      vscode.window.showInformationMessage('所有文档已存在 / All documents already exist.');
      return;
    }

    // Default templates to select based on "代码业务知识显式化方法" document
    const defaultTemplateNames = [
      "README.md",
      "docs/architecture.md",
      "docs/api/api_overview.md",
      "docs/tutorials/getting_started.md",
      "docs/glossary.md",
      "docs/development.md"
    ];

    // Show notification for each existing file
    for (const file of existingFiles) {
      vscode.window.showInformationMessage(`文件 "${file}" 已存在 / File "${file}" already exists.`);
    }

    // Only show non-existing files in the selection list
    const nonExistingFiles = allTemplatePaths.filter(file => !existingFiles.includes(file));

    // If no files to generate, show a message and return
    if (nonExistingFiles.length === 0) {
      vscode.window.showInformationMessage('所有文档已存在 / All documents already exist.');
      return;
    }

    // Create items for non-existing files
    const nonExistingItems = nonExistingFiles.map(file => ({
      label: file,
      picked: defaultTemplateNames.includes(file)
    }));

    // Get relative path for display
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    const displayPath = workspaceFolder ? path.relative(workspaceFolder, targetDir) || '${workspace_root}' : targetDir;

    // Show the quick pick with only non-existing files
    const selected = await vscode.window.showQuickPick(nonExistingItems, {
      canPickMany: true,
      placeHolder: `请选择要为 ${displayPath} 生成的文档 / Select documentation templates to generate for ${displayPath}`
    });

    if (!selected || selected.length === 0) {
      vscode.window.showInformationMessage('未选择任何文档 / No templates selected.');
      return;
    }

    // Since we're only showing non-existing files, all selected items can be generated
    if (selected.length === 0) {
      vscode.window.showInformationMessage('没有新文档需要生成 / No new documents to generate.');
      return;
    }

    for (const item of selected) {
      const relPath = item.label;
      const fullPath = path.join(targetDir, relPath);

      // Create directory if it doesn't exist
      fs.mkdirSync(path.dirname(fullPath), { recursive: true });

      // Write file
      fs.writeFileSync(fullPath, templates[relPath], 'utf8');

      Logger.log(`Generated document: ${fullPath}`);
    }

    vscode.window.showInformationMessage(`已在 ${displayPath} 生成文档骨架 / Documentation created at ${displayPath}`);
  } catch (error) {
    Logger.error(`Error generating documentation: ${error}`);
    vscode.window.showErrorMessage(`Failed to generate documentation: ${error}`);
  }
}

/**
 * Register the document generation command
 * @param context The extension context
 */
export function registerDocGenCommand(context: vscode.ExtensionContext): void {
  // Register the main document generation command
  const disposable = vscode.commands.registerCommand('employeeZero.generateDocsStructure', async (uri: vscode.Uri) => {
    if (!uri) {
      // If no URI is provided, use the first workspace folder
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
      if (!workspaceFolder) {
        vscode.window.showErrorMessage('No folder selected and no workspace folder available.');
        return;
      }
      uri = vscode.Uri.file(workspaceFolder);
    }

    // Get file stats to determine if it's a file or directory
    const stats = fs.statSync(uri.fsPath);

    // Determine the target directory
    let targetDir: string;
    if (stats.isFile()) {
      // If a file is selected, use its parent directory
      targetDir = path.dirname(uri.fsPath);
    } else {
      // If a directory is selected, use it directly
      targetDir = uri.fsPath;
    }

    // Check if we're in a docs directory or its subdirectory
    const pathParts = targetDir.split(path.sep);
    const docsIndex = pathParts.indexOf('docs');

    if (docsIndex !== -1) {
      // If in docs directory or subdirectory, use parent of docs directory
      targetDir = pathParts.slice(0, docsIndex).join(path.sep);
    }

    await generateDocumentationStructure(targetDir);
  });

  // Register template editor commands
  registerTemplateEditorCommands(context);

  context.subscriptions.push(disposable);
}
