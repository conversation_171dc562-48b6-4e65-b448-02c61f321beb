/**
 * Template editor utility for document generation service
 * Provides commands to edit document templates in a more user-friendly way
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * Register template editor commands
 * @param context The extension context
 */
export function registerTemplateEditorCommands(context: vscode.ExtensionContext): void {
  // Register command to edit a template
  const editTemplateDisposable = vscode.commands.registerCommand('employeeZero.editDocTemplate', async () => {
    await editTemplate();
  });

  // Register command to create a new template
  const newTemplateDisposable = vscode.commands.registerCommand('employeeZero.newDocTemplate', async () => {
    await createNewTemplate();
  });

  context.subscriptions.push(editTemplateDisposable, newTemplateDisposable);
}

/**
 * Edit an existing template
 */
async function editTemplate(): Promise<void> {
  try {
    // Get current templates from configuration
    const config = vscode.workspace.getConfiguration('employeeZero');
    const templates = config.get<{ [key: string]: string }>('docgen.templates');

    if (!templates || Object.keys(templates).length === 0) {
      vscode.window.showInformationMessage('没有可用的模板 / No templates available.');
      return;
    }

    // Let user select a template to edit
    const templateKeys = Object.keys(templates);
    const selectedTemplate = await vscode.window.showQuickPick(templateKeys, {
      placeHolder: '选择要编辑的模板 / Select a template to edit'
    });

    if (!selectedTemplate) {
      return;
    }

    // Create a temporary file with the template content
    const tempFile = await createTempFileWithContent(selectedTemplate, templates[selectedTemplate]);

    // Open the temporary file in the editor
    const document = await vscode.workspace.openTextDocument(tempFile);
    await vscode.window.showTextDocument(document);

    // Register a file system watcher to detect when the file is saved
    const watcher = vscode.workspace.createFileSystemWatcher(tempFile);

    // When the file is saved, update the template in settings
    const saveListener = watcher.onDidChange(async () => {
      const updatedContent = fs.readFileSync(tempFile, 'utf8');
      await updateTemplateInSettings(selectedTemplate, updatedContent);
    });

    // When the editor is closed, clean up
    const closeListener = vscode.window.onDidChangeVisibleTextEditors(async (editors) => {
      if (!editors.some(e => e.document.uri.fsPath === tempFile)) {
        // Editor for this file was closed
        saveListener.dispose();
        closeListener.dispose();
        watcher.dispose();

        // Ask if user wants to keep changes
        const keepChanges = await vscode.window.showQuickPick(
          ['Yes', 'No'],
          { placeHolder: '保存更改到设置? / Save changes to settings?' }
        );

        if (keepChanges === 'Yes') {
          const finalContent = fs.readFileSync(tempFile, 'utf8');
          await updateTemplateInSettings(selectedTemplate, finalContent);
        }

        // Delete the temporary file
        try {
          fs.unlinkSync(tempFile);
        } catch (error) {
          console.error('Failed to delete temporary file:', error);
        }
      }
    });
  } catch (error) {
    vscode.window.showErrorMessage(`编辑模板时出错: ${error.message} / Error editing template: ${error.message}`);
  }
}

/**
 * Create a new template
 */
async function createNewTemplate(): Promise<void> {
  try {
    // Ask for template name
    const templateName = await vscode.window.showInputBox({
      prompt: '输入新模板的文件路径 / Enter the file path for the new template',
      placeHolder: 'e.g., docs/custom.md'
    });

    if (!templateName) {
      return;
    }

    // Create a temporary file with empty content
    const tempFile = await createTempFileWithContent(templateName, '');

    // Open the temporary file in the editor
    const document = await vscode.workspace.openTextDocument(tempFile);
    await vscode.window.showTextDocument(document);

    // Register a file system watcher to detect when the file is saved
    const watcher = vscode.workspace.createFileSystemWatcher(tempFile);

    // When the file is saved, update the template in settings
    const saveListener = watcher.onDidChange(async () => {
      const updatedContent = fs.readFileSync(tempFile, 'utf8');
      await updateTemplateInSettings(templateName, updatedContent);
    });

    // When the editor is closed, clean up
    const closeListener = vscode.window.onDidChangeVisibleTextEditors(async (editors) => {
      if (!editors.some(e => e.document.uri.fsPath === tempFile)) {
        // Editor for this file was closed
        saveListener.dispose();
        closeListener.dispose();
        watcher.dispose();

        // Ask if user wants to keep changes
        const keepChanges = await vscode.window.showQuickPick(
          ['Yes', 'No'],
          { placeHolder: '保存新模板到设置? / Save new template to settings?' }
        );

        if (keepChanges === 'Yes') {
          const finalContent = fs.readFileSync(tempFile, 'utf8');
          await updateTemplateInSettings(templateName, finalContent);
        }

        // Delete the temporary file
        try {
          fs.unlinkSync(tempFile);
        } catch (error) {
          console.error('Failed to delete temporary file:', error);
        }
      }
    });
  } catch (error) {
    vscode.window.showErrorMessage(`创建模板时出错: ${error.message} / Error creating template: ${error.message}`);
  }
}

/**
 * Create a temporary file with the given content
 * @param templateName The name of the template
 * @param content The content of the template
 * @returns The path to the temporary file
 */
async function createTempFileWithContent(templateName: string, content: string): Promise<string> {
  // Create a temporary file
  const tempDir = os.tmpdir();
  const fileName = `${path.basename(templateName)}_${Date.now()}.md`;
  const tempFile = path.join(tempDir, fileName);

  // Write the content to the file
  fs.writeFileSync(tempFile, content);

  return tempFile;
}

/**
 * Update a template in the settings
 * @param templateName The name of the template
 * @param content The new content of the template
 */
async function updateTemplateInSettings(templateName: string, content: string): Promise<void> {
  try {
    // Get current templates from configuration
    const config = vscode.workspace.getConfiguration('employeeZero');
    const templates = config.get<{ [key: string]: string }>('docgen.templates') || {};

    // Update the template
    templates[templateName] = content;

    // Save the updated templates
    await config.update('docgen.templates', templates, vscode.ConfigurationTarget.Global);

    vscode.window.showInformationMessage(`模板 "${templateName}" 已更新 / Template "${templateName}" has been updated.`);
  } catch (error) {
    vscode.window.showErrorMessage(`更新模板设置时出错: ${error.message} / Error updating template settings: ${error.message}`);
  }
}
