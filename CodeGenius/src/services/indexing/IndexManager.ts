import * as vscode from 'vscode'
import { LanceDbIndex } from './lanceDbIndex'
import { ExtensionMessage, IndexingProgressUpdate } from '@shared/ExtensionMessage'

export class IndexManager {
  private lanceDbIndex: LanceDbIndex
  private isIndexing: boolean = false
  private indexingCancellationController?: AbortController
  private pauseToken = { paused: false }
  private postMessage: (message: ExtensionMessage) => Promise<void>
  private lastProgressUpdate: IndexingProgressUpdate | null = null

  constructor(postMessage: (message: ExtensionMessage) => Promise<void>) {
    this.lanceDbIndex = new LanceDbIndex()
    this.postMessage = postMessage
  }

  async startIndexing(workspaceDir: string, pauseOnStart: boolean = false): Promise<void> {
    if (this.isIndexing) {
      console.log('[IndexManager] Indexing already in progress, skipping new request')
      return
    }

    this.isIndexing = true

    try {
      if (pauseOnStart) {
        this.pauseToken.paused = true
        await this.postMessage({
          type: "indexProgress",
          indexProgress: {
            progress: 0,
            desc: "Initial Indexing Skipped",
            status: "paused",
          },
        })
        // Reset indexing state since we're not actually indexing
        this.isIndexing = false
        return
      }

      await this.refreshCodebaseIndex(workspaceDir)
    } catch (error) {
      // Ensure state is reset if an error occurs before refreshCodebaseIndex
      this.isIndexing = false
      throw error
    }
  }

  private async refreshCodebaseIndex(workspaceDir: string): Promise<void> {
    if (this.indexingCancellationController) {
      this.indexingCancellationController.abort()
    }
    this.indexingCancellationController = new AbortController()

    try {
      for await (const update of this.indexWorkspace(workspaceDir, this.indexingCancellationController.signal)) {
        // Only send progress update if it's different from the last one
        if (!this.lastProgressUpdate ||
          this.lastProgressUpdate.progress !== update.progress ||
          this.lastProgressUpdate.desc !== update.desc ||
          this.lastProgressUpdate.status !== update.status) {

          this.lastProgressUpdate = update;
          await this.postMessage({
            type: "indexProgress",
            indexProgress: update,
          })
        }

        if (update.status === "failed") {
          console.error('[IndexManager] Indexing failed:', update.desc)
        }
      }
    } catch (e: any) {
      console.error(`[IndexManager] Failed refreshing codebase index: ${e}`)
      await this.handleIndexingError(e)
    } finally {
      this.isIndexing = false
      this.indexingCancellationController = undefined
      this.lastProgressUpdate = null
    }
  }

  private async *indexWorkspace(
    workspaceDir: string,
    abortSignal: AbortSignal,
  ): AsyncGenerator<IndexingProgressUpdate> {
    // Check if indexing is disabled
    const config = vscode.workspace.getConfiguration('employeeZero')
    const enableCodebaseIndexing = false

    if (!enableCodebaseIndexing) {
      yield {
        progress: 0,
        desc: "代码库索引已禁用",
        status: "disabled",
      }
      return
    }

    yield {
      progress: 0,
      desc: "Starting indexing...",
      status: "loading",
    }

    const beginTime = Date.now()

    try {
      if (abortSignal.aborted) {
        yield {
          progress: 0,
          desc: "Indexing cancelled",
          status: "cancelled",
        }
        return
      }

      if (this.pauseToken.paused) {
        yield* this.yieldUpdateAndPause()
      }

      // Perform the actual indexing with real-time progress updates
      const progressGenerator = this.indexDirectoryWithProgress(workspaceDir, abortSignal);
      for await (const update of progressGenerator) {
        yield update;
      }

    } catch (err) {
      yield this.createErrorUpdate(err)
      return
    }

    yield {
      progress: 1,
      desc: "Indexing Complete",
      status: "done",
    }

    const duration = Date.now() - beginTime
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    console.log(`[IndexManager] Indexing completed: 100.0% - ${minutes}m ${seconds}s`)
  }

  private async *indexDirectoryWithProgress(
    directory: string,
    abortSignal: AbortSignal
  ): AsyncGenerator<IndexingProgressUpdate> {
    let isCompleted = false;
    let currentUpdate: IndexingProgressUpdate | null = null;

    // Start indexing process
    const indexingPromise = this.lanceDbIndex.initializeIndex(directory, (update: IndexingProgressUpdate) => {
      currentUpdate = update;
      if (update.status === "done" || update.status === "failed" || update.status === "disabled") {
        isCompleted = true;
      }
    });

    // Periodically yield current progress
    while (!isCompleted) {
      if (abortSignal.aborted) {
        yield {
          progress: 0,
          desc: "Indexing cancelled",
          status: "cancelled",
        };
        return;
      }

      if (currentUpdate) {
        yield currentUpdate;
      }

      await new Promise(resolve => setTimeout(resolve, 100));
    }

    try {
      await indexingPromise;
    } catch (error) {
      console.error('[IndexManager] Indexing process error:', error);
      yield this.createErrorUpdate(error);
      return;
    }

    // Send final update
    if (currentUpdate) {
      yield currentUpdate;
    }
  }

  private async *yieldUpdateAndPause(): AsyncGenerator<IndexingProgressUpdate> {
    yield {
      progress: 0,
      desc: "Indexing paused",
      status: "paused",
    }

    // Wait until unpaused
    while (this.pauseToken.paused) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    yield {
      progress: 0,
      desc: "Resuming indexing...",
      status: "indexing",
    }
  }

  private createErrorUpdate(err: any): IndexingProgressUpdate {
    const errorMessage = err instanceof Error ? err.message : String(err)
    console.error('[IndexManager] Indexing error:', errorMessage)

    return {
      progress: 0,
      desc: `Indexing failed: ${errorMessage}`,
      status: "failed",
    }
  }

  private async handleIndexingError(error: any): Promise<void> {
    const errorUpdate = this.createErrorUpdate(error)
    await this.postMessage({
      type: "indexProgress",
      indexProgress: errorUpdate,
    })
  }

  pauseIndexing(): void {
    this.pauseToken.paused = true
  }

  resumeIndexing(): void {
    this.pauseToken.paused = false
  }

  cancelIndexing(): void {
    if (this.indexingCancellationController) {
      this.indexingCancellationController.abort()
    }
  }


}
