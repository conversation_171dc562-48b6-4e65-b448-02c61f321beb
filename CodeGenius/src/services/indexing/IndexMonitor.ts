import { IndexingProgressUpdate } from '@shared/ExtensionMessage';

interface IndexingMetrics {
    lastUpdateTime: number;
    currentProgress: number;
    status: string;
}

export class IndexMonitor {
    private metrics: IndexingMetrics;
    private progressCallback?: (update: IndexingProgressUpdate) => void;
    private monitorInterval?: NodeJS.Timeout;
    private readonly STUCK_THRESHOLD = 60000; // 1 minute
    private readonly HEARTBEAT_INTERVAL = 5000; // 5 seconds
    private stuckCallback?: () => void; // Callback to handle stuck situations
    private hasReportedStuck = false; // Prevent multiple stuck reports

    constructor() {
        this.metrics = this.initializeMetrics();
    }

    private initializeMetrics(): IndexingMetrics {
        return {
            lastUpdateTime: Date.now(),
            currentProgress: 0,
            status: 'initializing'
        };
    }

    startMonitoring(
        progressCallback?: (update: IndexingProgressUpdate) => void,
        stuckCallback?: () => void
    ) {
        this.progressCallback = progressCallback;
        this.stuckCallback = stuckCallback;
        this.metrics = this.initializeMetrics();
        this.hasReportedStuck = false;

        // Start monitoring interval
        this.monitorInterval = setInterval(() => {
            this.checkProgress();
        }, this.HEARTBEAT_INTERVAL);

        console.log('[IndexMonitor] Started monitoring indexing process');
    }

    stopMonitoring() {
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = undefined;
        }
        console.log('[IndexMonitor] Stopped monitoring indexing process');
    }

    updateProgress(update: IndexingProgressUpdate) {
        this.metrics.lastUpdateTime = Date.now();
        this.metrics.currentProgress = update.progress;
        this.metrics.status = update.status;

        // Reset stuck flag when we get progress updates
        if (this.hasReportedStuck && update.status === 'indexing') {
            this.hasReportedStuck = false;
            console.log('[IndexMonitor] Indexing resumed after being stuck');
        }

        // Forward to callback if provided
        if (this.progressCallback) {
            this.progressCallback(update);
        }
    }

    private checkProgress() {
        const currentTime = Date.now();
        const timeSinceLastUpdate = currentTime - this.metrics.lastUpdateTime;

        // Check if indexing appears stuck
        if (timeSinceLastUpdate > this.STUCK_THRESHOLD &&
            this.metrics.status === 'indexing' &&
            !this.hasReportedStuck) {

            this.hasReportedStuck = true;
            console.warn(`[IndexMonitor] Indexing appears stuck for ${timeSinceLastUpdate / 1000}s`);

            // Report stuck status
            if (this.progressCallback) {
                this.progressCallback({
                    progress: this.metrics.currentProgress,
                    desc: `Indexing appears stuck (no progress for ${Math.floor(timeSinceLastUpdate / 1000)}s)`,
                    status: 'failed'
                });
            }

            // Call stuck callback to handle the situation
            if (this.stuckCallback) {
                console.log('[IndexMonitor] Calling stuck callback to handle stuck situation');
                this.stuckCallback();
            }
        }

        // Provide heartbeat for long-running processes (but not if stuck)
        if (this.metrics.status === 'indexing' &&
            timeSinceLastUpdate > this.HEARTBEAT_INTERVAL * 2 &&
            timeSinceLastUpdate < this.STUCK_THRESHOLD) {

            if (this.progressCallback) {
                this.progressCallback({
                    progress: this.metrics.currentProgress,
                    desc: `💓 Processing... (heartbeat)`,
                    status: 'indexing'
                });
            }
        }
    }
}

// Singleton instance for global use
export const indexMonitor = new IndexMonitor();
