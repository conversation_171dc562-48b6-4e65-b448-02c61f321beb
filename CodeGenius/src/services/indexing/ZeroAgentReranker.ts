import axios from 'axios';
import { RERANK_MODEL_URL } from './constant';

export class ZeroAgentReranker {
    private url: string;

    constructor(url: string = RERANK_MODEL_URL) {
        this.url = url;
    }

    async rerank(data: string[][]) {
        const query = {
            'query_and_nodes': data
        };
        try {
            const response = await axios.post(this.url, query, { timeout: 10000 });
            return response.data.scores;
        } catch (error) {
            console.debug('Error fetching embedding:', error);
            const errorData = Array.isArray(data) ? new Array(data.length).fill(0) : [];
            return errorData;
        }
    }
}

async function rerankerTest() {
    const rerank = new ZeroAgentReranker();
    const data = [["aaaa", "aaa"], ["aaaa", "cccc"], ["aaaa", "dddd"]];
    const result = await rerank.rerank(data);
    console.log("result:", result);
}