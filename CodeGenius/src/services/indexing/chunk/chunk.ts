import { CODE_INDEXING_EXTENSIONS, KNOWLEDGE_INDEXING_EXTENSIONS } from "../util/paths.js";
import { getUriFileExtension, getUriPathBasename } from "../util/uri.js";
import { codeChunker } from "./code.js";

export interface CodeChunk {
  filePath: string;
  content: string;
  startLine: number;
  endLine: number;
  isKnowledge?: boolean;
  vector?: number[];
  [key: string]: any;
}

export function shouldChunk(fileUri: string, contents: string): boolean {
  if (contents.length > 1000000) {
    // if a file has more than 1m characters then skip it
    return false;
  }
  if (contents.length === 0) {
    return false;
  }
  const baseName = getUriPathBasename(fileUri);
  return baseName.includes(".");
}

async function genToArr<T>(generator: AsyncGenerator<T>): Promise<T[]> {
  const result: T[] = [];
  for await (const item of generator) {
    result.push(item);
  }
  return result;
}

export async function genToStrs(
  generator: AsyncGenerator<CodeChunk>,
): Promise<CodeChunk[]> {
  const chunks = await genToArr(generator);
  return chunks;
}

export async function countLinesAsync(content: string): Promise<number> {
  const lines = content.split('\n');
  const lineCount = lines.length;
  return lineCount;
}

export function extractInterfaceDescription(filePath: string, content: string): CodeChunk[] {
  const lines = content.split('\n');
  let isTargetSection = false;
  let resultLines: string[] = [];
  for (const line of lines) {
      if (line.trim().startsWith('#') && line.trim().split(/\s+/).slice(1).join(' ') === '接口功能描述') {
          isTargetSection = true;
          continue;
      }
      if (isTargetSection && line.trim().startsWith('# ') && !line.trim().startsWith('##')) {
          break;
      }

      if (isTargetSection) {
          resultLines.push(line);
      }
  }

  while (resultLines.length > 0 && resultLines[resultLines.length - 1].trim() === '') {
      resultLines.pop();
  }
  const interfaceDescription:string = resultLines.join('\n').trim();
  const chunk: CodeChunk = {
    filePath: filePath,
    content: interfaceDescription,
    startLine: 0,
    endLine: lines.length - 1,
    isKnowledge: true
  }
  return [chunk];
}

export async function chunker(
  filePath: string,
  content: string,
  maxLines: number
  ): Promise<CodeChunk[]> {
  const extension = "." + getUriFileExtension(filePath);
  if (CODE_INDEXING_EXTENSIONS.includes(extension)) {
    const generator = codeChunker(filePath, content, maxLines);
    const chunks = await genToStrs(generator);
    for (const chunk of chunks) {
      chunk.isKnowledge = false;
    }
    return chunks;
  }
  else if (KNOWLEDGE_INDEXING_EXTENSIONS.includes(extension)) {
    return extractInterfaceDescription(filePath, content);
  }
  return [];
}