import { SyntaxNode } from "web-tree-sitter";

import { CodeChunk } from "./chunk";
import { getParserForFile } from "../util/treeSitter";
import { countLinesAsync } from "./chunk";
import { MAX_CHUNK_LINES } from "../lanceDbIndex";

function chunkFunctionBody(node: SyntaxNode): string[] {
  const text: string[] = node.text.split("\n");
  const textLen = text.length;
  let currentLine = 0;
  const bodyContents: string[] = [];
  const chunkSize = MAX_CHUNK_LINES;
  while (currentLine < textLen) {
      let endLine = Math.min(currentLine + chunkSize, textLen);
      if ((textLen - endLine) < MAX_CHUNK_LINES/2) {
        endLine = textLen;
      }
      const chunk = text.slice(currentLine, endLine).join("\n");
      bodyContents.push(chunk);
      currentLine = endLine;
  }
  return bodyContents;
}

function countInvalidLines(code: string): number {
  const lines = code.split('\n');
  let count = 0;
  for (const line of lines) {
    if ((line.trim() === '') || (checkAiCoderLabel(line))) {
      count++;
    }
  }
  return count;
}

function deleteInvalidLines(code: string): string {
  let lines = code.split("\n");
  let firstInvalidLineIndex = -1;
  for (let i = lines.length - 1; i >= 0; i--) {
    if ((lines[i].trim() === "") || (checkAiCoderLabel(lines[i]))) {
      if (firstInvalidLineIndex < 0) {
        firstInvalidLineIndex = i;
      }
    }
    else {
      if (firstInvalidLineIndex - i > 1) {
        // Remove the lines
        lines = [
          ...lines.slice(0, i + 1),
          ...lines.slice(firstInvalidLineIndex + 1),
        ];
      }
      firstInvalidLineIndex = -1;
    }
  }
  code = lines.join("\n");
  return code;
}

function checkAiCoderLabel(code: string): boolean {
  if (code.includes("# Started by AICoder") 
    || (code.includes("# Ended by AICoder"))) {
    return true;
  }
  return false;
}

function firstChild(
  node: SyntaxNode,
  grammarName: string | string[],
): SyntaxNode | null {
  if (Array.isArray(grammarName)) {
    return (
      node.children.find((child) => grammarName.includes(child.type)) || null
    );
  }
  return node.children.find((child) => child.type === grammarName) || null;
}

async function collapseChildren(
  node: SyntaxNode,
  code: string,
  blockTypes: string[],
  collapseTypes: string[],
  collapseBlockTypes: string[],
  maxLines: number,
): Promise<string[]> {
  code = code.slice(0, node.endIndex);
  const block = firstChild(node, blockTypes);
  const collapsedChildren: string[] = [];

  if (block) {
    const childrenToCollapse = block.children.filter((child) =>
      collapseTypes.includes(child.type),
    );
    for (const child of childrenToCollapse.reverse()) {
      const grandChild = firstChild(child, collapseBlockTypes);
      if (grandChild) {
        const start = grandChild.startIndex;
        const end = grandChild.endIndex;
        const collapsedChild = code.slice(child.startIndex, start);
        code = code.slice(0, start) + code.slice(end);
        collapsedChildren.unshift(collapsedChild);
      }
    }
  }
  code = code.slice(node.startIndex);
  let invalidLines = countInvalidLines(code);
  while (
    (await countLinesAsync(code.trim())) > (maxLines + invalidLines) &&
    collapsedChildren.length > 0
  ) {
    // Remove children starting at the end - TODO: Add multiple chunks so no children are missing
    const childCode = collapsedChildren.pop()!;
    const index = code.lastIndexOf(childCode);
    if (index > 0) {
      code = code.slice(0, index) + code.slice(index + childCode.length);
    }
  }
  code = deleteInvalidLines(code);
  return [code];
}

export const CLASS_BLOCK_NODE_TYPES = ["block", "class_body", "declaration_list"];
export const CLASS_DECLARATION_NODE_TYPEs = ["class_definition", "impl_item", "class_declaration"];
export const FUNCTION_BLOCK_NODE_TYPES = ["block", "statement_block"];
export const FUNCTION_DECLARATION_NODE_TYPEs = [
  "method_definition",
  "function_definition",
  "function_item",
  "function_declaration",
  "method_declaration",
];

async function constructClassDefinitionChunk(
  node: SyntaxNode,
  code: string,
  maxLines: number,
): Promise<string[]> {
  return collapseChildren(
    node,
    code,
    CLASS_BLOCK_NODE_TYPES,
    FUNCTION_DECLARATION_NODE_TYPEs,
    FUNCTION_BLOCK_NODE_TYPES,
    maxLines,
  );
}

async function constructFunctionDefinitionChunk(
  node: SyntaxNode,
  code: string,
  maxLines: number,
): Promise<string[]> {
  const bodyNode = node.children[node.children.length - 1];
  let fucnSign = code.slice(node.startIndex, bodyNode.startIndex);
  const funcContents: string[] = chunkFunctionBody(bodyNode);
  if (
    node.parent &&
    CLASS_BLOCK_NODE_TYPES.includes(node.parent.type) &&
    node.parent.parent &&
    CLASS_DECLARATION_NODE_TYPEs.includes(node.parent.parent.type)
  ) {
    // If inside a class, include the class header
    const classNode = node.parent.parent;
    const classBlock = node.parent;
    fucnSign =  `${code.slice(
      classNode.startIndex,
      classBlock.startIndex,
    )}...\n\n${" ".repeat(node.startPosition.column)}${fucnSign}`;
  }
  const funcText: string[] = [];
  for (let i=0;i<funcContents.length;i++) {
    const chunk = fucnSign + funcContents[i];
    funcText.push(chunk);
  }
  return funcText;
}

const collapsedNodeConstructors: {
  [key: string]: (
    node: SyntaxNode,
    code: string,
    maxLines: number,
  ) => Promise<string[]>;
} = {
  // Classes, structs, etc
  class_definition: constructClassDefinitionChunk,
  class_declaration: constructClassDefinitionChunk,
  impl_item: constructClassDefinitionChunk,
  // Functions
  function_definition: constructFunctionDefinitionChunk,
  function_declaration: constructFunctionDefinitionChunk,
  function_item: constructFunctionDefinitionChunk,
  // Methods
  method_declaration: constructFunctionDefinitionChunk,
  method_definition: constructFunctionDefinitionChunk,
  // Properties
};

async function maybeYieldChunk(
  node: SyntaxNode,
  filePath: string,
  code: string,
  maxLines: number,
  root = true,
): Promise<CodeChunk | undefined> {
  if (FUNCTION_DECLARATION_NODE_TYPEs.includes(node.type)) {
    return undefined;
  }
  // Keep entire text if not over size
  if (root || node.type in collapsedNodeConstructors) {
    const lines = await countLinesAsync(node.text);
    if (lines < maxLines) {
      return {
        filePath: filePath,
        content: node.text,
        startLine: node.startPosition.row,
        endLine: node.endPosition.row,
      };
    }
  }
  return undefined;
}

async function* getSmartCollapsedChunks(
  node: SyntaxNode,
  filePath: string,
  code: string,
  maxLines: number,
  root = true,
): AsyncGenerator<CodeChunk> {
  const chunk = await maybeYieldChunk(node, filePath, code, maxLines, root);
  if (chunk) {
    yield chunk;
    return;
  }
  // If a collapsed form is defined, use that
  if (node.type in collapsedNodeConstructors) {
    const contents: string[] = await collapsedNodeConstructors[node.type](
      node,
      code,
      maxLines,
    );
    for (let i=0;i<contents.length;i++) {
      yield {
        filePath: filePath,
        content: contents[i],
        startLine: node.startPosition.row,
        endLine: node.endPosition.row,
      };
    }
  }
  if (FUNCTION_DECLARATION_NODE_TYPEs.includes(node.type)) {
    return;
  }
  // Recurse (because even if collapsed version was shown, want to show the children in full somewhere)
  const generators = node.children.map((child) =>
    getSmartCollapsedChunks(child, filePath, code, maxLines, false),
  );
  for (const generator of generators) {
    yield* generator;
  }
}

export async function* codeChunker(
  filepath: string,
  contents: string,
  maxLines: number,
): AsyncGenerator<CodeChunk> {
  if (contents.trim().length === 0) {
    return;
  }

  const parser = await getParserForFile(filepath);
  if (parser === undefined) {
    throw new Error(`Failed to load parser for file ${filepath}: `);
  }
  const tree = parser.parse(contents);

  yield* getSmartCollapsedChunks(tree.rootNode, filepath, contents, maxLines);
}
