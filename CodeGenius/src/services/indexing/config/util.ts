import fs from "fs";
import os from "os";

/**
 * This check is to determine if the user is on an unsupported CPU
 * target for our Lance DB binaries.
 *
 * See here for details: https://github.com/continuedev/continue/issues/940
 */
export function isSupportedLanceDbCpuTargetForLinux() {
  const CPU_FEATURES_TO_CHECK = ["avx2", "fma"] as const;

  const arch = os.arch();

  // This check only applies to x64
  // https://github.com/lancedb/lance/issues/2195#issuecomment-2057841311
  if (arch !== "x64") {
    return true;
  }

  try {
    const cpuFlags = fs.readFileSync("/proc/cpuinfo", "utf-8").toLowerCase();

    const isSupported = cpuFlags
      ? CPU_FEATURES_TO_CHECK.every((feature) => cpuFlags.includes(feature))
      : true;

    if (!isSupported) {
      console.warn("Codebase indexing disabled - Your Linux system lacks required CPU features (AVX2, FMA)");
    }

    return isSupported;
  } catch (error) {
    return true;
  }
}