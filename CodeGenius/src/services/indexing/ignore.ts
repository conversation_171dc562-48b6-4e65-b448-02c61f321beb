import ignore from "ignore";

export const DEFAULT_IGNORE_FILETYPES = [
  "*.DS_Store",
  "*-lock.json",
  "*.lock",
  "*.log",
  "*.ttf",
  "*.png",
  "*.jpg",
  "*.jpeg",
  "*.gif",
  "*.mp4",
  "*.svg",
  "*.ico",
  "*.pdf",
  "*.zip",
  "*.gz",
  "*.tar",
  "*.dmg",
  "*.tgz",
  "*.rar",
  "*.7z",
  "*.exe",
  "*.dll",
  "*.obj",
  "*.o",
  "*.o.d",
  "*.a",
  "*.lib",
  "*.so",
  "*.dylib",
  "*.ncb",
  "*.sdf",
  "*.woff",
  "*.woff2",
  "*.eot",
  "*.cur",
  "*.avi",
  "*.mpg",
  "*.mpeg",
  "*.mov",
  "*.mp3",
  "*.mp4",
  "*.mkv",
  "*.mkv",
  "*.webm",
  "*.jar",
  "*.onnx",
  "*.parquet",
  "*.pqt",
  "*.wav",
  "*.webp",
  "*.db",
  "*.sqlite",
  "*.wasm",
  "*.plist",
  "*.profraw",
  "*.gcda",
  "*.gcno",
  "go.sum",
  "*.env",
  "*.gitignore",
  "*.gitkeep",
  "*.continueignore",
  "config.json",
  "config.yaml",
  "*.csv",
  "*.uasset",
  "*.pdb",
  "*.bin",
  "*.pag",
  "*.swp",
  "*.jsonl",
];
export const defaultIgnoreFile = ignore().add(DEFAULT_IGNORE_FILETYPES);

export const DEFAULT_IGNORE_DIRS = [
  ".git/",
  ".svn/",
  ".vscode/",
  ".idea/",
  ".vs/",
  "venv/",
  ".venv/",
  "env/",
  ".env/",
  "node_modules/",
  "dist/",
  "build/",
  "Build/",
  "target/",
  "out/",
  "bin/",
  ".pytest_cache/",
  ".vscode-test/",
  ".continue/",
  "__pycache__/",
  "site-packages/",
  ".gradle/",
  ".mvn/",
  ".cache/",
  "gems/",
  "vendor/",
  //"test/",
  //"tests/"
];
export const defaultIgnoreDir = ignore().add(DEFAULT_IGNORE_DIRS);

export const IGNORE_TESTFILETYPES = [
  "ft_*.cpp", "ut_*.cpp"
];
export const ignoreTestFileTypes = ignore().add(IGNORE_TESTFILETYPES);

export const IGNORE_TEST_DIRS = [
  "test", "tests", "__tests__",
  "spec", "specs", "testing",
  "Test", "Tests", "TestCases"
];
export const ignoreTestDirs = ignore().add(IGNORE_TEST_DIRS);

export const DEFAULT_IGNORE =
  DEFAULT_IGNORE_FILETYPES.join("\n") + "\n" +
  IGNORE_TESTFILETYPES.join("\n") + "\n" +
  DEFAULT_IGNORE_DIRS.join("\n");

export const defaultIgnoreFileAndDir = ignore()
  .add(defaultIgnoreFile)
  .add(ignoreTestFileTypes)
  .add(defaultIgnoreDir);
