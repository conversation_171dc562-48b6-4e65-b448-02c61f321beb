import * as fs from 'fs';
import * as path from 'path';
import * as os from "os";
import * as vscode from "vscode"

import { connect, Connection, LocalTable, VectorIndexParams, WriteMode } from "vectordb";
import { chunker, CodeChunk } from './chunk/chunk';
import { ZeroAgentEmbedding } from './ZeroAgentEmbedding';
import { calculateFileHash, FileMetadata, getCodeFilesInDirectory, getDeleteFiles, getNeedIndexingFiles, readFileByLine } from './util/paths';
import { ZeroAgentReranker } from './ZeroAgentReranker';
import { readJsonFile, writeJsonFile } from './evaluation';
import { EMBED_VECTOR_LEN, RETRIEVE_TOPN } from './constant';
import { IndexingProgressUpdate } from '@shared/ExtensionMessage';
import { taskExtract } from './util/taskExtract';
import { isSupportedLanceDbCpuTargetForLinux } from './config/util';
import { indexMonitor } from './IndexMonitor';

export const MAX_CHUNK_LINES = 50;
export const MAX_CHUNK_BATCH = 50;
const MIN_ROWS_NEED_INDEX = 100000;
const ZERO_AGENT_GLOBAL_DIR = path.join(os.homedir(), ".zeroAgent");
const DEFAULT_DB_SAVE_DIR = path.join(ZERO_AGENT_GLOBAL_DIR, "index/lancedb");

function normalizeProgress(progress: number): number {
    const percentage = Math.round(progress * 100);
    return percentage / 100;
}

export interface IndexResult {
    repoName: string;
    fileNum: number;
    chunkDuration: number;
    embeddingDuration: number;
    insertDataDuration: number;
    duration: number;
    dataRows: number;
}

export async function createCodeChunks(
    chunks: CodeChunk[],
    embeddings: number[][]) {
    chunks.forEach((chunk, index) => {
        chunk.vector = embeddings[index];
        if (chunk.isKnowledge === undefined) {
            chunk.isKnowledge = false;
        }
    });
    return chunks;
}

function pathToSafeFilename(pathStr: string): string {
    return pathStr
        .replace(/[^a-zA-Z0-9_\-.]/g, '_')
        .replace(/^[^a-zA-Z_]/, '_');
}

export class LanceDbIndex {
    private db!: Connection;
    private embedModel: ZeroAgentEmbedding;
    private rerankModel: ZeroAgentReranker;
    private repoBaseDir!: string;
    private dbSaveDir: string;
    private codeTableName!: string;
    private codeTable!: LocalTable;
    // private knowledgeTableName!: string;
    // private knowledgeTable!: LocalTable;
    private fileIndexingInfo: Record<string, FileMetadata>;
    private result: IndexResult;

    constructor() {
        this.dbSaveDir = DEFAULT_DB_SAVE_DIR;
        this.embedModel = new ZeroAgentEmbedding();
        this.rerankModel = new ZeroAgentReranker();
        this.fileIndexingInfo = {};
        this.result = {
            repoName: '',
            fileNum: 0,
            chunkDuration: 0,
            embeddingDuration: 0,
            insertDataDuration: 0,
            duration: 0,
            dataRows: 0
        };
    }

    private getEnableCodebaseIndexing(): boolean {
        const config = vscode.workspace.getConfiguration('employeeZero');
        return false;
    }

    /**
     * Initialize database connection and basic properties
     * @param repoBaseDir Repository base directory
     * @returns true if connection successful, false otherwise
     */
    private async initializeConnection(repoBaseDir: string): Promise<boolean> {
        this.repoBaseDir = repoBaseDir;
        this.codeTableName = pathToSafeFilename(repoBaseDir);
        //this.knowledgeTableName = this.codeTableName + "_knowledge";
        this.result.repoName = this.codeTableName;
        console.log("[LanceDbIndex] 表名:", this.codeTableName);

        if (!isSupportedLanceDbCpuTargetForLinux()) {
            console.error("[LanceDbIndex] linux系统缺少运行lanceDB所需的CPU指令集(AVX2, FMA)");
            return false;
        }

        if (!this.db) {
            console.log("[LanceDbIndex] 连接数据库...");
            this.db = await connect(this.dbSaveDir);
            if (!this.db) {
                console.error("[LanceDbIndex] 数据库连接失败!");
                return false;
            }
            console.log("[LanceDbIndex] 数据库连接成功");
        }
        return true;
    }

    /**
     * Initialize for retrieval operations only
     * @param repoBaseDir Repository base directory
     * @returns true if initialization successful and index exists, false otherwise
     */
    async initializeForRetrieval(repoBaseDir: string): Promise<boolean> {
        console.log("[LanceDbIndex] 初始化检索环境，工作空间:", repoBaseDir);

        // Initialize connection
        if (!(await this.initializeConnection(repoBaseDir))) {
            return false;
        }

        // Check if index table exists
        if (!(await this.db.tableNames()).includes(this.codeTableName)) {
            console.log("[LanceDbIndex] 代码索引不存在，无法进行检索!");
            return false;
        }
        // if (!(await this.db.tableNames()).includes(this.knowledgeTableName)) {
        //     console.log("[LanceDbIndex] 显性化知识索引不存在，无法进行检索!");
        //     return false;
        // }

        // Open existing table
        if (!this.codeTable) {
            try {
                this.codeTable = await this.db.openTable(this.codeTableName) as LocalTable;
            } catch (error) {
                console.error("[LanceDbIndex] Open table error:", error);
            }
            if (!this.codeTable) {
                console.error(`[LanceDbIndex] 表 ${this.codeTableName} 打开失败!`);
                return false;
            }
        }
        // if (!this.knowledgeTable) {
        //     try {
        //         this.knowledgeTable = await this.db.openTable(this.knowledgeTableName) as LocalTable;
        //     } catch (error) {
        //         console.error("[LanceDbIndex] Open table error:", error);
        //     }
        //     if (!this.knowledgeTable) {
        //         console.error(`[LanceDbIndex] 表 ${this.knowledgeTableName} 打开失败!`);
        //         return false;
        //     }
        // }

        console.log("[LanceDbIndex] 检索环境初始化成功");
        return true;
    }

    /**
     * Initialize for indexing operations
     * @param repoBaseDir Repository base directory
     * @param onProgress Progress callback
     */
    async initializeIndex(repoBaseDir: string, onProgress?: (update: IndexingProgressUpdate) => void) {
        console.log("[LanceDbIndex] 开始索引流程，仓库路径:", repoBaseDir);

        // Initialize connection
        if (!(await this.initializeConnection(repoBaseDir))) {
            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(0),
                    desc: "数据库连接失败或系统不支持",
                    status: "failed"
                });
            }
            return;
        }
        // Check if table exists, create if not
        // if (!(await this.db.tableNames()).includes(this.knowledgeTableName)) {
        // }
        if (!(await this.db.tableNames()).includes(this.codeTableName)) {
            this.db.dropTable(this.codeTableName);
            //this.db.dropTable(this.knowledgeTableName);
            console.log(`[LanceDbIndex] 表 ${this.codeTableName} 不存在，开始全量索引...`);

            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(0.05),
                    desc: "初始化数据库连接...",
                    status: "loading"
                });
            }

            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(0.1),
                    desc: "创建索引表...",
                    status: "loading"
                });
            }

            await this.createTable();
            await this.fullIndexing(onProgress);
            await this.createVectorIndex();
            console.log("[LanceDbIndex] 全量索引完成，统计信息:", this.result);
        } else {
            // Open existing table
            try {
                this.codeTable = await this.db.openTable(this.codeTableName) as LocalTable;
                //this.knowledgeTable = await this.db.openTable(this.knowledgeTableName) as LocalTable;
            } catch (error) {
                console.error("[LanceDbIndex] Open table error:", error);
                if (onProgress) {
                    onProgress({
                        progress: normalizeProgress(0),
                        desc: "表打开失败",
                        status: "failed"
                    });
                }
                return;
            }
            if (!this.codeTable/*|| !this.knowledgeTable*/) {
                console.error("[LanceDbIndex] 表打开失败!");
                if (onProgress) {
                    onProgress({
                        progress: normalizeProgress(0),
                        desc: "表打开失败",
                        status: "failed"
                    });
                }
                return;
            }

            // For incremental updates, only send progress if there are files to process
            console.log("[LanceDbIndex] 开始增量更新...");
            await this.performIncrementalUpdate(onProgress);
        }
    }

    async createTable() {
        console.log("[LanceDbIndex] 开始创建表...");
        // Database connection should already be established in indexing() method
        const array: number[] = new Array(EMBED_VECTOR_LEN).fill(0);
        const exampleData: CodeChunk = {
            filePath: "example/path",
            content: "example",
            startLine: 1,
            endLine: 10,
            isKnowledge: false,
            vector: array
        };
        console.log("[LanceDbIndex] 创建表结构，向量维度:", EMBED_VECTOR_LEN);
        this.codeTable = await this.db.createTable(this.codeTableName, [exampleData], { writeMode: WriteMode.Overwrite }) as LocalTable;
        //this.knowledgeTable = await this.db.createTable(this.knowledgeTableName, [exampleData], { writeMode: WriteMode.Overwrite }) as LocalTable;
        if (!this.codeTable /*|| !this.knowledgeTable*/) {
            console.error("[LanceDbIndex] 表创建失败");
            return;
        }
        await this.deleteByFilepath(["example/path"]);
        console.log("[LanceDbIndex] 表创建成功:", this.codeTable.name);
    }

    async insertData(data: CodeChunk[]) {
        const startTime = performance.now();
        if (!this.db || !this.codeTable/*|| !this.knowledgeTable*/) {
            console.error("[LanceDbIndex] 数据库或表不存在!");
            return;
        }
        if (data.length === 0) {
            return;
        }
        this.checkFailEmbeddings(data);
        const codeData: CodeChunk[] = [...data.filter(item => !item.isKnowledge)];
        const knowledgeData: CodeChunk[] = [...data.filter(item => item.isKnowledge)];
        await this.codeTable.add(codeData);
        //await this.knowledgeTable.add(knowledgeData);
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.result.insertDataDuration += duration;
    }

    async fullIndexing(onProgress?: (update: IndexingProgressUpdate) => void) {
        console.log("[LanceDbIndex] 开始扫描代码文件...");

        let indexingAborted = false;

        // Start monitoring with stuck callback
        indexMonitor.startMonitoring(
            onProgress,
            () => {
                // Handle stuck situation
                console.error("[LanceDbIndex] 检测到索引卡住，正在中止...");
                indexingAborted = true;
            }
        );

        const codeFiles = await getCodeFilesInDirectory(this.repoBaseDir);
        console.log("[LanceDbIndex] 发现代码文件数量:", codeFiles.length);

        const progressUpdate = {
            progress: normalizeProgress(0.15),
            desc: `发现 ${codeFiles.length} 个代码文件，开始处理...`,
            status: "indexing" as const
        };

        indexMonitor.updateProgress(progressUpdate);

        try {
            await this.processFilesForEmbedding(
                codeFiles,
                (update) => indexMonitor.updateProgress(update),
                () => indexingAborted // Pass abort check function
            );
        } finally {
            indexMonitor.stopMonitoring();
        }
    }

    async processFilesForEmbedding(codeFiles: string[],
        onProgress?: (update: IndexingProgressUpdate) => void,
        shouldAbort?: () => boolean
    ) {
        console.log("[LanceDbIndex] 开始向量化处理...");
        const startTime = performance.now();
        let chunkBatch: CodeChunk[] = [];
        const chunksConcurrency: CodeChunk[][] = [];
        let totalChunks = 0;
        let processedChunks = 0;
        this.result.fileNum = codeFiles.length;

        try {
            for (let i = 0; i < codeFiles.length; i++) {
                // Check if indexing should be aborted
                if (shouldAbort && shouldAbort()) {
                    console.log("[LanceDbIndex] 由于检测卡住，索引中止");
                    if (onProgress) {
                        onProgress({
                            progress: normalizeProgress(0),
                            desc: "由于检测卡住，索引中止",
                            status: "failed"
                        });
                    }
                    return;
                }

                const current_file = codeFiles[i];
                const fileProgress = i / codeFiles.length;
                const overallProgress = 0.15 + (fileProgress * 0.7); // 15% - 85%

                if (!this.getEnableCodebaseIndexing()) {
                    if (onProgress) {
                        onProgress({
                            progress: normalizeProgress(overallProgress),
                            desc: `设置中已禁用索引`,
                            status: "disabled"
                        });
                    }
                    this.result.fileNum = i;
                    break;
                }

                if (onProgress) {
                    onProgress({
                        progress: normalizeProgress(overallProgress),
                        desc: `处理文件 ${i + 1}/${codeFiles.length}: ${path.basename(current_file)}`,
                        status: "indexing"
                    });
                }

                try {
                    const chunks = await this.splitFileIntoChunks(current_file);
                    if (chunks.length === 0) {
                        await this.createFileMetadata(current_file);
                        continue;
                    }
                    totalChunks += chunks.length;
                    chunkBatch.push(...chunks);
                    await this.createFileMetadata(current_file);

                    // Process batches with timeout protection
                    while (chunkBatch.length >= MAX_CHUNK_BATCH) {
                        chunksConcurrency.push(chunkBatch.slice(0, MAX_CHUNK_BATCH));
                        const contentsMap = chunksConcurrency.map(chunks => chunks.map(chunk => chunk.content))
                        if (contentsMap.length >= this.embedModel.getEmbeddingConcurrency()) {
                            // Add timeout protection for embedding process
                            const embeddingTimeout = 60000; // 1 minute timeout
                            const embeddingsMap = await Promise.race([
                                this.generateBatchEmbeddings(contentsMap),
                                new Promise<number[][][]>((_, reject) =>
                                    setTimeout(() => reject(new Error('Embedding timeout')), embeddingTimeout)
                                )
                            ]);
                            while (embeddingsMap.length > 0) {
                                const chunks = chunksConcurrency.pop();
                                const embeddings = embeddingsMap.pop();
                                if (chunks && embeddings && embeddings.length > 0) {
                                    const chunkDicts = await createCodeChunks(chunks, embeddings);
                                    await this.insertData(chunkDicts);
                                    processedChunks += chunks.length;
                                }
                            }
                        }
                        chunkBatch = chunkBatch.slice(MAX_CHUNK_BATCH);
                    }
                } catch (fileError) {
                    console.error(`[LanceDbIndex] 处理文件失败: ${current_file}`, fileError);
                    // Mark file as failed but continue processing
                    if (current_file in this.fileIndexingInfo) {
                        this.fileIndexingInfo[current_file].state = "fail";
                    }
                    continue;
                }
            }
        } catch (error) {
            console.error(`[LanceDbIndex] 向量化处理失败:`, error);
            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(0),
                    desc: `向量化处理失败: ${error.message}`,
                    status: "failed"
                });
            }
            throw error;
        }

        // 处理剩余的代码块
        if (chunkBatch.length > 0) {
            console.log(`[LanceDbIndex] 处理剩余代码块: ${chunkBatch.length}`);
            chunksConcurrency.push(chunkBatch);
        }

        if (chunksConcurrency.length > 0) {
            console.log(`[LanceDbIndex] 处理剩余批次: ${chunksConcurrency.length}`);
            try {
                const contentsMap = chunksConcurrency.map(chunks => chunks.map(chunk => chunk.content))

                // Add timeout protection for final batch processing
                const finalBatchTimeout = 60000; // 1 minute for final batch
                const embeddingsMap = await Promise.race([
                    this.generateBatchEmbeddings(contentsMap),
                    new Promise<number[][][]>((_, reject) =>
                        setTimeout(() => reject(new Error('Final batch timeout')), finalBatchTimeout)
                    )
                ]);
                while (embeddingsMap.length > 0) {
                    const chunks = chunksConcurrency.pop();
                    const embeddings = embeddingsMap.pop();
                    if (chunks && embeddings && embeddings.length > 0) {
                        const chunkDicts = await createCodeChunks(chunks, embeddings);
                        await this.insertData(chunkDicts);
                        processedChunks += chunks.length;
                    }
                }
            } catch (finalBatchError) {
                console.error(`[LanceDbIndex] 最终批次处理失败:`, finalBatchError);
                if (onProgress) {
                    onProgress({
                        progress: normalizeProgress(0.8),
                        desc: `最终批次处理失败: ${finalBatchError.message}`,
                        status: "failed"
                    });
                }
                // Don't throw here, allow partial completion
            }
        }

        const endTime = performance.now();
        const duration = endTime - startTime;
        const rows = await this.codeTable.countRows() /*+ await this.knowledgeTable.countRows()*/;
        this.result.dataRows = rows;
        this.result.duration = duration;

        // Format time durations to minutes and seconds
        const formatDuration = (ms: number): string => {
            const totalSeconds = Math.floor(ms / 1000);
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            if (minutes > 0) {
                return `${minutes}分${seconds}秒`;
            }
            return `${seconds}秒`;
        };

        console.log(`[LanceDbIndex] 向量化统计:`);
        console.log(`   - 文件数量: ${this.result.fileNum}`);
        console.log(`   - 总代码块数: ${totalChunks}`);
        console.log(`   - 已处理数: ${processedChunks}`);
        console.log(`   - 数据库行数: ${rows}`);
        console.log(`   - 切分耗时: ${formatDuration(this.result.chunkDuration)}`);
        console.log(`   - 向量化耗时: ${formatDuration(this.result.embeddingDuration)}`);
        console.log(`   - 入库耗时: ${formatDuration(this.result.insertDataDuration)}`);
        console.log(`   - 总耗时: ${formatDuration(duration)}`);

        await this.saveFileIndexingInfo();
        if (!this.getEnableCodebaseIndexing()) {
            return;
        }

        if (onProgress) {
            onProgress({
                progress: normalizeProgress(1.0),
                desc: "索引完成",
                status: "done"
            });
        }

        console.log("[LanceDbIndex] 向量化处理完成!");
    }

    async createVectorIndex() {
        console.log("[LanceDbIndex] 开始创建向量索引...");
        if (!this.db || !this.codeTable) {
            console.error("[LanceDbIndex] 数据库或表不存在!");
            return;
        }
        const dataRows: number = await this.codeTable.countRows();
        console.log(`[LanceDbIndex] 数据行数: ${dataRows}`);

        if (dataRows < MIN_ROWS_NEED_INDEX) {
            console.log(`[LanceDbIndex] 数据行数太少 (${dataRows} < ${MIN_ROWS_NEED_INDEX})，无需创建索引!`);
            return;
        }

        const indexParams: VectorIndexParams = {
            type: "ivf_pq",
            num_partitions: 4,
            num_sub_vectors: 8,
            num_bits: 4,
            use_opq: false,
            index_cache_size: 128
        }
        console.log("[LanceDbIndex] 索引参数:", indexParams);
        await this.codeTable.createIndex(indexParams);
        console.log("[LanceDbIndex] 向量索引创建成功!");
    }

    async retrieve(repoBaseDir: string, task: string, provideCode: boolean = false) {
        console.log("[LanceDbIndex] 开始检索，工作空间:", repoBaseDir);

        // Use dedicated retrieval initialization
        const initialized = await this.initializeForRetrieval(repoBaseDir);
        if (!initialized) {
            console.error("[LanceDbIndex] 检索环境初始化失败!");
            return "";
        }

        task = await taskExtract(task);
        console.log("[LanceDbIndex] 检索任务:", task);
        const codeResults: string = await this.retrieveCode(task, provideCode);
        //const knowledgeResults: string = await this.retrieveKnowledge(task);
        //return `${codeResults}\n\n${knowledgeResults}`;
        return codeResults;
    }

    async retrieveCode(task: string, provideCode: boolean = false): Promise<string> {
        const embedding = await this.generateEmbeddings([task]);
        let results: CodeChunk[] = await this.codeTable.search(embedding[0]).limit(RETRIEVE_TOPN).execute();
        await this.enrichChunksWithCode(results);
        const scores: number[] = await this.calculateRerankScores(task, results);
        results = await this.sortChunksByScore(scores, results);
        if (provideCode) {
            return this.formatFilePathsWithCode(results);
        }
        return this.formatUniqueFilePaths(results);
    }

    // async retrieveKnowledge(task: string): Promise<string> {
    //     const embedding = await this.generateEmbeddings([task]);
    //     let results: CodeChunk[] = await this.knowledgeTable.search(embedding[0]).limit(RETRIEVE_TOPN).execute();
    //     await this.enrichChunksWithCode(results);
    //     const scores: number[] = await this.calculateRerankScores(task, results);
    //     results = await this.sortChunksByScore(scores, results);
    //     return this.formatUniqueFilePaths(results);
    // }

    async enrichChunksWithCode(chunks: CodeChunk[]) {
        const normalizedRoot = path.normalize(this.repoBaseDir);
        for (let i = 0; i < chunks.length; i++) {
            const chunk: CodeChunk = chunks[i];
            const normalizedFile = path.normalize(chunk["filePath"]);
            const relativePath = path.relative(normalizedRoot, normalizedFile);
            chunk["filePath"] = relativePath;
        }
    }

    async calculateRerankScores(task: string, chunks: CodeChunk[]) {
        const queryNodes: string[][] = [];
        for (let i = 0; i < chunks.length; i++) {
            const code: string = chunks[i]["content"];
            const node: string[] = [];
            node.push(task);
            node.push(code);
            queryNodes.push(node);
        }
        const scores: number[] = await this.rerankModel.rerank(queryNodes);
        return scores;
    }

    async sortChunksByScore(scores: number[], chunks: CodeChunk[]): Promise<CodeChunk[]> {
        if (scores.length !== chunks.length) {
            return chunks;
        }
        const pairedArray: Array<{ score: number; chunk: CodeChunk }> = [];
        for (let i = 0; i < scores.length; i++) {
            pairedArray.push({
                score: scores[i],
                chunk: chunks[i]
            });
        }
        pairedArray.sort((a, b) => b.score - a.score);
        const result: CodeChunk[] = pairedArray.map(item => item.chunk);
        return result;
    }

    formatUniqueFilePaths(chunks: CodeChunk[]): string {
        let filePaths: string[] = [];
        for (let i = 0; i < chunks.length; i++) {
            if (filePaths.includes(chunks[i]["filePath"])) {
                continue;
            }
            filePaths.push(chunks[i]["filePath"]);
            if (filePaths.length >= 10) {
                break;
            }
        }
        return filePaths.join("\n");
    }

    formatFilePathsWithCode(chunks: CodeChunk[]): string {
        let result: string = "";
        let fileCodeMap: Record<string, string> = {};
        for (let i = 0; i < chunks.length; i++) {
            const chunk: CodeChunk = chunks[i];
            const info: string[] = [];
            info.push("content:");
            info.push(chunk["content"]);
            const content: string = info.join("\n");
            const filePath: string = chunk["filePath"];
            if (filePath in fileCodeMap) {
                fileCodeMap[filePath] = fileCodeMap[filePath] + "\n|----------\n" + content;
            }
            else {
                fileCodeMap[filePath] = content;
            }
            if (Object.keys(fileCodeMap).length >= 5) {
                break;
            }
        }
        for (const filePath in fileCodeMap) {
            result += filePath + "\n";
            result += fileCodeMap[filePath] + "\n";
        }
        return result;
    }

    async performIncrementalUpdate(onProgress?: (update: IndexingProgressUpdate) => void) {
        await this.readFileIndexingInfo();
        const codeFiles = await getCodeFilesInDirectory(this.repoBaseDir);
        const needIndexingFiles: string[] = await getNeedIndexingFiles(codeFiles, this.fileIndexingInfo);
        const deleteFiles: string[] = await getDeleteFiles(codeFiles, this.fileIndexingInfo);
        if (deleteFiles.length > 0) {
            console.log(`[LanceDbIndex] 增量更新: 发现 ${deleteFiles.length} 个文件被删除，需要从向量库中删除对应数据`);
            await this.deleteByFilepath(deleteFiles);
        }
        // Only send progress updates if there are files to process
        if (needIndexingFiles.length > 0) {
            console.log(`[LanceDbIndex] 增量更新: 发现 ${needIndexingFiles.length} 个文件需要重新索引`);

            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(0.05),
                    desc: `发现 ${needIndexingFiles.length} 个文件需要更新...`,
                    status: "indexing"
                });
            }

            await this.deleteByFilepath(needIndexingFiles);
            await this.processFilesForEmbedding(needIndexingFiles, onProgress);
            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(1.0),
                    desc: "增量更新完成",
                    status: "done"
                });
            }
        } else {
            console.log("[LanceDbIndex] 增量更新: 没有文件需要更新");

            if (onProgress) {
                onProgress({
                    progress: normalizeProgress(1.0),
                    desc: "索引已是最新状态",
                    status: "done"
                });
            }
        }

        await this.codeTable.cleanupOldVersions(0);
        //await this.knowledgeTable.cleanupOldVersions(0);
        await this.saveFileIndexingInfo();
    }

    checkFailEmbeddings(chunkDicts: CodeChunk[]): boolean {
        let hasFailures = false;
        const failedFiles = new Set<string>();

        for (let i = 0; i < chunkDicts.length; i++) {
            const chunk: CodeChunk = chunkDicts[i];
            if (!chunk.vector || chunk.vector.length === 0) {
                hasFailures = true;
                failedFiles.add(chunk.filePath);
            }
        }

        // Mark all failed files for reindexing
        failedFiles.forEach(filePath => {
            if (filePath in this.fileIndexingInfo) {
                this.fileIndexingInfo[filePath].state = "fail";
                console.warn(`[LanceDbIndex] File marked for reindexing due to embedding failure: ${filePath}`);
            }
        });

        if (hasFailures) {
            console.warn(`[LanceDbIndex] Found ${failedFiles.size} files with embedding failures`);
        }

        return hasFailures;
    }

    async createFileMetadata(filePath: string) {
        const hash: string = await calculateFileHash(filePath);
        const indexTime = Date.now();
        const metadata: FileMetadata = {
            "indexTime": indexTime,
            "hash": hash,
            "state": "success"
        }
        this.fileIndexingInfo[filePath] = metadata;
    }

    async saveFileIndexingInfo() {
        const savePath = path.join(ZERO_AGENT_GLOBAL_DIR, `index/${this.codeTableName}.json`);
        await writeJsonFile(savePath, this.fileIndexingInfo);
    }

    async readFileIndexingInfo() {
        const savePath = path.join(ZERO_AGENT_GLOBAL_DIR, `index/${this.codeTableName}.json`);
        if (!fs.existsSync(savePath)) {
            this.fileIndexingInfo = {};
            return;
        }
        this.fileIndexingInfo = await readJsonFile(savePath);
    }

    async deleteByFilepath(filePaths: string[]) {
        if (!this.db || !this.codeTable /*|| !this.knowledgeTable*/) {
            console.error("[LanceDbIndex] 数据库或表不存在!");
            return;
        }
        if (filePaths.length > 0) {
            console.log(`[LanceDbIndex] 删除文件索引: ${filePaths.length} 个文件`);
        }
        for (let i = 0; i < filePaths.length; i++) {
            const filePath = filePaths[i];
            const condition = "`filePath` = " + `'${filePath}'`;
            await this.codeTable.delete(condition);
            //await this.knowledgeTable.delete(condition);
        }
    }

    async splitFileIntoChunks(filePath: string): Promise<CodeChunk[]> {
        const startTime = performance.now();
        const content = await fs.promises.readFile(filePath, 'utf-8');
        const chunks = await chunker(filePath, content, MAX_CHUNK_LINES);
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.result.chunkDuration += duration;
        // Detailed per-file logging removed to reduce log noise - statistics shown in final summary
        return chunks;
    }

    async generateBatchEmbeddings(contents: string[][]): Promise<number[][][]> {
        const startTime = performance.now();
        const totalChunks = contents.reduce((sum, batch) => sum + batch.length, 0);
        console.log(`[LanceDbIndex] 批量向量化: ${contents.length} 个批次，共 ${totalChunks} 个代码块`);
        const embeddings = await this.embedModel.getBatchEmbeddings(contents);
        const duration = performance.now() - startTime;
        this.result.embeddingDuration += duration;
        // Batch completion logged in final statistics
        return embeddings;
    }

    async generateEmbeddings(contents: string[]): Promise<number[][]> {
        const startTime = performance.now();
        const embeddings = await this.embedModel.getEmbeddings(contents);
        const duration = performance.now() - startTime;
        this.result.embeddingDuration += duration;
        // Individual embedding operations logged in final statistics
        return embeddings;
    }
}