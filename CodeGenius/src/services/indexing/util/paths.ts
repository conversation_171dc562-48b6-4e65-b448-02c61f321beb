import * as crypto from 'crypto';
import * as path from "path";
import * as fs from "fs";
import { DEFAULT_IGNORE_DIRS, defaultIgnoreFileAndDir } from "../ignore";

export const CODE_INDEXING_EXTENSIONS = [
  ".c", ".h",
  ".cpp", ".cc", ".cxx", ".hpp",
  ".cs",
  ".java",
  ".py",
  ".js", ".jsx", ".mjs", ".ts", ".tsx",
  ".go",
  ".rs",
  ".swift",
  ".kt", ".kts",
  ".php",
  ".rb",
  ".dart"
];

export const KNOWLEDGE_INDEXING_EXTENSIONS = [
  ".md"
];

export async function getCodeFilesInDirectory(rootDir: string): Promise<string[]> {
  const ignoreDirs = new Set([...DEFAULT_IGNORE_DIRS].map(dir => dir.replace(/\/$/, "")));
  // const indexingExtensions = [...KNOWLEDGE_INDEXING_EXTENSIONS, ...CODE_INDEXING_EXTENSIONS];
  const indexingExtensions = CODE_INDEXING_EXTENSIONS;
  const codeFiles: string[] = [];

  function walkDir(dir: string) {
    fs.readdirSync(dir).forEach(f => {
      const fullPath = path.join(dir, f);
      const stat = fs.statSync(fullPath);
      if (stat.isDirectory()) {
          if (ignoreDirs.has(f)) {
              return;
          }
          walkDir(fullPath);
      } else {
          // 将绝对路径转换为相对于根目录的相对路径
          const relativePath = path.relative(rootDir, fullPath);
          const isIgnored = defaultIgnoreFileAndDir.ignores(relativePath);
          if (!isIgnored && indexingExtensions.some(ext => f.endsWith(ext))) {
              codeFiles.push(fullPath);
          }
      }
    });
  }

  return new Promise((resolve, reject) => {
    try {
      walkDir(rootDir);
      resolve(codeFiles);
    } catch (e) {
      reject(e);
    }
  });
}

export async function readFileByLine(
  filePath: string,
  startLine: number,
  endLine: number
): Promise<string> {
  const defaultResult = "";
  if (startLine < 0) {
    return defaultResult;
  }
  if (endLine < startLine) {
    return defaultResult;
  }
  const absolutePath = path.resolve(filePath);
  if (!fs.existsSync(absolutePath)){
    return defaultResult;
  }
  const content = await fs.promises.readFile(absolutePath, 'utf-8');
  const lines = content.split(/\r?\n/);
  if (startLine > lines.length) {
    console.debug(`开始行号${startLine}超过了文件总行数${lines.length}`);
  }
  if (endLine > lines.length) {
    console.warn(`警告: 结束行号${endLine}超过了文件总行数${lines.length}, 将返回到文件末尾`);
    endLine = lines.length;
  }
  const selectedLines = lines.slice(startLine, endLine);
  return selectedLines.join('\n');
}

export interface FileMetadata {
  indexTime: number;
  hash: string;
  state: "success" | "fail";
}

export async function getNeedIndexingFiles(filePaths: string[], fileIndexingInfo: Record<string, FileMetadata>): Promise<string[]> {
  const needIndexingFiles: string[] = [];
  for (let i=0;i<filePaths.length;i++) {
    const filePath = filePaths[i];
    const stats = fs.statSync(filePath);
    const metadata = fileIndexingInfo[filePath];
    if (metadata === undefined) {
      needIndexingFiles.push(filePath);
      continue;
    }
    if (fileIndexingInfo[filePath].state === "fail") {
      needIndexingFiles.push(filePath);
      continue;
    }
    if (stats.mtimeMs > metadata["indexTime"]) {
      const hash: string = await calculateFileHash(filePath);
      if (hash !== metadata["hash"]) {
        needIndexingFiles.push(filePath);
      }
    }
  }
  return needIndexingFiles;
}

export async function getDeleteFiles(filePaths: string[], fileIndexingInfo: Record<string, FileMetadata>): Promise<string[]> {
  const deleteFiles: string[] = [];
  for (const filePath in fileIndexingInfo) {
    if (!filePaths.includes(filePath)) {
      deleteFiles.push(filePath);
    }
  }
  for (const file of deleteFiles) {
    delete fileIndexingInfo[file];
  }
  return deleteFiles;
}

export async function calculateFileHash(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath);

    stream.on('data', (chunk) => hash.update(chunk));
    stream.on('end', () => resolve(hash.digest('hex')));
    stream.on('error', reject);
  });
}