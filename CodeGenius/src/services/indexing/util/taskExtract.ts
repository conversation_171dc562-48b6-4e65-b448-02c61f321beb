import { OpenAI } from 'openai';
const openai = new OpenAI({
  apiKey: 'EMPTY',
  baseURL: 'http://nebulacoder.dev.zte.com.cn:40081/v1'
});

const EXTRACT_SEARCH_INFO_PROMPT = `从用户的输入中提取出最关键的查询词或主题，忽略任务指令、限制条件和指定要求。目标是找出用户想要查询的核心内容，输出应该简洁而直接。例如：
示例1
用户输入：回答时请用2000字详尽阐述，我的问题是，自行车是什么时候发明的？请用英文回复。
提取信息：自行车是什么时候发明的
示例2
用户输入：生成AccountService.java中，AccountService类的getAccountDetails方法的单元测试用例。测试框架使用的是JUnit5。
提取信息：AccountService.java中,AccountService类的getAccountDetails方法
示例3
用户输入：为ServiceManager.java中的ServiceManager类的startService方法编写单元测试，采用JUnit4进行测试，不要使用PowerMock。禁止修改源文件。不允许增加测试数据文件。
提取信息：ServiceManager.java中,ServiceManager类的startService方法
-----------
以下为真实用户输入，直接回答提取后的内容。`;

export async function taskExtract(task: string) {
  const query = EXTRACT_SEARCH_INFO_PROMPT + "\n" + task;
  try {
    const chatCompletion = await openai.chat.completions.create({
      model: "nebulacoder-v6.0",
      messages: [
        { role: "user", content: query }
      ],
      temperature: 0.7,
      max_tokens: 150
    });
    let result = chatCompletion.choices[0].message.content;
    if (!result) {
        return task;
    }
    result = result.replace(/提取信息：/, '');
    return result;
  } catch (error) {
    console.error("Error calling OpenAI API:", error);
    return task;
  }
}