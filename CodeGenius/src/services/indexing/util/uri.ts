import * as URI from "uri-js";

export function getCleanUriPath(uri: string) {
  const path = URI.parse(uri).path ?? "";
  let clean = path.replace(/^\//, ""); // remove start slash
  clean = clean.replace(/\/$/, ""); // remove end slash
  return clean;
}

/*
  Returns just the file or folder name of a URI
*/
export function getUriPathBasename(uri: string): string {
  const path = getCleanUriPath(uri);
  const basename = path.split("/").pop() || "";
  return decodeURIComponent(basename);
}

export function getFileExtensionFromBasename(basename: string) {
  const parts = basename.split(".");
  if (parts.length < 2) {
    return "";
  }
  return (parts.slice(-1)[0] ?? "").toLowerCase();
}

/*
  Returns the file extension of a URI
*/
export function getUriFileExtension(uri: string) {
  const baseName = getUriPathBasename(uri);
  return getFileExtensionFromBasename(baseName);
}
