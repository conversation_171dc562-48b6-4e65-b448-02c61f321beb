import * as path from "path"
import { execa } from "execa"
import { Logger } from "@services/logging/Logger"
import { WebviewProvider } from "@core/webview"

import {
	getWorkspacePath,
	validateWorkspacePath,
} from "./GitHelper"
import {
	executeTask,
} from "./TestServer"

import {  cp, readdir, access, constants } from 'fs/promises';
import * as fs from "fs"
import { ZeroLogger } from "@/utils/logger"

async function parseKeyValueFile(filePath: string): Promise<Record<string, string>> {
    try {
		const content = fs.readFileSync(filePath,  'utf-8');
		const lines = content.split(/\r?\n/);  // 兼容不同换行符
		const result: Record<string, string> = {};

		lines.forEach(line  => {
			if (!line.trim()  || !line.includes('=')) {return;}  // 跳过空行和无效行
			const [key, value] = line.split('=').map(item  => item.trim());
			result[key] = value;
		});

		return result;
	} catch (error) {
		console.error('Error  reading or parsing the file:', error);
		throw error; // 可以选择重新抛出错误或返回一个空对象等
	}
}


function findZipFiles(zipPath: string): { zipfiles: string[]; zipfile: string | null } {
	try {
		const files = fs.readdirSync(zipPath);
		const zipfiles: string[] = [];

		files.forEach(file => {
			const ext = path.extname(file);
			const fullPath = path.join(zipPath, file);

			if (ext === '.7z' || ext === '.zip' || file.endsWith('.tar.gz')) {
				zipfiles.push(fullPath);
			}
		});

		const zipfile = zipfiles.length > 0 ? zipfiles[0] : null;

		return { zipfiles, zipfile };
	} catch (error) {
		console.error(` 读取目录时出错: ${error}`);
		return { zipfiles: [], zipfile: null };
	}
}


export function executeCompilationAndUpload(req: any, res: any) {
	// Parse the request body
	let body = ""
	req.on("data", (chunk: any) => {
		body += chunk.toString()
	})

	req.on("end", async () => {
		// Parse the body as JSON
		let { request_id, rdcid, relate_path, time_out } = JSON.parse(body)


		/********************Get workspacePath********************/
		let workspacePath = ""
		//get ZeroAgents instance
		const visibleWebview = WebviewProvider.getVisibleInstance()
		if (!visibleWebview || !visibleWebview.controller) {
			res.writeHead(500)
			res.end(JSON.stringify({ error: "No active ZeroAgents instance found" }))
			return
		}
		//Get and validate the workspace path
		workspacePath = getWorkspacePath(visibleWebview)
		Logger.log(`Using workspace path: ${workspacePath}`)


		/********************Parse configFile********************/
		let configFilePath = '/code/arc_config.txt'
		let compileAndPackageFilePath = ""
		let packagePath = ""
		let severAdd = ""
		let user = ""
		let password = ""
		let arcDest = ""
		let zipFilename =""
		try {
			const keyValuePairs			= await parseKeyValueFile(configFilePath);
			compileAndPackageFilePath	= keyValuePairs['compileAndPackageFilePath'];	//related path/file
			packagePath					= keyValuePairs['packagePath'];					//related path
			severAdd					= keyValuePairs['artifactory_severAdd'];
			user						= keyValuePairs['artifactory_user'];
			password					= keyValuePairs['artifactory_password'];
			arcDest 					= keyValuePairs['artifactory_destPath'];
			Logger.log(`compileAndPackageFilePath is: ${compileAndPackageFilePath}`)
			Logger.log(`packagePath is: ${packagePath}`)
			Logger.log(`severAdd is: ${severAdd}`)
			Logger.log(`arcDest is: ${arcDest}`)
		} catch (error: any) {
			Logger.log(`Error parsing configFile: ${configFilePath}`)
			res.writeHead(500)
			res.end(JSON.stringify({ error: `Error parsing configFile: ${configFilePath}` }))
			return
		}


		/********************CompileAndPackage by execute scriptFile********************/
		const compileAndPackage_pathAll = path.join(workspacePath, compileAndPackageFilePath);
		Logger.log(`compileAndPackage_pathAll:\n${compileAndPackage_pathAll}`)
		try {
			const { stdout: compileOutput } = await execa(`bash ${compileAndPackage_pathAll}`, { shell: true, all: true });
			// const { stdout: compileOutput } = await execa(`cmd /c ${compileAndPackage_pathAll}`, { shell: true, all: true });// windows
			Logger.log(`compile success:\n${compileOutput}`)
		} catch (error) {
			res.writeHead(500)
			res.end(JSON.stringify({ error: "compile or package failed" }))
			Logger.log(`compile or package failed: ${error.message}`)
			return
		}

		
		/********************Get the current zipfile Path********************/
		// const relPackagePath = path.dirname(packagePath);  // 提取目录部分
		// Logger.log(`relPackagePath is: ${relPackagePath}`)
		const absPackagePath = path.join(workspacePath, packagePath)
		Logger.log(`absPackagePath is: ${absPackagePath}`)
		const { zipfiles, zipfile } = findZipFiles(absPackagePath);	//find .7z , .zip , .tar.gz File
		if (zipfile) {
			zipFilename = path.basename(zipfile);
			Logger.log(`zipfile is: ${zipfile}`)
		}
		else {
			Logger.log(`No zip or 7z or tar.gz File Found in ${absPackagePath}`)
			res.writeHead(500)
			res.end(JSON.stringify({ error: `Error parsing configFile: ${configFilePath}` }))
			return
		}
		//relate_path = path.join(arcDest, zipFilename)
		let curr_file_relatePath = path.join(absPackagePath, zipFilename)
		Logger.log(`curr_file_relatePath is: ${curr_file_relatePath}`)

		
		/********************Config and Upload zipFile to ArcDestination********************/
		// generate Command
		const config_command = ['jfrog rt config',
								`--url="${severAdd}"`,
								`--user=${user}`,
								`--password=${password}`,
								'--interactive=false'
								].join(' ');
		const upload_command = ['jfrog rt upload',
								`${curr_file_relatePath}`,
								`${arcDest}`
								].join(' ');
		Logger.log(`config_command is: ${config_command}`)
		Logger.log(`upload_command is: ${upload_command}`)

		//PushtoVersionBase
		try {
			const { stdout: configOutput } = await execa(config_command, { cwd: workspacePath, shell: true, all: true })
			Logger.log(`config success:\n${configOutput}`)
			const { stdout: upLoadOutput } = await execa(upload_command, { cwd: workspacePath, shell: true, all: true })
			Logger.log(`upLoad success:\n${upLoadOutput}`)
			res.writeHead(200, { "Content-Type": "application/json" })
			res.end(
				JSON.stringify({
					request_id: request_id,		//本次请求的id,也需要返回给开发智能体？
					rdcid: rdcid,				//为和开发智能体配套使用
					tag: ["US"],				//not nessary 需要验证的用例,列表
					relate_path: zipFilename,			//存放的版本。脚本路径信息
					version_type: "all",			//not nessary, string 全量还是补丁, 默认全量，6月份先默认全量
				})
			)
		} catch (error) {
			res.writeHead(400)
			res.end(JSON.stringify({ error: "PushArtifact failed" }))
			Logger.log(`PushArtifact failed: ${error.message}`)
		}
		//} catch (timeoutError) {
		//	res.writeHead(400)
		//	res.end(JSON.stringify({ error: "Timeout Error" }))
		//	Logger.log(`Timeout Error`)
		//	return
		//}
	})
}

export function handleTest_DevelopRequest(req: any, res: any) {
	// Parse the request body
	let body = ""
	req.on("data", (chunk: any) => {
		body += chunk.toString()
	})
	req.on("end", async () => {
		// Parse the body as JSON
		let { request_id, rdcid, storydescript, use_case, acceptance, relate_path, business_rule, api_description, time_out } = JSON.parse(body)

		let configFilePath = '/code/arc_config.txt'
		let workspacePath = ""
		let filename =""
		let responseObj

		try {
			let task = ""
			if (storydescript) {
				task += "##用户故事描述:\n" + storydescript + "\n\n"
			}
			if (use_case) {
				task += "use_case:" + use_case + "\n\n"
			}
			if (acceptance) {
				task += "##验收准则:\n" + acceptance + "\n\n"
			}
			if (api_description){
				task+= "##接口信息:\n" + api_description + "\n\n"
			}
			if (business_rule) {
				task+= "##业务流程:\n" + business_rule + "\n\n"
			}
			task+= "##要求:\n" + "一定不要修改任何pom.xml文件，忽略pom.xml文件的错误，另外，必须先读取.zerorules下的规则，再根据以上信息，生成该用户故事的生产代码" + "\n\n"
			Logger.log(`task is: ${task}`)

			//get ZeroAgents instance
			const visibleWebview = WebviewProvider.getVisibleInstance()
			if (!visibleWebview || !visibleWebview.controller) {
				res.writeHead(500)
				res.end(JSON.stringify({ error: "No active ZeroAgents instance found" }))
				return
			}
			//Execute Task
			try {
				// Get and validate the workspace path
				workspacePath = getWorkspacePath(visibleWebview)
				Logger.log(`Using workspace path: ${workspacePath}`)

				//Validate workspace path before proceeding with any operations
				try {
					await validateWorkspacePath(workspacePath)
				} catch (error) {
					Logger.log(`Workspace validation failed: ${error.message}`)
					res.writeHead(500)
					res.end(
						JSON.stringify({
							error: `Workspace validation failed: ${error.message}. Please open a workspace folder in VSCode before running the test.`,
							workspacePath,
						}),
					)
					return
				}

				// Initialize Git repository before starting the task
				//try {
				//	const wasNewlyInitialized = await initializeGitRepository(workspacePath)
				//	if (wasNewlyInitialized) {
				//		Logger.log(`Initialized new Git repository in ${workspacePath} before task start`)
				//	} else {
				//		Logger.log(`Using existing Git repository in ${workspacePath} before task start`)
				//	}
				//
				//	// Log directory contents before task start
				//	try {
				//		const { stdout: lsOutput } = await execa("ls", ["-la", workspacePath])
				//		Logger.log(`Directory contents before task start:\n${lsOutput}`)
				//	} catch (lsError) {
				//		Logger.log(`Warning: Failed to list directory contents: ${lsError.message}`)
				//	}
				//} catch (gitError) {
				//	Logger.log(`Warning: Git initialization failed: ${gitError.message}`)
				//	Logger.log("Continuing without Git initialization")
				//}
				Logger.log(`await begin `)
				if (!time_out) {
					time_out = 30
				}
				const responseStr = await executeTask(request_id, visibleWebview, task, time_out)
				responseObj = JSON.parse(responseStr);
				Logger.log(`Success to receive request, id:${request_id}`)
				
				res.writeHead(200, { "Content-Type": "application/json" });
				res.end(
				JSON.stringify({
					request_id: request_id,		//本次请求的id,也需要返回给开发智能体？
					rdcid: rdcid,				//为和开发智能体配套使用
					tag: ["US"],				//not nessary 需要验证的用例,列表
					relate_path: filename,		//存放的版本。脚本路径信息
					version_type: "all",		//not nessary, string 全量还是补丁, 默认全量，6月份先默认全量
					files: responseObj.files
				}),
				);
				return
			} catch (error: any) {
				Logger.log(`Error initiating task: ${error}`)
				res.writeHead(500)
				res.end(JSON.stringify({ error: `Failed to initiate task: ${error}` }))
				return
			}
		} catch (timeoutError) {
			res.writeHead(400)
			res.end(JSON.stringify({ error: "Timeout Error" }))
			Logger.log(`Timeout Error`)
			return
		}
	})
}

export function handleTest_VerifyRequest(req: any, res: any) {
	// Parse the request body
	let body = ""
	req.on("data", (chunk: any) => {
		body += chunk.toString()
	})
	req.on("end", async () => {
		// Parse the body as JSON
		let { request_id, rdcid, result, problemDescription, file_change, userStory, branch, time_out } = JSON.parse(body)
		let relate_path = ""
		let responseObj
		let filename = ""
		let gerrit_url = ""
		if (result === "success") {
			Logger.log(`Code push to Gerrit begin`)
			//PushtoGerrit
			try {
				let workspacePath = ""
				/********************  Get workspacePath ********************/
				//get ZeroAgents instance
				const visibleWebview = WebviewProvider.getVisibleInstance()
				if (!visibleWebview || !visibleWebview.controller) {
					res.writeHead(500)
					res.end(JSON.stringify({ error: "No active ZeroAgents instance found" }))
					return
				}
				//Get and validate the workspace path
				workspacePath = getWorkspacePath(visibleWebview)
				Logger.log(`Using workspace path: ${workspacePath}`)


				/********************  check status and push ********************/
				let repoDir = workspacePath
				let remote = "origin"
				if (!branch) {
					branch = "develop"
				}
				if (!userStory) {
					userStory = "智能体自动生成代码"
				}
				const cleanedUserStory = userStory.replace(/[\r\n]+/g, "");  // 去除换行符以免影响commitMessage格式
				const commitMessage = `RDC:${rdcid} 用户故事：${cleanedUserStory} \n\n[新增]用户故事：${cleanedUserStory}`;
				Logger.log(`git commitMessage:${commitMessage}`)
				Logger.log(`git push ${remote} HEAD:refs/for/${branch}`)

				// 验证目录和.git存在 
				await access(repoDir, constants.R_OK);
				const gitDir = path.join(workspacePath, '.git');
				await access(gitDir, constants.R_OK);
				// check 
				const { stdout: statusStdout } = await execa('git status --porcelain', { cwd: repoDir, shell: true, all: true });
				if (!statusStdout.trim()) {
					Logger.log(`无文件变更，跳过提交`)
					res.writeHead(500)
					res.end(JSON.stringify({ error: `No file changed, No need to push` }))				
				}
				else{
					Logger.log(`add commit push`)
					// add commit push 
					await execa(`git add .`, { cwd: repoDir, shell: true, all: true });
					const commitEditMsgPath = path.join(repoDir, '.git', 'COMMIT_EDITMSG');
					//Logger.log(`wrtting file:${commitEditMsgPath}`)
					//Logger.log(`wrtting commitMessage:${commitMessage}`)
					fs.writeFileSync(commitEditMsgPath, commitMessage)
					//await execa(`echo -e "${commitMessage}" > .git/COMMIT_EDITMSG`, { cwd: repoDir, shell: true, all: true });
					await execa(`git commit -F .git/COMMIT_EDITMSG`, { cwd: repoDir, shell: true, all: true });
					//await execa(`git commit -m "${commitMessage}"`, { cwd: repoDir, shell: true, all: true });
					//Logger.log(`git push ${remote} HEAD:refs/for/${branch}`)
					const { all } = await execa(`git push -v ${remote} HEAD:refs/for/${branch}`, { cwd: repoDir, shell: true, all: true });
					//Logger.log(`pushStdout:${all}`)
					const urlRegex = /http:\/\/[^\s]+/;// 正则匹配：提取 http URL
					const match = all.match(urlRegex);
					if (match) {
						gerrit_url = match[0].split(' ')[0]
					}
					Logger.log(`gerrit_url:${gerrit_url}`)
				}
			} catch (error: any) {
				Logger.log(`Error Pushing file`)
				res.writeHead(500)
				res.end(JSON.stringify({ error: `Error Pushing file` }))
				return
			}
			Logger.log(`Code push to Gerrit end`)
		} else {
			let workspacePath = ""

			//get ZeroAgents instance
			const visibleWebview = WebviewProvider.getVisibleInstance()
			if (!visibleWebview || !visibleWebview.controller) {
				res.writeHead(500)
				res.end(JSON.stringify({ error: "No active ZeroAgents instance found" }))
				return
			}
			//Get and validate the workspace path
			workspacePath = getWorkspacePath(visibleWebview)
			Logger.log(`Using workspace path: ${workspacePath}`)

			//Validate workspace path before proceeding with any operations
			try {
				await validateWorkspacePath(workspacePath)
			} catch (error) {
				Logger.log(`Workspace validation failed: ${error.message}`)
				res.writeHead(500)
				res.end(
					JSON.stringify({
						error: `Workspace validation failed: ${error.message}. Please open a workspace folder in VSCode before running the test.`,
						workspacePath,
					}),
				)
				return
			}

			//call the repair Agent wait to RepairDown
			let task = ""
			if (problemDescription) {
				task += "##验收测试返回的testcase_info的信息为:\n" + problemDescription + "\n\n"
			}
			task+= "##要求:\n" + "请根据以上信息，完整传入testcase_info的内容，并使用RepairAgent-MCP工具修复错误， 如果RepairAgent返回补丁，请必须应用该补丁"
			Logger.log(`task is: ${task}`)

			//Execute Task
			try {
				// Initialize Git repository before starting the task
				//try {
				//	const wasNewlyInitialized = await initializeGitRepository(workspacePath)
				//	if (wasNewlyInitialized) {
				//		Logger.log(`Initialized new Git repository in ${workspacePath} before task start`)
				//	} else {
				//		Logger.log(`Using existing Git repository in ${workspacePath} before task start`)
				//	}
				//
				//	// Log directory contents before task start
				//	try {
				//		const { stdout: lsOutput } = await execa("ls", ["-la", workspacePath])
				//		Logger.log(`Directory contents before task start:\n${lsOutput}`)
				//	} catch (lsError) {
				//		Logger.log(`Warning: Failed to list directory contents: ${lsError.message}`)
				//	}
				//} catch (gitError) {
				//	Logger.log(`Warning: Git initialization failed: ${gitError.message}`)
				//	Logger.log("Continuing without Git initialization")
				//}
				Logger.log(`await begin `)
				if (!time_out) {
					time_out = 30
				}
				const responseStr = await executeTask(request_id, visibleWebview, task, time_out)
				responseObj = JSON.parse(responseStr)
				Logger.log(`Success to receive request, id:${request_id}`)
			} catch (error: any) {
				Logger.log(`Error initiating task: ${error}`)
				res.writeHead(500)
				res.end(JSON.stringify({ error: `Failed to initiate task: ${error}` }))
				return
			}
		}

		res.writeHead(200, { "Content-Type": "application/json" });
		res.end(
			JSON.stringify({
				request_id: request_id,		//本次请求的id,也需要返回给开发智能体？
				rdcid: rdcid,				//为和开发智能体配套使用
				tag: ["US"],				//not nessary 需要验证的用例,列表
				task: "repair",				//任务,枚举类型
				relate_path: filename,		//存放的版本。脚本路径信息
				gerrit_url: gerrit_url,
				files: responseObj?.files ?? []
			}),
		);
	})
}
