// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: file.proto

/* eslint-disable */
import { Empty, StringRequest } from "./common"

export const protobufPackage = "cline"

/** Service for file-related operations */
export type FileServiceDefinition = typeof FileServiceDefinition
export const FileServiceDefinition = {
	name: "FileService",
	fullName: "cline.FileService",
	methods: {
		/** Opens a file in the editor */
		openFile: {
			name: "openFile",
			requestType: StringRequest,
			requestStream: false,
			responseType: Empty,
			responseStream: false,
			options: {},
		},
	},
} as const
