import { createLogger, format, Logger, transports } from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

const logFormat = format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.errors({ stack: true }), 
    format.splat(),
    format.printf(({ timestamp, level, message }) =>
        `${timestamp} [${level}] ${message}`
    )
);

export class ZeroLogger {
    private static _logger: Logger;
    private filename: string;

    constructor(filename: string) {
        this.filename = filename;
    }

    public static initLogger(logRootDirs: string) {
        ZeroLogger._logger = createLogger({
            level: 'info',
            format: logFormat,
            transports: [
                new transports.Console({
                    format: format.combine(
                        format.colorize()
                    )
                }),
                new DailyRotateFile({
                    filename: `${logRootDirs}/logs/zero-%DATE%.log`,
                    datePattern: 'YYYY-MM-DD',
                    maxSize: '20m',
                    maxFiles: '30d'
                })
            ]
        });
    }

    info(message: string, ...meta: any[]) {
        ZeroLogger._logger.info(`${this.filename}: ${message}`, ...meta);
    }

    error(message: string, ...meta: any[]) {
         ZeroLogger._logger.error(`${this.filename}: ${message}`, ...meta);
    }

    warn(message: string, ...meta: any[]) {
        ZeroLogger._logger.warn(`${this.filename}: ${message}`, ...meta);
    }

    debug(message: string, ...meta: any[]) {
         ZeroLogger._logger.debug(`${this.filename}: ${message}`, ...meta);
    }
}
