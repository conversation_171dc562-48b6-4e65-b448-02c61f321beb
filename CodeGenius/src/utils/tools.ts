export function deepClone(source: any): any {
    if (source === null || typeof source !== 'object') {
        return source;
    } else {
        if (source instanceof Array) {
            const target: any[] = [];
            source.forEach(item => target.push(deepClone(item)));
            return target;
        } else {
            const target: any = {};
            for (const key in source) {
                target[key] = deepClone(source[key]);
            }
            return target;
        }
    }
}