import {useEffect, useMemo, useState } from "react"
import { createPortal } from "react-dom"
import styled from "styled-components"
import { CODE_BLOCK_BG_COLOR } from "../common/CodeBlock"
import { flip, offset, shift, useFloating } from "@floating-ui/react"
import { VSCodeButton, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"

const RdcSubmission = () => {
  const { currentTaskItem, clineMessages: messages, userId } = useExtensionState()
  const [showSubmission, setShowSubmission] = useState<boolean>(false)
  const [title, setTitle] = useState<string>('')
  const [currUserId, setCurrUserId] = useState<string>('')
  const [submitDisable, setSubmitDisable] = useState<boolean>(false)
  const task = useMemo(() => messages.at(0), [messages])

  const { refs, floatingStyles, update, placement } = useFloating({
    placement: "bottom",
    middleware: [
      offset({
        mainAxis: 8,
        crossAxis: 10,
      }),
      flip(),
      shift(),
    ],
  })

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const tooltipElement = document.querySelector("rdc-submission-tooltip")
      if (!tooltipElement?.contains(event.target as Node)) {
        setShowSubmission(false)
      }
    }

    if (showSubmission) {
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showSubmission])

  useEffect(() => {
    const handleScroll = () => {
      update()
    };
    window.addEventListener("scroll", handleScroll, true)
    return () => window.removeEventListener("scroll", handleScroll, true)
  }, [update])

  useEffect(() => {
    if (showSubmission) {
      update()
    }
  }, [showSubmission, update])

  useEffect(() => {
    setCurrUserId(userId)
  }, [userId])

  useEffect(() => {
    setSubmitDisable(!validateUserId(currUserId) || !title)
  }, [currUserId, title])

  const validateUserId = (userId: string) => {
    const match = userId.match(/\d{8,12}/)
    return (match && userId.length >= 8 && userId.length <= 12)
  }

  const submit = () => {
    vscode.postMessage({
      type: "rdcSubmission",
      rdcSubmission: {
        title,
        taskId: currentTaskItem?.id ?? '',
        ts: task?.ts ?? null,
        userId: currUserId,
        model: task?.model ?? ''
      },
    })
    setShowSubmission(false)
  }

  const cancel = () => {
    setShowSubmission(false)
  }

  return (
    <div ref={refs.setReference} style={{ position: "relative", display: "inline-block", top: "-1px", marginLeft: "8px" }}>
      <CustomButton
        isActive={showSubmission}
        onClick={(e) => {
          e.stopPropagation()
          setTitle('')
          setShowSubmission(true)
        }}>
        提交RDC
      </CustomButton>
      {showSubmission &&
        createPortal(
          <SubmissionTooltip
            ref={refs.setFloating}
            style={floatingStyles}
            data-placement={placement}
            className="rdc-submission-tooltip"
            onClick={(e) => e.stopPropagation()}>
            <SubmissionField>
              <label>标题</label>
              <VSCodeTextField 
                placeholder="输入RDC故障标题"
                style={{width: "100%"}} 
                value={title}
                onInput={(e) => {
                  setTitle((e.target as HTMLInputElement)?.value ?? '')
                }}
              />
            </SubmissionField>
            <SubmissionField>
              <label>工号</label>
              <VSCodeTextField 
                placeholder="输入8~12位工号"
                style={{width: "100%"}} 
                value={currUserId}
                onInput={(e) => {
                  setCurrUserId((e.target as HTMLInputElement)?.value ?? '')
                }}
              />
            </SubmissionField>
            <p style={{margin: "8px 0 0", display: "flex"}}>
              <label style={{flex: 0, flexBasis: "48px", paddingRight: "16px"}}></label>
              <VSCodeButton onClick={submit} disabled={submitDisable}>提交</VSCodeButton>
              <VSCodeButton 
                style={{background: "transparent", marginLeft: "8px"}}
                onClick={cancel}>
                  取消
              </VSCodeButton>
            </p>
          </SubmissionTooltip>,
          document.body,
        )
      }
    </div>
  )
}

const CustomButton = styled.button<{ disabled?: boolean; isActive?: boolean; $isCheckedOut?: boolean }>`
  background: ${(props) =>
    props.isActive || props.disabled
      ? props.$isCheckedOut
        ? "var(--vscode-textLink-foreground)"
        : "var(--vscode-descriptionForeground)"
      : "transparent"};
  border: none;
  color: ${(props) =>
    props.isActive || props.disabled
      ? "var(--vscode-editor-background)"
      : props.$isCheckedOut
        ? "var(--vscode-textLink-foreground)"
        : "var(--vscode-descriptionForeground)"};
  padding: 2px 6px;
  font-size: 9px;
  cursor: pointer;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 1px;
    background-image: ${(props) =>
      props.isActive || props.disabled
        ? "none"
        : `linear-gradient(to right, ${props.$isCheckedOut ? "var(--vscode-textLink-foreground)" : "var(--vscode-descriptionForeground)"} 50%, transparent 50%),
      linear-gradient(to bottom, ${props.$isCheckedOut ? "var(--vscode-textLink-foreground)" : "var(--vscode-descriptionForeground)"} 50%, transparent 50%),
      linear-gradient(to right, ${props.$isCheckedOut ? "var(--vscode-textLink-foreground)" : "var(--vscode-descriptionForeground)"} 50%, transparent 50%),
      linear-gradient(to bottom, ${props.$isCheckedOut ? "var(--vscode-textLink-foreground)" : "var(--vscode-descriptionForeground)"} 50%, transparent 50%)`};
    background-size: ${(props) => (props.isActive || props.disabled ? "auto" : `4px 1px, 1px 4px, 4px 1px, 1px 4px`)};
    background-repeat: repeat-x, repeat-y, repeat-x, repeat-y;
    background-position:
      0 0,
      100% 0,
      0 100%,
      0 0;
  }

  &:hover:not(:disabled) {
    background: ${(props) =>
      props.$isCheckedOut ? "var(--vscode-textLink-foreground)" : "var(--vscode-descriptionForeground)"};
    color: var(--vscode-editor-background);
    &::before {
      display: none;
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const SubmissionTooltip = styled.div`
  position: fixed;
  background: ${CODE_BLOCK_BG_COLOR};
  border: 1px solid var(--vscode-editorGroup-border);
  padding: 12px;
  border-radius: 3px;
  margin-left: 32px;
  width: calc(100vw - 90px);
  z-index: 1000;

  // Add invisible padding to create a safe hover zone
  &::before {
    content: "";
    position: absolute;
    top: -8px;
    left: 0;
    right: 0;
    height: 8px;
  }

  // Adjust arrow to be above the padding
  // &::after {
  //   content: "";
  //   position: absolute;
  //   top: -6px;
  //   left: 24px;
  //   width: 10px;
  //   height: 10px;
  //   background: ${CODE_BLOCK_BG_COLOR};
  //   border-left: 1px solid var(--vscode-editorGroup-border);
  //   border-top: 1px solid var(--vscode-editorGroup-border);
  //   transform: rotate(45deg);
  //   z-index: 1;
  // }

  // When menu appears above the button
  &[data-placement^="top"] {
    &::before {
      top: auto;
      bottom: -8px;
    }

    &::after {
      top: auto;
      bottom: -6px;
      right: 24px;
      transform: rotate(225deg);
    }
  }

  p {
    margin: 0 0 6px 0;
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    white-space: normal;
    word-wrap: break-word;
  }
`

const SubmissionField = styled.div`
  &:not(:last-of-type) {
    margin-bottom: 10px;
    padding-bottom: 4px;
    border-bottom: 1px solid var(--vscode-editorGroup-border);
  }
  
  & {
    display: flex;
    align-items: center;
    border-bottom: none;
  }

  label {
    flex-shrunk: 0;
    flex-grow: 0;
    padding-right: 16px;
    flex-basis: 48px;
    min-width: 48px;
    text-align: right;
  }

  p {
    margin: 0 0 2px 0;
    color: var(--vscode-descriptionForeground);
    font-size: 11px;
    line-height: 14px;
  }

  &:last-child p {
    margin: 0 0 -2px 0;
  }
`

export default RdcSubmission