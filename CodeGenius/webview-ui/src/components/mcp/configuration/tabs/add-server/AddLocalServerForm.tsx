import { VSCodeButton, VSCodeLink } from "@vscode/webview-ui-toolkit/react"
import { vscode } from "@/utils/vscode"
import styled from "styled-components"
import { LINKS } from "@/constants"
import i18n from "@/i18n"

type AddLocalServerFormProps = {
	onServerAdded: () => void
}

const AddLocalServerForm = ({ onServerAdded }: AddLocalServerFormProps) => {
	return (
		<FormContainer>
			<div className="text-[var(--vscode-foreground)]">
				{i18n.get("setting.mcp.remote_servers.add_server.desc_prefix1")}
				<code>zero_mcp_settings.json</code>
				{i18n.get("setting.mcp.remote_servers.add_server.desc_prefix2")}
				<VSCodeLink href={LINKS.DOCUMENTATION.LOCAL_MCP_SERVER_DOCS} style={{ display: "inline" }}>
					{i18n.get("setting.mcp.remote_servers.add_server.desc_suffix")}
				</VSCodeLink>
			</div>

			<VSCodeButton
				appearance="primary"
				style={{ width: "100%", marginBottom: "5px", marginTop: 8 }}
				onClick={() => {
					vscode.postMessage({ type: "openMcpSettings" })
				}}>
				{i18n.get("setting.mcp.remote_servers.add_server.open_prefix")}zero_mcp_settings.json
			</VSCodeButton>
		</FormContainer>
	)
}

const FormContainer = styled.div`
	padding: 16px 20px;
	display: flex;
	flex-direction: column;
	gap: 8px;
`

export default AddLocalServerForm
