import { VSCodeButton, VSCodeLink } from "@vscode/webview-ui-toolkit/react"
import { vscode } from "@/utils/vscode"
import { useExtensionState } from "@/context/ExtensionStateContext"
import ServersToggleList from "./ServersToggleList"
import i18n from "@/i18n"
const InstalledServersView = () => {
	const { mcpServers: servers } = useExtensionState()

	return (
		<div style={{ padding: "16px 20px" }}>
			<div
				style={{
					color: "var(--vscode-foreground)",
					fontSize: "13px",
					marginBottom: "16px",
					marginTop: "5px",
				}}>
				{i18n.getDefinedValue("setting.mcp.installed.desc_prefix1")}
				<VSCodeLink href="https://github.com/modelcontextprotocol" style={{ display: "inline" }}>
				{i18n.get("setting.mcp.installed.desc_prefix2")}
				</VSCodeLink>
				{i18n.get("setting.mcp.installed.desc_prefix3")}
				<VSCodeLink href="https://github.com/modelcontextprotocol/servers" style={{ display: "inline" }}>
					{i18n.get("setting.mcp.installed.desc_prefix4")}
				</VSCodeLink>
				{i18n.get("setting.mcp.installed.desc_prefix5")}
			</div>

			<ServersToggleList servers={servers} isExpandable={true} hasTrashIcon={false} />

			{/* Settings Section */}
			<div style={{ marginBottom: "20px", marginTop: 10 }}>
				<VSCodeButton
					appearance="secondary"
					style={{ width: "100%", marginBottom: "5px" }}
					onClick={() => {
						vscode.postMessage({ type: "openMcpSettings" })
					}}>
					<span className="codicon codicon-server" style={{ marginRight: "6px" }}></span>
					{i18n.get("setting.mcp.installed.config_server")}
				</VSCodeButton>

				{/* <div style={{ textAlign: "center" }}>
					<VSCodeLink
						onClick={() => {
							vscode.postMessage({
								type: "openExtensionSettings",
								text: "cline.mcp",
							})
						}}
						style={{ fontSize: "12px" }}>
						{i18n.get("setting.mcp.installed.advanced_settings")}
					</VSCodeLink>
				</div> */}
			</div>
		</div>
	)
}

export default InstalledServersView
