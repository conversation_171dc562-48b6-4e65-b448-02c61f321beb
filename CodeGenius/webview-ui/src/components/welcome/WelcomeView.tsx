import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { useEffect, useState } from "react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { validateApiConfiguration } from "@/utils/validate"
import { vscode } from "@/utils/vscode"
import ApiOptions from "@/components/settings/ApiOptions"
import i18n from "@/i18n"

const WelcomeView = () => {
	const { apiConfiguration } = useExtensionState()

	const [apiErrorMessage, setApiErrorMessage] = useState<string | undefined>(undefined)

	const disableLetsGoButton = apiErrorMessage != null

	const handleSubmit = () => {
		if (apiConfiguration?.apiProvider === "qwen-native" || apiConfiguration?.apiProvider === "llama") {
			//apiConfiguration.qwenModelApiKey = 'xxxx'
		}
		vscode.postMessage({ type: "apiConfiguration", apiConfiguration })
	}

	useEffect(() => {
		setApiErrorMessage(validateApiConfiguration(apiConfiguration))
	}, [apiConfiguration])

	return (
		<div
			style={{
				position: "fixed",
				top: 0,
				left: 0,
				right: 0,
				bottom: 0,
				padding: "0 20px",
			}}>
			<h2>{i18n.get('welcome.title')}</h2>
			<p>
				{i18n.get('welcome.describe.0')}
				{i18n.get('welcome.describe.1')}
				{i18n.get('welcome.describe.2')}
			</p>
			

			<b>{i18n.get('welcome.require')}</b>

			<div style={{ marginTop: "10px" }}>
				<ApiOptions showModelOptions={false} />
				<VSCodeButton onClick={handleSubmit} disabled={disableLetsGoButton} style={{ marginTop: "3px" }}>
				{i18n.get('welcome.begin')}
				</VSCodeButton>
			</div>
		</div>
	)
}

export default WelcomeView
