import en from './locales/en';
import zh from './locales/zh';

export const LANG_TYPE = {
  ZH: 'zh-CN',
  EN: 'en-US',
}

const template_matcher: RegExp = /{{\s?([^{}\s]*)\s?}}/g;

class I18n {
  private locales: { [_: string]: any } = { [LANG_TYPE.ZH]: zh, [LANG_TYPE.EN]: en };
  private locale!: { [_: string]: any };

  constructor() {
    this.setLocale(this.getLang() || LANG_TYPE.ZH);
  }

  setLanguage(lang: string, options?: { refresh?: boolean }) {
    this.setLang(lang);
    this.setLocale(lang);
    options?.refresh && window.location.reload();
  }

  get(key: string, interpolation?: any): any {
    return this.getDefinedValue(
      key, interpolation) || key;
  }

  getDefinedValue(key: string, interpolation?: any): any {
    let value;
    if (key.indexOf('.')) {
      const keys = key.split('.');
      let _locale = this.locale;
      keys.forEach(k => _locale = _locale[k]);
      value = _locale;
    } else {
      value = this.locale[key];
    }

    if (value) {
      return this.interpolate(
        value, interpolation);
    } else {
      return value === undefined ? key : value;
    }
  }

  private interpolate(expr: any, params?: any): string {
    if (typeof expr === 'function') {
      return expr(params);
    } else if (typeof expr === 'string') {
      if (!params) {
        return expr;
      } else {
        return expr.replace(template_matcher,
          (substr: string, key: string) => {
            let res = this.getValue(params, key);
            return res ?? substr;
          });
      }
    } else {
      return expr as string;
    }
  }

  private getValue(target: any, key: string): any {
    const keys = typeof key === 'string'
      ? key.split('.') : [key];

    let k = '';
    do {
      k += keys.shift();
      if (target && target[k] && (
        typeof target[k] === 'object' || !keys.length)) {
        target = target[k];
        k = '';
      } else if (!keys.length) {
        target = undefined;
      } else {
        k += '.';
      }
    } while (keys.length);

    return target;
  }

  private setLang(lang: string) {
    window.localStorage.setItem('lang', lang);
  }

  public getLang() {
    return window.localStorage.getItem('lang') || LANG_TYPE.ZH;
  }

  private setLocale(lang: string) {
    this.locale = this.locales[lang];
  }
}

let i18n!: I18n;
if (!i18n) {
  i18n = new I18n();
}

export default i18n;