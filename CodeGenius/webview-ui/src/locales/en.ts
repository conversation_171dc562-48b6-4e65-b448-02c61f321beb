export default {
  "welcomeTitle": "What can I do for you?",
  "welcomeDesc": {
    "0": "Thanks to",
    "1": "I can handle complex software development tasks step-by-step. With tools that let me create & edit files, explore complex projects, use the browser, and execute terminal commands (after you grant permission), I can assist you in ways that go beyond code completion or tech support. I can even use MCP to create new tools and extend my own capabilities."
  },
  "welcome": {
    "title": "Hi, I'm zero employee",
    "describe": {
      "0": "Thanks to",
      "1": "zero agents agentic coding capabilities",
      "2": "I can handle complex software development tasks step-by-step. With tools that let me create & edit files, explore complex projects, use the browser, and execute terminal commands (after you grant permission), I can assist you in ways that go beyond code completion or tech support. I can even use MCP to create new tools and extend my own capabilities."
    },
    "require": "Zero requires LLM API provisioning capabilities.",
    "begin": "Let's go!",
  },
  "setting": {
    "name": "Settings",
    "save": "Save",
    "tab_plan_mode": "Plan Mode",
    "tab_act_mode": "Act Mode",
    "api_provider": {
      "name": "API Provider",
      "note": "Note: Employee Zero used complex prompt words. The stronger the model capability, the better the generation effect.",
      "deepseek": {
        "name": "DeepSeek",
        "api_key": "DeepSeek API Key",
        "describe": "This key is stored locally and only used to make API requests from this extension.",
        "describe_1": "You can get a DeepSeek API key by signing up here.",
      },
      "openai": {
        "ask_version": "Set Azure API version",
        "complex_model_tip": "Zero uses complex prompts and works best with Claude	models. Less capable models may not work as expected.",
      },
      "qwen_coder": "Qwen-Coder",
      "nebula": "Nebula-Coder",
      "nebula-reason": "Nebula-Reason-Coder",
      "llama_coder": "Llama-Coder",
      "api_key_placeholder": "Enter API Key...",
      "api_url_placeholder": "Enter base url...",
      "api_model_id_placeholder": "Enter model ID...",
    },
    "model": {
      "name": "Model",
      "model_id": "Model ID",
      "model_url": "Model URL",
      "model_key": "Model Key",
      "select": "Select a model...",
      "info": {
        "image": {
          "support": "Supports images",
          "not_support": "Does not support images",
        },
        "browser": {
          "support": "Supports browser use",
          "not_support": "Does not support browser use",
        },
        "computer": {
          "support": "Supports computer use",
          "not_support": "Does not support computer use",
        },
        "prompt": {
          "support": "Supports prompt caching",
          "not_support": "Does not support prompt caching",
        },
      },
      "capacity": {
        "max_tokens": "Max Output Tokens",
        "input_price": "Input price",
        "cache_writes_price": "Cache writes price",
        "cache_reads_price": "Cache reads price",
        "output_price": "Output price",
        "million": "million",
      },
      "configuration": {
        "configuration": "Model Configuration",
        "support_image": "Support Images",
        "context_window_size": "Context Window Size",
        "max_output_tokens": "Max Output Tokens",
        "input_price": "Input Price / 1M tokens",
        "output_price": "Output Price / 1M tokens",
        "enable_r1_format": "Enable R1 messages format",
        "temperature": "Temperature"
      },
      "header":{
        "custom_header": "Costum Headers",
        "add_header": "Add Header",
        "remove": "Remove"
      }
    },
    "view": {
      "done": "done",
      "custom_instructions": "Custom Instructions",
      "describe": "These instructions are added to the end of the system prompt sent with every request.",
      "placeholder": "e.g. Run unit tests at the end, Use TypeScript with async/await, Speak in Spanish",
      "use_different_model": "Use different models for Plan and Act modes",
      "use_different_model_desc": "Switching between Plan and Act mode will persist the API and model used in the previous mode. This may be helpful e.g. when using a strong reasoning model to architect a plan for a cheaper coding model to act on.",
      "dev": {
        "reset_tate": "Reset State",
        "describe": "This will reset all global state and secret storage in the extension.",
      },
      "feedback": {
        "describe": "If you have any questions or feedback, feel free to open an issue at",
        "link": "feedback url",
      },
    },
    "mcp": {
      "done": "Done",
      "marketplace": {
        "title": "Marketplace",
        "retry": "Retry",
        "searching": "Search MCPs...",
        "most_installs": "Most Installs",
        "github_stars": "GitHub Stars",
        "name": "Name",
        "no_match_found": "No matching MCP servers found",
        "not_in_market": "No MCP servers found in the marketplace",
        "require_api_key": "Requires API key",
        "submit_card_desc_prefix": "Help others discover great MCP servers by submitting an issue to ",
        "employee_zero_official": "Employee Zero Official"
      },
      "remote_servers": {
        "title": "Servers",
        "desc_prefix": "Add a remote MCP server by providing a name and its URL endpoint. Learn more ",
        "desc_suffix": "here.",
        "server_name": "Server Name",
        "server_url": "Sever URL",
        "connecting_server": "Connecting to server... This may take a few seconds.",
        "adding": "Adding...",
        "add_server": {
          "title": "Add Server",
          "desc_prefix1": "Add a local MCP server by configuring it in ",
          "desc_prefix2": ". You'll need to specify the server name, command, arguments, and any required environment variables in the JSON configuration.",
          "desc_suffix": "here.",
          "open_prefix": "Open ",
        },
        "edit_config": "Edit Configuration",
        "error": {
          "name_required": "Server name is required",
          "url_required": "Server URL is required",
          "invalid_url": "Invalid URL format",
        }
      },
      "installed": {
        "title": "Installed",
        "desc_prefix1": "The ",
        "desc_prefix2": "Model Context Protocol",
        "desc_prefix3": "enables communication with locally running MCP servers that provide additional tools and resources to extend	Zero Agents's capabilities. You can use ",
        "desc_prefix4": "community-made servers",
        "desc_prefix5": " or ask Zero Agents to create new tools specific to your workflow (e.g., \"add a tool that gets the latest npm docs\"). ",
        "desc_suffix": "See a demo here.",
        "config_server": "Config MCP Servers",
        "advanced_settings": "Advanced MCP Settings",
        "no_desc": "No description",
        "unknown": "Unknown",
        "parameters": "Parameters",
        "auto_approve": "Auto-approve",
        "retrying": "Retrying...",
        "retry_connect": "Retry Connection",
        "deleting": "Deleting...",
        "delete_server": "Delete Server",
        "restarting": "Restarting...",
        "restart_server": "Restart Server",
        "auto_approve_all": "Auto-approve all tools",
        "no_tools_found": "No tools found",
        "no_res_found": "No resources found"
      }
    }
  },
  "history": {
    "recent_tasks": "Recent Tasks",
    "view_all_history": "View all history",
    "delete_all_history": "Delete All History",
    "tokens": "Tokens:",
    "cache": "Cache:",
    "api_cost": "API Cost: $",
    "history": "History",
    "done": "Done",
    "fuzzy_search": "Fuzzy search history...",
    "clear_search": "Clear search",
    "newest": "Newest",
    "oldest": "Oldest",
    "most_expensive": "Most Expensive",
    "most_tokens": "Most Tokens",
    "most_relevant": "Most Relevant",
    "export": "EXPORT",
    "start_task": "Start a task to see it here"
  },
  "common": {
    "button_compare": "Compare",
    "button_restore": "Restore",
    "restore_both": "Restore Task and Workspace",
    "restore_both_desc": "Restores the task and your project's files back to a snapshot taken at this point",
    "restore_task": "Restore Task Only",
    "restore_task_desc": "Deletes messages after this point (does not affect workspace)",
    "restore_workspace": "Restore Workspace Only",
    "restore_workspace_desc": "Restores your project's files to a snapshot taken at this point (task may become out of sync)",
    "user_edits": "User Edits",
    "console_logs": "Console Logs",
    "restore_files": "Restore Files",
    "restore_files_desc": `Restores your project's files back to a snapshot taken at this point (use "Compare" to see what will be reverted)`,
    "restore_task_only": "Restore Task Only",
    "restore_task_only_desc": "Deletes messages after this point (does not affect workspace files)",
    "restore_file_&_task": "Restore Files & Task",
    "restore_file_&_task_desc": "Restores your project's files and deletes all messages after this point"
  },
  "chat": {
    "input_task_placeholder": "Type your task here (@ to add context)...",
    "input_message_placeholder": "Type a message (@ to add context)...",
    "auto_approve_settings": {
      "auto_approve": "Auto-approve:",
      "auto_approve_description": "Auto-approve allows Zero to perform the following actions without asking for permission. Please use with caution and only enable if you understand the risks.",
      "read_file": {
        "label": "Read project files",
        "short_name": "Read Local",
        "description": "Allows access to read files within your workspace."
      },
      "read_file_external": {
        "label": "Read all files",
        "short_name": "Read (all)",
        "description": "Allows access to read any file on your computer."
      },
      "edit_file": {
        "label": "Edit project files",
        "short_name": "Edit",
        "description": "Allows modification of files within your workspace."
      },
      "edit_file_external": {
        "label": "Edit all files",
        "short_name": "Edit (all)",
        "description": "Allows modification of any files on your computer."
      },
      "execute_commands": {
        "label": "Execute safe commands",
        "short_name": "Commands",
        "description": "Allows execution of safe terminal commands. If the model determines a command is potentially destructive, it will still require approval."
      },
      "execute_all_commands": {
        "label": "Execute all commands",
        "short_name": "All Commands",
        "description": "Allows execution of all terminal commands. Use at your own risk."
      },
      "use_browser": {
        "label": "Use the browser",
        "short_name": "Browser",
        "description": "Allows ability to launch and interact with any website in a headless browser."
      },
      "context_menu": {
        "no_results_found": "No results found",
        "problems": "Problems",
        "terminal": "Terminal",
        "git_commits": "Git Commits",
        "searching": "Searching...",
        "add": "Add",
        "file": "File",
        "folder": "Folder",
        "paste_url_to_fetch_contents": "Paste URL to fetch contents"
      },
      "use_mcp": {
        "label": "Use MCP servers",
        "short_name": "MCP",
        "description": "Allows use of configured MCP servers which may modify filesystem or interact with APIs."
      },
      "max_requests": "Max Requests:",
      "max_requests_description": "Zero Agents will automatically make this many API requests before asking for approval to proceed with the task.",
      "enable_notification": "Enable Notifications",
      "enable_notification_description": "Receive system notifications when Zero Agents requires approval to proceed or when a task is completed."
    },
    "bottom_bar": {
      "add_context": "Add Context",
      "add_image": "Add Image",
      "manage_mcp_servers": "Manage MCP Servers",
      "mcp_servers": "MCP Servers",
      "no_mcp_installed": "No MCP severs installed",
      "manage_rules": "Manage Zero Agents Rules",
      "rules_title": "Zero Agents Rules",
      "global_rules": "Global Rules",
      "workspace_rules": "Workspace Rules",
      "no_rules_found": "No rules found",
      "new_rule_file": "New rule file",
      "new_rule_file_Ellipses": "New rule file...",
      "rule_name_format": "rule-name (.md, .txt, or no extension)",
      "select_model": "Select Model / API Provider",
      "plan": "Plan",
      "plan_tooltip": "In Plan mode, Zero Agents will gather information to architect a plan",
      "act": "Act",
      "act_tooltip": "In Act mode, Zero Agents will complete the task immediately",
      "toggle_mode_hint": "Toggle w/ {{0}}+Shift+A"
    },
    "retry": "Retry",
    "new_task": "Start New Task",
    "new_ctx_task": "Start New Task with Context",
    "proceed_anyways": "Proceed Anyways",
    "proceed": "Proceed",
    "save": "Save",
    "reject": "Reject",
    "approve": "Approve",
    "run_command": "Run Command",
    "resume_task": "Resume Task",
    "proceed_while_running": "Proceed While Running",
    "cancel": "Cancel",
    "want_read_file": "Zero Agents wants to read this file:",
    "read_file": "Read this file:",
    "want_create_file": "Zero Agents wants to create a new file:",
    "create_file": "Zero Agents is creating a new file:",
    "want_edit_file": "Zero Agents wants to edit this file:",
    "edit_file": "Zero Agents is editing this file:",
    "api_request_cancelled": "API Request Cancelled",
    "api_streaming_failed": "API Streaming Failed",
    "api_request": "API Request",
    "api_requesting": "API Request...",
    "api_request_failed": "API Request Failed",
    "has_a_question": "Zero Agents has a question:",
    "view_top_files": "Zero Agents wants to recursively view all files in this directory:",
    "viewed_top_files": "Zero Agents viewed the top level files in this directory:",
    "view_all_files": "Zero Agents wants to view the top level files in this directory:",
    "viewed_all_files": "Zero Agents recursively viewed all files in this directory:",
    "view_source_code": "Zero Agents wants to view source code definition names used in this directory:",
    "viewed_source_code": "Zero Agents viewed source code definition names used in this directory:",
    "model_determined_command_requires_approval": "The model has determined this command requires explicit approval.",
    "use_browser": "Zero Agents wants to use the browser:",
    "shell_integration_unavailable": "Shell Integration Unavailable",
    "using_browser": "Zero Agents is using the browser:",
    "execute_command": "Zero Agents wants to execute this command:",
    "executed_command": "Zero Agents executed this command:",
    "see_new_changes": "See new changes",
    "not_match": "This usually happens when the model uses search patterns that don't match anything in the file. Retrying...",
    "diff_edit_failed": "Diff Edit Failed",
    "still_having_trouble": "Still having trouble?",
    "task": "Task",
    "see_less": "See less",
    "see_more": "See more",
    "task_complete": "Task Completed",
    "context_window": "Context Window:",
    "api_cost": "API Cost:",
    "chatIndexingProgress": {
      "indexing": "Codebase indexing...",
      "done": "Codebase indexing complete"
    }
  }
}