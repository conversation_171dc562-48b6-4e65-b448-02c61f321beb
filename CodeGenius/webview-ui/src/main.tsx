import { StrictMode } from "react"
import { createRoot } from "react-dom/client"
import "./index.css"
import App from "./App.tsx"
import "../../node_modules/@vscode/codicons/dist/codicon.css"
import { ErrorBoundary, FallbackProps } from "react-error-boundary";

const fallbackRender = ({error}: FallbackProps) => {
	return (
		<div role="alert">
			<p>Something went wrong:</p>
			<pre style={{ color: "red" }}>{error.message}</pre>
			<p>Call stack details:</p>
			<pre style={{ color: "red" }}>{error.stack}</pre>
		</div>
	)
}

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ErrorBoundary fallbackRender={fallbackRender}>
			<App />
		</ErrorBoundary>
	</StrictMode>,
)
