# 排除不需要的文件和目录
exclude tests/*
exclude setup.py
exclude MANIFEST.in
exclude src/application/services/repair_service/*
exclude src/domain/subdomains/repair_agent/config/*

# 包含特定的服务文件
include src/application/services/repair_service/repair_agent_cline_mcp_service.py
include src/application/services/repair_service/mcp_repair_agent_cline.py

# 包含配置文件
include src/domain/subdomains/repair_agent/config/load_config.py
include src/domain/subdomains/repair_agent/config/mcp_repair_default.yaml
include src/domain/subdomains/repair_agent/config/yaml_assemble.py

# 包含repair_agent_setup_files目录
recursive-include repair_agent_setup_files *