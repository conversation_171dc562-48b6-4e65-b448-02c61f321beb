/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Codeview: typeof import('./src/components/todolist/codeview.vue')['default']
    Console: typeof import('./src/components/console/console.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    Help: typeof import('./src/components/help/help.vue')['default']
    History: typeof import('./src/components/history/history.vue')['default']
    Setting: typeof import('./src/components/setting/setting.vue')['default']
    Streamlisten: typeof import('./src/components/todolist/streamlisten.vue')['default']
    Task: typeof import('./src/components/todolist/task.vue')['default']
    Test: typeof import('./src/components/usrInput/test/test.vue')['default']
    Todolist: typeof import('./src/components/todolist/todolist.vue')['default']
    Usrinput: typeof import('./src/components/usrInput/usrinput.vue')['default']
  }
}
