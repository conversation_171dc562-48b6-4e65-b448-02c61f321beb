# Started by AICoder, pid:y901f5a5d508b0a14a90092200c0f635b5d82779 
#!/bin/bash

# 配置参数
MVN_PROJECT_PATH=$(cd $(dirname $0); pwd)  # Maven项目路径
PACKAGE_PREFIX="employee-zero-iui"             # tar包前缀
REMOTE_USER="root"                  # 远程服务器用户名
REMOTE_HOST="************"     # 远程服务器地址
REMOTE_PATH="/home/<USER>/apache-tomcat-11.0.1-remote/webapps"  # 远程目录
REMOTE_PASSWORD="ZEROAGENT@uss100"              # 远程服务器密码

# 步骤 1: 编译Maven项目，生成tar包
cd "$MVN_PROJECT_PATH" || { echo "项目路径不存在: $MVN_PROJECT_PATH"; exit 1; }
echo "正在编译Maven项目..."
mvn clean install || { echo "Maven编译失败"; exit 1; }

# 确认tar包是否生成
TAR_FILE=$(ls target | grep "^$PACKAGE_PREFIX.*\.tar\.gz$")
if [ -z "$TAR_FILE" ]; then
    echo "目标tar包未找到: 以$PACKAGE_PREFIX为前缀的tar包"
    exit 1
fi

echo "Maven编译成功，生成tar包: target/$TAR_FILE"

# 步骤 2: 上传tar包到远程服务器
echo "正在上传tar包到远程服务器: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH"
echo "$REMOTE_PASSWORD" | sshpass -p "$REMOTE_PASSWORD" scp "target/$TAR_FILE" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH" || { echo "tar包上传失败"; exit 1; }

# 步骤 3: 连接到远程服务器并删除旧目录，然后解压tar包
echo "正在连接到远程服务器并删除旧目录，然后解压tar包..."
echo "$REMOTE_PASSWORD" | sshpass -p "$REMOTE_PASSWORD" ssh "$REMOTE_USER@$REMOTE_HOST" << EOF
    cd "$REMOTE_PATH" || { echo "目标目录不存在: $REMOTE_PATH"; exit 1; }
    rm -rf iui || { echo "删除旧目录iui失败"; exit 1; }
    tar -xzf "$TAR_FILE" || { echo "解压tar包失败"; exit 1; }
    echo "tar包解压成功"
EOF

echo "脚本执行完毕"
# Ended by AICoder, pid:y901f5a5d508b0a14a90092200c0f635b5d82779 