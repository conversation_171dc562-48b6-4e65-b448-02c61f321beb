<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + Vue + TS</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="src/main.ts" style="width: 100%; height: 100%; grid-template-columns:none;position: fixed;top: 0;left: 0;"></script>
    <script>
      // 监听从父窗口传来的消息
     window.addEventListener('message', function(event) {
       if (event.data?.type !== 'vscodeStyle') {
         console.log('Message received not style:', event.data);
         return;
       }
       // 处理接收到的消息
       console.log('Message received from parent:', event.data);
       // 动态修改 :root 的 CSS 变量
       const style = document.createElement('style');
       style.innerText = ":root {"+event.data.data+"}";
       document.querySelector('head').appendChild(style)

     });
   </script>
  </body>
</html>
