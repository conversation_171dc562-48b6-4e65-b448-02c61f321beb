#!/bin/bash

# Step 1: Extract node-modules.tar.gz
if [ -f "node-modules.tar.gz" ]; then
    tar -xzvf node-modules.tar.gz
else
    echo "node-modules.tar.gz not found!"
    exit 1
fi

# Step 2: Run npm build-only
if command -v npm &> /dev/null; then
    npm run build-only
else
    echo "npm is not installed!"
    exit 1
fi

# Step 3: Create a temporary directory and package
mkdir -p iui/employee-zero-iui/
cp -r dist/* iui/employee-zero-iui/
tar -czvf employee-zero-iui.tar.gz -C ./ iui/employee-zero-iui/
rm -rf iui
