{"name": "employeezero-iui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "preview": "vite preview", "build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.8", "element-plus": "^2.8.6", "lodash": "^4.17.21", "mark": "^0.10.0", "markdown-it": "^14.1.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-multimd-table": "^4.2.3", "markdown-it-plantuml": "^1.4.1", "markdown-it-prism": "^2.3.0", "mitt": "^3.0.1", "pinia": "^2.2.4", "prismjs": "^1.29.0", "uuid": "^11.0.2", "vue": "^3.5.13"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/axios": "^0.9.36", "@types/markdown-it": "^14.1.2", "@types/markdown-it-plantuml": "^1.4.5", "@types/node": "^20.17.1", "@types/prismjs": "^1.26.5", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.2.3", "typescript": "~5.6.2", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^6.0.0", "vue-tsc": "^2.1.10"}}