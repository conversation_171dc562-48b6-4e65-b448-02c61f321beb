<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zte.ums.zenap</groupId>
        <artifactId>goadf-parentpom</artifactId>
        <version>3.25.10.03-SNAPSHOT</version>
    </parent>

    <groupId>com.zte.employeezero</groupId>
    <artifactId>employee-zero-iui</artifactId>
    <version>3.25.10.03-SNAPSHOT</version>
    <packaging>pom</packaging>

    <profiles>
        <profile>
            <id>platform-windows</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <build>
                <plugins>
                    <!-- 第 1 步：清理 target, dist目录  -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-clean-plugin</artifactId>
                        <version>3.0.0</version>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>dist</directory>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                </fileset>
                                <fileset>
                                    <directory>target</directory>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>

                    <!-- 第 2 步：编译输出到 dist目录  -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>vue-cli install</id>
                                <configuration>
                                    <workingDirectory>${basedir}</workingDirectory>
                                    <executable>cmd</executable>
                                    <arguments>
                                        <argument>/c</argument>
                                        <argument>"npm install"</argument>
                                    </arguments>
                                </configuration>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>vue-cli build</id>
                                <configuration>
                                    <workingDirectory>${basedir}</workingDirectory>
                                    <executable>cmd</executable>
                                    <arguments>
                                        <argument>/c</argument>
                                        <argument>"ng build-only"</argument>
                                    </arguments>
                                </configuration>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>platform-unix</id>
            <activation>
                <os>
                    <family>unix</family>
                </os>
            </activation>
            <build>
                <plugins>
                    <!-- 第 1 步：清理 target, dist目录  -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-clean-plugin</artifactId>
                        <version>3.0.0</version>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>dist</directory>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                    <directory>target</directory>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>

                    <!-- 第 2 步：编译输出到 dist目录  -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>vue-cli install</id>
                                <configuration>
                                    <workingDirectory>${basedir}</workingDirectory>
                                    <executable>npm</executable>
                                    <arguments>
                                        <argument>install</argument>
                                        <argument>--unsafe-perm</argument>
                                        <argument>--prefer-offline</argument>
                                    </arguments>
                                </configuration>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>vue-cli build</id>
                                <configuration>
                                    <workingDirectory>${basedir}</workingDirectory>
                                    <executable>npm</executable>
                                    <arguments>
                                        <argument>run</argument>
                                        <argument>build-only</argument>
                                    </arguments>
                                </configuration>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- 第 3 步：生成timestamp -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.4</version>
                <configuration>
                    <timestampFormat>yyMMddHHmm</timestampFormat>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>create-timestamp</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 第 4 步：dist目录压缩gz文件并输出到到 target目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>

                    <execution>
                        <id>distribution</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target name="distribution">
                                <tar destfile="target/employee-zero-iui-${project.version}.tar.gz"
                                     compression="gzip">
                                    <tarfileset prefix="/iui/employee-zero-iui" dir="dist" filemode="0540" dirmode="0750" uid="3001" gid="3001" username="oes" group="oes" />
                                </tar>
                                <attachartifact file="target/employee-zero-iui-${project.version}.tar.gz" type="tar.gz"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
