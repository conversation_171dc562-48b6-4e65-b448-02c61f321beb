/* Started by AICoder, pid:26dd0s73e82dbbf148a50b6510627b3422a0b194 */
<script setup lang="ts">
import {onMounted, ref, toRefs, watch} from "vue";
import UsrInputVue from "./components/usrInput/usrinput.vue";
import Todolist from "./components/todolist/todolist.vue";
import Setting from "./components/setting/setting.vue";
import Help from "./components/help/help.vue";
import History from "@/components/history/history.vue";
import {
  fileContentCatchStatus,
  fileListCatchStatus,
  showHistoryChat,
  toggleNewChat,
  useVscodeParamStore,
} from "@/stores/vscodeparam"

/* Started by AICoder, pid:r84dcvb5245fde814df808e14095341da2c3c908 */
const mode = ref("");
const setMode = () => {
  const urlSearch = window.location.search;
  if (urlSearch === "") {
    mode.value = "zero";
  } else {
    mode.value = urlSearch.split("?mode=")[1];
  }
};

onMounted(async () => {
  currentTaskDirPath.value = getCurrentFormattedTime()
  console.info("全新会话", currentTaskDirPath.value);
  setMode();
  showHistoryChat.value = true
  fileListCatchStatus.value = false
  fileContentCatchStatus.value = false
});
/* Ended by AICoder, pid:r84dcvb5245fde814df808e14095341da2c3c908 */

const vscodeParamStore = useVscodeParamStore()
const {currentTaskDirPath} = toRefs(vscodeParamStore)
const {getFileList, deleteFile, getUacInfo} = vscodeParamStore

watch(toggleNewChat, (newValue, oldValue) => {
  if (oldValue === false && newValue === true) {
    newChat()
  }
})

const newChat = async () => {
  // await waitResponseCatched(fileListCatchStatus, false)
  // getFileList('employee-zero-data/ZeroAiFiles')
  // await waitResponseCatched(fileListCatchStatus, false)
  // let localFileList = fileList.value
  // if (localFileList.length > 50) {
  //   const dirWithDates: { dir: string; date: Date }[] = parsedDateDirList(localFileList)
  //   dirWithDates.splice(50).forEach(item => {
  //     deleteFile(`employee-zero-data/ZeroAiFiles/${item.dir}`)
  //   })
  // }

  currentTaskDirPath.value = getCurrentFormattedTime()
  console.info("新会话", currentTaskDirPath.value);
  toggleNewChat.value = false
  showHistoryChat.value = true
}

function getCurrentFormattedTime(): string {
  const now = new Date();
  const year = now.getFullYear(); // 获取年份
  const month = String(now.getMonth() + 1).padStart(2, "0"); // 获取月份，注意月份从0开始
  const day = String(now.getDate()).padStart(2, "0"); // 获取日期
  const hours = String(now.getHours()).padStart(2, "0"); // 获取小时
  const minutes = String(now.getMinutes()).padStart(2, "0"); // 获取分钟
  const seconds = String(now.getSeconds()).padStart(2, "0"); // 获取秒
  // 拼接成所需格式
  return `/employee-zero-data/ZeroAiFiles/ZeroAiTemp${year}${month}${day}${hours}${minutes}${seconds}`;
}

</script>

/* Started by AICoder, pid:17d416172332684141940bf020fa771b3667aa53 */
<template>
  <div v-if="mode === 'setting'">
    <Setting/>
  </div>
  <div v-if="mode === 'help'">
    <Help/>
  </div>
  <el-container class="main-body" v-if="mode === 'zero'">
    <el-scrollbar>
      <el-main style="height: 100%; min-height: 500px; overflow: visible">
        <UsrInputVue/>
        <History v-if="showHistoryChat === true"/>
        <Todolist/>
        <!-- <TestComponent /> -->
      </el-main>
    </el-scrollbar>
  </el-container>
</template>
/* Ended by AICoder, pid:17d416172332684141940bf020fa771b3667aa53 */

<style scoped>
.main-body {
  /* width: 100%; */

  width: 100%;
  height: 100%;
  grid-template-columns: none;
  position: fixed;
  top: 0;
  left: 0;

  background: var(--vscode-panel-background);
}

/* Started by AICoder, pid:5b098r59dfhb47414abf0a3600a4172147e1aa2d */
/* 自定义滚动条样式 */
.el-scrollbar {
  overflow-y: auto;
  width: 100%;
  /* 允许垂直滚动 */
}

/* Webkit 浏览器（如 Chrome 和 Safari） */
.el-scrollbar::-webkit-scrollbar {
  width: 4px;
  /* 设置滚动条宽度为更小的值 */
  background: transparent;
  /* 滚动条轨道背景 */
  display: none;
  /* 隐藏完整的滚动条 */
}

.el-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
  /* 滚动条颜色 */
  border-radius: 10px;
  /* 圆角 */
  height: 30px;
  /* 设置滚动条的高度，调整长度 */
}

.el-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  /* 滚动条轨道背景 */
}

.el-main {
  --el-main-padding: 5px;
}

/* Ended by AICoder, pid:5b098r59dfhb47414abf0a3600a4172147e1aa2d */
</style>
/* Ended by AICoder, pid:26dd0s73e82dbbf148a50b6510627b3422a0b194 */