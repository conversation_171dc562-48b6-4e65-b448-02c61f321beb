<?xml version="1.0" encoding="UTF-8" ?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="654px" height="163px" viewBox="-0.5 -0.5 654 163" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2025-01-06T02:39:29.042Z&quot; agent=&quot;5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/20.8.16 Chrome/106.0.5249.199 Electron/21.4.0 Safari/537.36&quot; etag=&quot;ujoeUVSyqFUC6i-TI44w&quot; version=&quot;20.8.16&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;C5RBs43oDa-KdzZeNtuy&quot; name=&quot;Page-1&quot;&gt;5Vtbc5s4FP41enQGEGB4xJdk0213spvdTfLUoUax2WLLC3Ji59dXAnEzsi0Sc3E700nRQWD4vqNP5xwJAMfL7U3orhdfsIcCoCneFsAJ0DRVVXX6H7PsEosFtcQwD32Pd8oN9/4b4kaFWze+h6JSR4JxQPx12TjDqxWakZLNDUP8Wu72jIPyr67dOaoY7mduULU++B5Z8LfQhrn9N+TPF+kvq6adnFm6aWf+JtHC9fBrwQSnAI5DjElytNyOUcDAS3F5uN09BJ+/mzef/oz+d/8Z/f73H/8Okptd17kke4UQrci7b42Qf3eLr7/ez+/f1PGLq78NJgPNTO794gYbDhh/WbJLEUQeBZQ3cUgWeI5XbjDNraMQb1YeYr+j0Fbe5zPGa2pUqfE/RMiOe4e7IZiaFmQZ8LP0vcLdI2tcKYqZGp7YDa+MrMNky38hae2KrTsU+ktEUMiNkpBxaCO8CWfoSD/IPdcN54gcw5N3ZJgV/I8zcoMwfcZwRzuEKHCJ/1J2Upf7+jzrl/NJDzilNTwHVtmd6mA0Yv+mBrAc4KjxgQXsIZharOlYFQ/I+WVcvC58gu7XbgzYK1WNMpfPeEU40Sp989E8cKOIsxKREH/PxiHrnQ2q+qS9oJCg7VGU+Vl9yIfwLtUm3n7NFcHmpkVBDKDycV6EbmKfHnVzCvr6g4Bkqup+S2+rHAVKtd4JlNYQUPppoGo5ZxXQo/yc9rCuPMg4DQwdWutYbmcEhx1Aw8+aJY/SK/6kCWDTmoLNEgiiCSwbjGwwtYE1BNY1tzipIFrjCrT01UkZvUTZxjhgUE9WeMXmxWc/CPZMbuDPV7Q5oyizyWrEgPRpuOLwE0vf8+JJVURXeaJtirE9ygZahTOzTVdPXbt3aglTb+6LWmqC+b4NucwY6q9eahIzSTOCWQOcPiqmJphpWAx5DZwpU0pbj7WzElVetGTW5yzNJzlpXUtmNYZvI4vb+uSRX86Oef7GW3n2xhql5O0RFNLAwlVHkr56Wl/M744q58kEj4/G9vM7cRQoIfhtpu9KpyRCaRLVXrEID6bpDgTTIXAUYKnsgGqtnYSnNF9XKkxfeppeyT7N6tzXbpI17MXwOvcw0SWHCTR6NUwMQfL2K7Fh9ooNKFOaWeDlt030nlD6PIoyhPuKYlcUBbYZTUNRND1kUm/DOJqeMIWveHUQ+OvoUIRbwNGN1skayrO/ZT7eGLCWsg+sdmVUoYUCsYZNyYPdbeiTRztPxXMthT6mrIp0tkBx9Ln3Qx8LWKP0QJHILi899BnujadMlboKfeBPGfpIz7X9GiW6xFzbUQW0ZxF7mtm1XQHNGOpvBVTXTkPTTAW0Bjh9rIDqovTcYhVQyt/PsURUn6E9itQKRaJ6p2Y3RZHEcmgnAlnZedC5QEps+GlEII0DkPZIICVCnoYEUh6cXgqkaFU9WyIygKMDa3LhAlmboZ4JZF93HJmVQkLXNV+lI4G8gD1HEsF1QwJ52buODEHozdZ11FggLfbX0S9cID+6y6hjgTQkUuwLrHecfaGTX3qHffqAh8Pc4R5PScWFX7VHVfYYH2BPIv5vcQNEelxrA0S3a+e6bAE5nR97Uhoz+vEBw5nZSEE+Xajs136UVLI7W4PRulyESfOg0/tPzl5eji91wtDdFTqsmdxGh1XbUMuqrfFvva4P9Iemeqw/PUie4L3Srnz1nqzAJLr112D5ZXCLNIqVRDheSFykExJZ2uUTlxa/KREC1UzlWwBoEbjDlHWStggfRxB8nyVrOSM0HSctwkcVVb3jLWiOxbegjeIvJEbTeO+CASwDOEYF2j5lMednrLMkRvis/YiChXPb+6bZY0JXnGUPf/x4/kiINvNvfpNZLv9yGk5/AA==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <style>
        path {
        stroke: var(--vscode-editor-foreground, black);
        fill: var(--vscode-editor-background, none);
        }
        rect {
        stroke: var(--vscode-editor-foreground, black);
        fill: var(--vscode-editor-background, white);
        }
        text {
        fill: var(--vscode-editor-foreground, black);
        }
    </style>
    <g>

        <path d="M 290 17 L 262 17 L 240.91 17.23" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke" />
        <path d="M 235.66 17.29 L 242.62 13.71 L 240.91 17.23 L 242.7 20.71 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all" />
        <rect x="290" y="2" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all" />
        <g transform="translate(-0.5 -0.5)">

            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 17px; margin-left: 291px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">任务列表</div></div>
                    </div>
                </foreignObject>
                <text x="335" y="21" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">任务列表</text></switch>
        </g>
        <rect x="0" y="2" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <path d="M 6 26 C 6 18 6 14 16 14 C 9.33 14 9.33 6 16 6 C 22.67 6 22.67 14 16 14 C 26 14 26 18 26 26 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 15px; margin-left: 27px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">批量执行</div></div>
                    </div>
                </foreignObject>
                <text x="56" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">批量执行</text></switch>
        </g>
        <rect x="144" y="2" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <path d="M 150 26 C 150 18 150 14 160 14 C 153.33 14 153.33 6 160 6 C 166.67 6 166.67 14 160 14 C 170 14 170 18 170 26 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 19px; margin-left: 172px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">修改任务</div></div>
                    </div>
                </foreignObject>
                <text x="201" y="23" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">修改任务</text></switch>
        </g>
        <path d="M 144 17 L 96.37 17" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 91.12 17 L 98.12 13.5 L 96.37 17 L 98.12 20.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 90 81 L 133.63 81" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 138.88 81 L 131.88 84.5 L 133.63 81 L 131.88 77.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <rect x="0" y="66" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 81px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">代码生成</div></div>
                    </div>
                </foreignObject>
                <text x="45" y="85" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">代码生成</text></switch>
        </g>
        <path d="M 580 81 L 613.63 81" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 618.88 81 L 611.88 84.5 L 613.63 81 L 611.88 77.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 565 91 L 565 125.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 565 130.88 L 561.5 123.88 L 565 125.63 L 568.5 123.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 565 71 L 580 81 L 565 91 L 550 81 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <ellipse cx="636.5" cy="81" rx="16.5" ry="16.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 31px; height: 1px; padding-top: 81px; margin-left: 621px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">结束</div></div>
                    </div>
                </foreignObject>
                <text x="637" y="85" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">结束</text></switch>
        </g>
        <path d="M 520 147 L 45 147 L 45 102.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 45 97.12 L 48.5 104.12 L 45 102.37 L 41.5 104.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <rect x="520" y="132" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 147px; margin-left: 521px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">下一任务</div></div>
                    </div>
                </foreignObject>
                <text x="565" y="151" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">下一任务</text></switch>
        </g>
        <path d="M 45 32 L 45 52 L 45 46 L 45 59.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 45 64.88 L 41.5 57.88 L 45 59.63 L 48.5 57.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <rect x="140" y="66" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <path d="M 146 90 C 146 82 146 78 156 78 C 149.33 78 149.33 70 156 70 C 162.67 70 162.67 78 156 78 C 166 78 166 82 166 90 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 82px; margin-left: 167px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">运行</div></div>
                    </div>
                </foreignObject>
                <text x="196" y="85" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">运行</text></switch>
        </g>
        <rect x="290" y="66" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <path d="M 296 90 C 296 82 296 78 306 78 C 299.33 78 299.33 70 306 70 C 312.67 70 312.67 78 306 78 C 316 78 316 82 316 90 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 82px; margin-left: 317px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">修复</div></div>
                    </div>
                </foreignObject>
                <text x="346" y="85" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">修复</text></switch>
        </g>
        <rect x="420" y="66" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <path d="M 426 90 C 426 82 426 78 436 78 C 429.33 78 429.33 70 436 70 C 442.67 70 442.67 78 436 78 C 446 78 446 82 446 90 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 82px; margin-left: 447px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">确认</div></div>
                    </div>
                </foreignObject>
                <text x="476" y="85" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">确认</text></switch>
        </g>
        <path d="M 230 81 L 283.63 81" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 288.88 81 L 281.88 84.5 L 283.63 81 L 281.88 77.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 380 81 L 413.63 81" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 418.88 81 L 411.88 84.5 L 413.63 81 L 411.88 77.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 510 81 L 543.63 81" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 548.88 81 L 541.88 84.5 L 543.63 81 L 541.88 77.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 330 96 L 330 116 L 181 116 L 181 102.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 181 97.12 L 184.5 104.12 L 181 102.37 L 177.5 104.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <rect x="420" y="2" width="90" height="30" rx="4.5" ry="4.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none" />
        <path d="M 426 26 C 426 18 426 14 436 14 C 429.33 14 429.33 6 436 6 C 442.67 6 442.67 14 436 14 C 446 14 446 18 446 26 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 18px; margin-left: 447px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">用户输入</div></div>
                    </div>
                </foreignObject>
                <text x="476" y="21" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">用户输入</text></switch>
        </g>
        <path d="M 420 17 L 386.37 17" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" />
        <path d="M 381.12 17 L 388.12 13.5 L 386.37 17 L 388.12 20.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none" /></g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" />
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%"></text>
        </a>
    </switch>
</svg>