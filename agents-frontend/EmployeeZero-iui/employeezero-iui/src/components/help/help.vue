/* Started by AICoder, pid:e8f58w1b82hb6c81488e0a88e0296b461a481f12 */
<template>
  <div class="title-describe">0号员工使用流程</div>
  <div>
    <img src="@/assets/help.svg" alt="Help Icon" />
  </div>
  <!-- 描述 -->
  <div class="describe">
    <p>
      使用流程：
      <ol>
        <li>@文件或者Code，输入用户故事描述。</li>
        <li>大模型根据用户故事描述拆分任务，生成任务列表。每个任务包含描述、测试用例和文件路径，可以手动编辑任务列表。</li>
        <li>编辑任务列表后，点击“批量执行”，依次完成每个任务。</li>
        <li>
          每个任务包含以下操作：
          <ul>
            <li>生成代码。</li>
            <li>运行并修复问题，重复执行直至解决。</li>
            <li>确认生成的代码正确后，继续下一个任务。</li>
          </ul>
        </li>
      </ol>
    </p>
  </div>
  <el-button style="margin-top:8px" type="primary" @click="goToFeedback" round
        >问题反馈</el-button>
</template>

/* Started by AICoder, pid:5b75fnacc6s5a14142ea0bef90ba0d1f55c09684 */
<script setup lang="ts">
// No additional logic needed for this component
const goToFeedback = () => {
  const message = {
    type: "zero_openExternalWebLink",
    data: { url: "https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/8fe40a9f40294af8ab4e6c07c58ae2f8/view" }, // 填写反馈问题页面链接
  };
  window.parent.postMessage(message, "*");
};
</script>
/* Ended by AICoder, pid:5b75fnacc6s5a14142ea0bef90ba0d1f55c09684 */

<style scoped>
.title-describe {
  font-size: calc(var(--vscode-font-size) + 2px);
  color: var(--vscode-editor-foreground);
  padding-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--vscode-editorHoverWidget-border);
}

.describe {
  font-size: var(--vscode-font-size);
  color: var(--vscode-editor-foreground);
  padding-bottom: 4px;
  border-bottom: 1px solid var(--vscode-editorHoverWidget-border);
}
</style>
/* Ended by AICoder, pid:e8f58w1b82hb6c81488e0a88e0296b461a481f12 */
