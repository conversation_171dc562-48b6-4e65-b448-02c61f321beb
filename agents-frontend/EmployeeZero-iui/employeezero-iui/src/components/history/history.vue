<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, watch} from 'vue';
import {
  fileContentCatchStatus,
  fileContentMap,
  fileList,
  fileListCatchStatus,
  showHistoryChat,
  toggleHistoryChat,
  useVscodeParamStore,
  waitResponseCatched
} from '@/stores/vscodeparam'
import {parsedDateDirList} from '@/stores'
import emitter from '@/bus';
import {ElIcon} from 'element-plus';
import {Delete} from '@element-plus/icons-vue';

const vscodeParamStore = useVscodeParamStore()
const {currentZeroDirPath} = toRefs(vscodeParamStore)
const {getFileList, deleteFile, getFilesContent} = vscodeParamStore

// 定义 workflow 数据类型
interface Workflow {
  dir: string;
  text: string;
  time: string;
}

// 初始化工作流数组
const workflows = ref<Workflow[]>([]);
const zeroDescriptionMap = ref(new Map<string, string>())

onMounted(async () => {
  fileListCatchStatus.value = false
  fileContentCatchStatus.value = false
  await loadHistoryChat();
});

function categorizeDirsByDate(fileList: { file: string[]; dir: string[] }): Workflow[] {
  const categorizeWorkflows: Workflow[] = [];
  // 获取当前日期和时间
  const now = new Date();

  // 存储匹配的目录和日期对象
  const dirWithDates: { dir: string; date: Date }[] = parsedDateDirList(fileList);

  function formatTimeDiff(diffDays: number, diffSeconds: number): string {
    // If less than 60 seconds, display in seconds
    if (diffSeconds < 60) {
      return `${Math.floor(diffSeconds)}s`;
    }

    // If less than 60 minutes, display in minutes
    const diffMinutes = Math.floor(diffSeconds / 60);
    if (diffMinutes < 60) {
      return `${diffMinutes}m`;
    }

    // If less than 24 hours (1 day), display in hours
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffDays < 1) {
      return `${diffHours}h`;
    }

    // If less than 30 days, display in hours (calculated from days)
    if (diffDays < 30) {
      return `${diffHours}h`;
    }

    // If more than 30 days, display in months
    const diffMonths = Math.floor(diffDays / 30);
    return `${diffMonths}mon`;
  }

  // 对截取后的目录进行分类
  for (const {dir, date} of dirWithDates) {
    const diffTime = now.getTime() - date.getTime();
    const diffSeconds = diffTime / 1000;
    const diffDays = diffTime / (1000 * 60 * 60 * 24);

    const rawData = zeroDescriptionMap.value.get(`employee-zero-data/ZeroAiFiles/${dir}/codeInput/codeInput.json`) as string | undefined;

    if (rawData) {
      try {
        // 解析 JSON 数据
        const parsedData = JSON.parse(rawData);
        const inputs = parsedData.inputs;


        // 构造 workflowItem 对象
        const workflowItem: Workflow = {
          dir: dir,
          text: inputs,
          time: formatTimeDiff(diffDays, diffSeconds), // 确保 diffDays 已定义并正确计算
        };

        categorizeWorkflows.push(workflowItem);

        // 进一步处理 workflowItem，例如添加到一个数组或者其他逻辑
      } catch (error) {
        console.error("解析 JSON 数据时出错:", error);
      }
    } else {
      console.warn(`未找到对应的文件: employee-zero-data/ZeroAiFiles/${dir}/codeInput/codeInput.json`);
    }
  }
  console.log("categorizeWorkflows", categorizeWorkflows);

  return categorizeWorkflows;
}



watch(toggleHistoryChat, async (newVal) => {
  if (newVal === 'chat' || newVal === 'history') {
    await loadHistoryChat();  // 加载历史内容
  } else {
    workflows.value = [];  // 清空工作流
  }
});

const loadHistoryChat = async () => {
  await waitResponseCatched(fileListCatchStatus, false)
  getFileList('employee-zero-data/ZeroAiFiles')
  await waitResponseCatched(fileListCatchStatus, false)
  let localFileList = fileList.value
  console.info("begin loadChatTitle")
  await loadChatTitle(localFileList.dir)
  workflows.value = categorizeDirsByDate(localFileList);
  if (workflows.value.length <= 4) {
    // 如果 workflows 长度小于或等于4，显示全部
    displayedWorkflows.value = [...workflows.value];
  } else {
    // 如果 workflows 长度大于4，显示前四个
    displayedWorkflows.value = workflows.value.slice(0, 4);
  }
}

async function loadChatTitle(dirList: []) {
  let tempDirList: string[] = []
  dirList.forEach(item => {
    tempDirList.push(`employee-zero-data/ZeroAiFiles/${item}/codeInput/codeInput.json`)
  })
  await waitResponseCatched(fileContentCatchStatus, false)
  getFilesContent(tempDirList)
  console.info("begin getFilesContent")
  await waitResponseCatched(fileContentCatchStatus, false)
  zeroDescriptionMap.value = fileContentMap.value
}

const recoverHistoryChat = async (zeroDir: string) => {
  currentZeroDirPath.value = `employee-zero-data/ZeroAiFiles/${zeroDir}`
  await loadAllNeedFile()
  emitter.emit('recoverInputInfo')
  emitter.emit('recoverTodolistInfo')
  emitter.emit('recoverCodeInfo')
}

function clearZeroItem(zeroDir: string, zeroItemRange: {
  name: string;
  dirs: { dirName: string, description: string }[]
}) {
  zeroItemRange.dirs.forEach((item, index) => {
    if (item.dirName === zeroDir) {
      zeroItemRange.dirs.splice(index, 1);
    }
  });
}



async function loadAllNeedFile() {
  let fileList = []
  fileList.push(`${currentZeroDirPath.value}/codeInput/codeInput.json`)
  fileList.push(`${currentZeroDirPath.value}/taskList/taskList.md`)
  fileList.push(`${currentZeroDirPath.value}/codeView/codeView.json`)

  await waitResponseCatched(fileContentCatchStatus, false)
  getFilesContent(fileList)
  await waitResponseCatched(fileContentCatchStatus, false)
  // recoverFileContentMap.value = fileContentMap.value
}

// 删除功能
const deleteWorkflow = (dir: string) => {
  deleteFile(`employee-zero-data/ZeroAiFiles/${dir}`)
  clearTaskItem(dir)
};

function clearTaskItem(dir: string) {
  workflows.value = workflows.value.filter(item => item.dir !== dir);

  if (showAll.value === true) {
    displayedWorkflows.value = [...workflows.value];
  } else {
    // 根据 workflows 长度更新 displayedWorkflows
    if (workflows.value.length <= 4) {
      // 如果 workflows 长度小于等于4，显示全部数据并且按钮变为 "Show less"
      displayedWorkflows.value = [...workflows.value];
    } else {
      // 如果 workflows 长度大于4，显示前四个数据
      displayedWorkflows.value = workflows.value.slice(0, 4);
    }
  }
}

// 文字点击事件
const handleTextClick = (index: number) => {
  console.log(`Text clicked for item at index ${index}`);
  // 在这里可以添加修改字段的逻辑
};

// 用于显示的工作流数据
const displayedWorkflows = ref<Workflow[]>([]);
// 控制按钮的状态，默认显示 "Show all"
const showAll = ref(false);

// 切换显示全部或显示前四个
const toggleShowAll = () => {
  showAll.value = !showAll.value;
  if (showAll.value) {
    // 显示所有数据
    displayedWorkflows.value = [...workflows.value];
  } else {
    // 显示前四个数据
    displayedWorkflows.value = workflows.value.slice(0, 4);
  }
};
</script>

<template>
  <div class="workflow-container">
    <h2>历史记录</h2>
    <div v-if="displayedWorkflows.length === 0">
      <p class="no-data-text">暂时没有历史记录，请向我提问吧</p>
    </div>
    <div v-else>
      <div class="workflow-grid">
        <div
            v-for="(item, index) in displayedWorkflows"
            :key="index"
            class="workflow-item"
        >
          <div class="workflow-text" @click="handleTextClick(index)">
            <span>{{ item.text }}</span>
            <span class="time">{{ item.time }}</span>
          </div>
          <span class="delete-icon" @click="deleteWorkflow(item.dir)">
          <el-icon><Delete/></el-icon>
        </span>
        </div>
      </div>
      <button v-if="workflows.length > 4" class="show-more" @click="toggleShowAll">
        {{ showAll ? 'Show less' : 'Show all' }}
      </button>
    </div>
  </div>

</template>

<style scoped>
.list-item {
  display: flex;
  float: left;
  margin-bottom: 5px;
  color: var(--vscode-foreground);
}

::v-deep(.el-collapse-item__header) {
  border-top: none;
}

::v-deep(.el-collapse-item__wrap) {
  border: 0;
  background-color: var(--vscode-panel-background);
  color: var(--vscode-panel-foreground);
  border-bottom: none;
}

.workflow-container {
  width: 100%;
  max-width: 100%;
  background: var(--vscode-panel-background);
  color: var(--vscode-editor-foreground);
  padding: 10px;
  border-radius: 8px;
  font-family: Arial, sans-serif;
  overflow: hidden; /* 防止出现横向滚动条 */
  box-sizing: border-box; /* 包含内边距在内 */
//max-height: 80vh; /* 限制容器的最大高度为视口高度的 80% */
}

h2 {
  margin-bottom: 16px;
  font-size: var(--vscode-font-size);
}

.no-data-text {
  margin-bottom: 16px;
  font-size: var(--vscode-font-size);
}

.workflow-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); /* 自动填充网格项，最小宽度200px */
  gap: 10px;
//height: 100%; /* 保证网格项占满容器 */
}

.workflow-item {
  border-radius: 12px;
  padding: 8px 16px; /* 减小上下内边距，左右保持不变 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  background-color: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editor-foreground);
  font-size: calc(var(--vscode-font-size) - 1px);
  box-sizing: border-box;
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.workflow-item:hover {
  background-color: var(--vscode-editorHoverWidget-border);
}

.workflow-item:hover .delete-icon {
  opacity: 1;
}

.workflow-item:hover .time {
  opacity: 0;
}

.workflow-text {
  display: flex; /* 使用 flexbox 来排列文字和时间 */
  justify-content: space-between; /* 文字和时间之间的间距 */
  align-items: center; /* 保证它们在垂直方向居中 */
  cursor: pointer; /* 提示文字是可点击的 */
}

.time {
  color: var(--vscode-editor-foreground);
  font-size: 12px;
}

.show-more {
  background: none;
  border: none;
  color: var(--vscode-commandCenter-inactiveBorder);
}

.show-more:hover {
  text-decoration: underline;
}

.delete-icon {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;
}

.workflow-item:hover .delete-icon {
  opacity: 1;
}

.workflow-item:hover .time {
  opacity: 0;
}

.delete-icon:hover {
  color: #f44336;
}

@media (max-width: 600px) {
  .workflow-grid {
    grid-template-columns: 1fr;
  }
}
</style>
