/* Started by AICoder, pid:7d631z1801qa2c7146710807c1d1912c677055d6 */
/* Started by AICoder, pid:981b2v9248vabe3140a80a5b00fa023d5416d310 */
<script setup lang="ts">
import { watch, ref, onMounted, onUnmounted } from "vue";

const showIndexStatus = ref("未构建");
const indexStatus = ref("未构建");
const codeBaseUrl = ref("localhost:56878");
const codeGenerateUrl = ref("localhost:56878");
const workSpacePath = ref("");
const creatIndexError = ref("");
const creatIndexResponse = ref("");

// 添加一个变量来存储定时器ID
let pollTimer: number | null = null;
let lastTriggerTime: number | null = null; // 添加一个变量来存储上次触发的时间

const changeCodeBaseUrl = (value: string) => {
  codeBaseUrl.value = value;
  console.log(codeBaseUrl.value);
  sendSettingChangeMsg();
};

const changeCodeGenerateUrl = (value: string) => {
  codeGenerateUrl.value = value;
  console.log(codeGenerateUrl.value);
  sendSettingChangeMsg();
};

const createIndex = async () => {
  const message = {
    type: "zero_getWorkspaceRootFolder",
    data: {},
  };
  window.parent.postMessage(message, "*");
  setTimeout(async () => {
    const body = {
      repo_path: workSpacePath.value,
      //   repo_path:  "/repo/MMS",
    };
    try {
      creatIndexError.value = null;
      creatIndexResponse.value = null;
      const res = await fetch(
        `http://${codeBaseUrl.value}/zero-employee/repo_understander/repo/index`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body),
        }
      );

      if (!res.ok) {
        throw new Error(`HTTP 错误！状态码: ${res.status}`);
      }

      // 开始轮询检查状态
      startPolling();
      showIndexStatus.value = "构建中";
    } catch (err) {
      creatIndexError.value = err.message;
    }
  }, 500);
};

// 获取index状态
const getIndex = async () => {
  const body = {
    repo_path: workSpacePath.value,
    // repo_path:  "/repo/MMS",
    interpret: false,
    language: "java",
  };
  try {
    creatIndexError.value = null;
    creatIndexResponse.value = null;
    const res = await fetch(
      `http://${codeBaseUrl.value}/zero-employee/repo_understander/repo/index/status`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      }
    );

    if (!res.ok) {
      throw new Error(`HTTP 错误！状态码: ${res.status}`);
    }

    const data = await res.json();
    indexStatus.value = data.status;

    // 如果状态为 Failed 或 Completed，停止轮询
    if (data.status === "Failed" || data.status === "Completed") {
      showIndexStatus.value =
        data.status === "Failed" ? "构建失败" : "构建成功";
      stopPolling();
    }
  } catch (err) {
    creatIndexError.value = err.message;
    // 发生错误时也停止轮询
    stopPolling();
  }
};

// 开始轮询的方法
const startPolling = () => {
  // 先执行一次
  getIndex();
  // 设置定时器，每10分钟执行一次
  pollTimer = window.setInterval(getIndex, 10 * 60 * 1000);
};

// 停止轮询的方法
const stopPolling = () => {
  if (pollTimer !== null) {
    clearInterval(pollTimer);
    pollTimer = null;
  }
};

// 监听消息，获取workspace
/* Started by AICoder, pid:08cd0y4eddbb27f14a1c09a8c0e80a1bc097e809 */
window.addEventListener("message", function (event) {
  if (
    event.data.type === "vscode_return.zero_getWorkspaceRootFolder" &&
    event.data.data.path
  ) {
    workSpacePath.value = event.data.data.path;
  }

  // 监听文件变化事件
  if (event.data.type === "vscode_return.zero_watchFileChanged") {
    const currentTime = Date.now();
    if (lastTriggerTime === null || currentTime - lastTriggerTime > 10000) {
      createIndex(); // 触发createIndex
      lastTriggerTime = currentTime;
    }
  }
});
/* Ended by AICoder, pid:08cd0y4eddbb27f14a1c09a8c0e80a1bc097e809 */

function sendSettingChangeMsg() {
  const message = {
    type: "zero_sendSettingChangeMsg",
    data: {
      codeBaseUrl: codeBaseUrl.value,
      codeGenerateUrl: codeGenerateUrl.value,
    },
  };
  window.parent.postMessage(message, "*");
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopPolling();
});
</script>

<template>
  <div class="code-base-container">
    <h3 class="text-title">CodeBase构建</h3>
    <div>
      <el-button id="codeBaseBtn" type="primary" @click="createIndex" round
        >构建</el-button
      >
      <span
        id="codeBaseBtnText"
        class="codeBaseBtn-tip"
        :class="{
          'status-blue': showIndexStatus === '未构建',
          'status-yellow': showIndexStatus === '构建中',
          'status-red': showIndexStatus === '构建失败',
          'status-green': showIndexStatus === '构建成功',
        }"
        >{{ showIndexStatus }}</span
      >
    </div>
    <div class="text-tip">
      *嵌入式代码可以优化整个代码库的回答能力，所有代码都存储在本地
    </div>
  </div>
  <br />
  <br />
  <br />
  <br />
  <br />
  <el-divider>调测配置</el-divider>
  <div class="code-base-container">
    <div class="input-container">
      <span class="text-tip" id="codeBaseUrlText"
        >代码理解服务的IP和PORT：</span
      >
      <el-input
        class="url-input"
        v-model="codeBaseUrl"
        @change="changeCodeBaseUrl"
        :input-style="{
          color: 'var(--vscode-input-foreground)',
        }"
      />
    </div>
  </div>
  <br />
  <div class="code-base-container">
    <div class="input-container">
      <span class="text-tip" id="codeBaseUrlText"
        >代码生成服务的IP和PORT：</span
      >
      <el-input
        class="url-input"
        v-model="codeGenerateUrl"
        @change="changeCodeGenerateUrl"
        :input-style="{
          color: 'var(--vscode-input-foreground)',
        }"
      />
    </div>
  </div>
</template>

<style scoped>
.code-base-container {
  display: flex;
  gap: 8px;
  flex-direction: column;
  align-content: flex-start;
  justify-content: space-evenly;
  align-items: flex-start;
  flex-wrap: nowrap;
}

.text-title {
  color: var(--vscode-input-foreground);
}

.text-tip {
  color: var(--vscode-input-foreground);
  font-size: var(--vscode-font-size);
}

#codeBaseBtn {
  width: 80px;
  font-size: var(--vscode-font-size);
}

.input-container {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  gap: 8px; /* 元素之间的间距 */
}

.url-input {
  flex: 1; /* 使输入框占据剩余空间 */
}

.codeBaseBtn-tip {
  font-size: 12px;
  padding-left: 8px;
}

.status-blue {
  color: var(--vscode-charts-blue);
}

.status-red {
  color: var(--vscode-charts-red);
}

.status-yellow {
  color: var(--vscode-charts-yellow);
}

.status-green {
  color: var(--vscode-charts-green);
}

:deep(.el-input__wrapper) {
  background-color: var(--vscode-input-background);
}

:deep(.el-input) {
  --el-input-border-color: var(--vscode-editorHoverWidget-border);
}

:deep(.el-divider__text) {
  background-color: var(--vscode-editor-background);
  color: var(--vscode-input-foreground);
}
</style>
/* Ended by AICoder, pid:981b2v9248vabe3140a80a5b00fa023d5416d310 */
/* Ended by AICoder, pid:7d631z1801qa2c7146710807c1d1912c677055d6 */
