/* Started by AICoder, pid:od681y06d9p2a2d144a2080c918da28fb792f5ee */
/* Started by AICoder, pid:58352ubd55w93c714db90a66f15f336c8b28f5e5 */
/* Started by AICoder, pid:6ba67n315ak746814fb60b90c19dd639c3f7e1f9 */
<template>
  <el-card shadow="never" :body-class="elCardBodyClass" v-if="resultMap.get(taskId)">
    <div v-for="(o, i) in resultMap.get(taskId).data" class="text">
      <div v-if="!isTestFile(o.fileName)" class="code-type">业务代码:</div>
      <div v-if="isTestFile(o.fileName)" class="code-type">单元测试代码:</div>
      <div class="item item-content">
        <el-icon v-if="!resultMap.get(taskId).data[i].fileStatus" class="is-loading"><Loading /></el-icon>
        <el-icon v-if="resultMap.get(taskId).data[i].fileStatus"><Check color="#409EFC" /></el-icon>
        <el-tooltip :content="o.path" placement="top">
          <el-link type="primary" @click="openDiffEditor(i)">
            <div class="text-content">{{ o.fileName }}</div>
          </el-link>
        </el-tooltip>
        <div class="icons-right">
          <el-tooltip content="修复" placement="top">
            <img  src="../../assets/repair.svg" @click="fix(i)" style="width: 15px; height: 15px;"/>
          </el-tooltip>
          <el-tooltip content="运行" placement="top">
            <el-icon @click="run(i)"><VideoPlay/></el-icon>
          </el-tooltip>
        </div>
      </div>
    </div>
  </el-card>

  <el-card v-if="resultMap.get(taskId) && resultMap.get(taskId).runInfo.length > 1" shadow="never" :body-class="elCardBodyClass">
    <div v-for="o in resultMap.get(taskId).runInfo" :key="o" class="item" v-html="o.replace(/\n/g, '<br>')"> </div>
  </el-card>
  <el-card v-if="resultMap.get(taskId) && resultMap.get(taskId).fixInfo.length > 1" shadow="never" :body-class="elCardBodyClass">
    <div v-for="o in resultMap.get(taskId).fixInfo" :key="o" class="item" v-html="o.replace(/\n/g, '<br>')"></div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, toRefs } from 'vue';
import { Loading, Check, VideoPlay } from '@element-plus/icons-vue';
import { codeViewStore } from '../../stores/todolist/index';
import { contentStore } from "../../stores/index";
import { commonStore } from "../../stores/common/index";

const {codeGenerateUrl} = toRefs(commonStore())

const contentstore = contentStore();
const { hilContents } = toRefs(contentstore);

const { resultMap } = toRefs(codeViewStore());
const props = defineProps(['taskId']);
const taskId = String(props.taskId);
const elCardBodyClass = ref('el-card-body');

function isTestFile(item: string) {
  return item.includes('Test');
}

function openDiffEditor(i: number) {
  window.parent.postMessage({
    type: "zero_writeFullContentToFile",
    data: { filePath: resultMap.value.get(taskId).data[i].path, fileContent: resultMap.value.get(taskId).data[i].code }
  }, "*");
}

/* Started by AICoder, pid:ffab5t867e6feba146c40802b0662614e9530ba3 */
function run(i: number) {
  resultMap.value.get(taskId).runInfo = ["运行信息:"]
  humanInterveneReq(i, "run");
}

/* Started by AICoder, pid:677c0sfa0050fda1477f09fdf05bad7a1e118b07 */
function fix(i: number) {
  resultMap.value.get(taskId).fixInfo = ["修复信息:"]
  humanInterveneReq(i, "repair");
}
/* Ended by AICoder, pid:677c0sfa0050fda1477f09fdf05bad7a1e118b07 */
/* Ended by AICoder, pid:ffab5t867e6feba146c40802b0662614e9530ba3 */

async function humanInterveneReq(i:number, action:string) {
  const url = `http://${codeGenerateUrl.value}/zero-employee/human/intervene`;
  const hilJson = JSON.parse(hilContents.value);
  const body = {
    hil_id: hilJson.hil_id,
    action: action,
    ut_path: resultMap.value.get(taskId).data[i].path,
  };
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error("Response body is null");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        console.log("Stream complete");
        if (buffer) {
          const message = {
            type: "zero_showDataInOutputChannel",
            data: {
              channelName: "zero-chat",
              content: buffer,
            },
          };
          window.parent.postMessage(message, "*");
        }
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;

      const lines = buffer.split("\n");

      // 输出除最后一行的所有行
      for (let i = 0; i < lines.length - 1; i++) {
        const message = {
          type: "zero_showDataInOutputChannel",
          data: {
            channelName: "zero-chat",
            content: " " + lines[i],
          },
        };
        window.parent.postMessage(message, "*");
      }

      // 将最后一行保留在缓冲区中
      buffer = lines[lines.length - 1];
    }
  } catch (error) {
    console.error("Error fetching data:", error);
  }
}

</script>

<style>
.el-card {
  --el-card-border-color: var(--vscode-editorHoverWidget-border);
  --el-card-border-radius: 4px;
  --el-card-padding: 20px;
  --el-card-bg-color: var(--el-fill-color-blank);

  border: 1px solid var(--el-card-border-color);
  border-radius: var(--el-card-border-radius);
  background-color: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editor-foreground);
  overflow: hidden;
  transition: var(--el-transition-duration);
}

.text {
  font-size: var(--vscode-font-size);
}

.item {
  font-size: var(--vscode-font-size);
  margin-bottom: 1px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-card-body {
  font-size: var(--vscode-font-size);
  width: 100%;
  padding: 10px !important;
}

.el-icon {
  margin-left: 10px;
}

.item-content {
  background-color: var(--vscode-editor-background);
  font-weight: bold;
  display: flex;
  align-items: center;
  width: 95%;
}

.text-content {
  flex-grow: 1;
  margin-left: 10px;
}

.icons-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
}
</style>
/* Ended by AICoder, pid:6ba67n315ak746814fb60b90c19dd639c3f7e1f9 */
/* Ended by AICoder, pid:58352ubd55w93c714db90a66f15f336c8b28f5e5 */
/* Ended by AICoder, pid:od681y06d9p2a2d144a2080c918da28fb792f5ee */