/* Started by AICoder, pid:jb972l7101ldfad14ba70802f19a281007a55c51 */
<template>
  <div>
    <h1>Stream JSON Viewer</h1>
    <div>
      <button @click="startListening">开始监听</button>
      <button @click="stopListening" :disabled="!isListening">停止监听</button>
    </div>
    <div v-if="displayedJson" class="json-display">
      <h3>解析的 JSON 内容：</h3>
      <pre>{{ displayedJson }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const isListening = ref(false); // 控制监听状态
const displayedJson = ref<string | null>(null); // 用于显示的 JSON 内容
let jsonBuffer = ""; // 当前正在组装的 JSON 数据
let isJsonStart = false; // 是否处于 JSON 组装中
let abortController: AbortController | null = null; // 控制流请求的中止

/**
 * 处理流式数据行
 * @param line 当前流返回的一行数据
 */
function processLine(line: string) {
  if (line.includes("json-start")) {
    isJsonStart = true;
    jsonBuffer = ""; // 开始新的 JSON 组装
  } else if (line.includes("json-end")) {
    isJsonStart = false;
    try {
      const jsonData = JSON.parse(jsonBuffer); // 解析 JSON
      displayedJson.value = JSON.stringify(jsonData, null, 2); // 格式化展示
    } catch (e) {
      console.error("JSON 解析失败:", jsonBuffer, e);
      displayedJson.value = "解析失败，数据无效！";
    }
    jsonBuffer = ""; // 清空缓冲区
  } else if (isJsonStart) {
    jsonBuffer += line; // 累加 JSON 内容
  }
}

/**
 * 开始监听流式接口
 */
async function startListening() {
  if (isListening.value) return;

  isListening.value = true;
  abortController = new AbortController();

  try {
    const response = await fetch("http://localhost:8000/stream", {
      method: "GET",
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
      signal: abortController.signal,
    });

    if (!response.body) {
      throw new Error("Response body is null.");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");

    while (isListening.value) {
      const { value, done } = await reader.read();
      if (done) {
        console.log("Stream ended.");
        break;
      }
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split("\n"); // 假设流数据按行分割
      for (const line of lines) {
        processLine(line.trim());
      }
    }
  } catch (error) {
    console.error("Error while listening to the stream:", error);
  } finally {
    isListening.value = false;
  }
}

/**
 * 停止监听流式接口
 */
function stopListening() {
  if (abortController) {
    abortController.abort(); // 中止流请求
    abortController = null;
  }
  isListening.value = false;
}
</script>

<style scoped>
.json-display {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #f9f9f9;
  white-space: pre-wrap; /* 保持 JSON 格式化显示 */
}
button {
  margin-right: 10px;
}
</style>
/* Ended by AICoder, pid:jb972l7101ldfad14ba70802f19a281007a55c51 */