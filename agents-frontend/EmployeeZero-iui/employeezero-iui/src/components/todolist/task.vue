/* Started by AICoder, pid:e085c8fe3696a38148c70b7a30765d7cce374ee1 */
<template>
  <div class="task-body">
    <el-table :data="task.tests" border class="table-style">
      <el-table-column prop="testId" label="ID" show-overflow-tooltip align="center" width="50" fixed />
      <el-table-column prop="description" label="描述" show-overflow-tooltip align="center" />
      <el-table-column prop="given" label="given" show-overflow-tooltip align="center" />
      <el-table-column prop="when" label="when" show-overflow-tooltip align="center" />
      <el-table-column prop="then" label="then" show-overflow-tooltip align="center" />
    </el-table>

    <!-- <el-table :data="task.tests" border style=" table-layout: fixed;" class="table-style">
      <el-table-column prop="testId" label="ID"  style="width: 10px;"/>
      <el-table-column prop="description" label="描述" show-overflow-tooltip/>
      <el-table-column prop="given" label="given" show-overflow-tooltip />
      <el-table-column prop="when" label="when" show-overflow-tooltip />
      <el-table-column prop="then" label="then" show-overflow-tooltip />
    </el-table> -->
    <div class="file-path">
      <span class="file-label">文件路径: </span>
      <span class="file-path-content" style="">{{ task.path }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

interface TestCase {
  testId: number;
  description: string;
  given: string;
  when: string;
  then: string;
}

interface Task {
  taskId: number;
  description: string;
  status: string;
  priority: number;
  path: string;
  dependencies: number[];
  tests: TestCase[];
}

const props = defineProps<{
  task: Task;
}>();
</script>

<style scoped>
.task-body {
  background-color: var(--vscode-editorHoverWidget-background);
}


:deep(.el-table .el-table__cell) {
  padding-top: 0px;
  padding-bottom: 0px;
}

.el-table .el-table__row {
  height: 10px;
  /* 设置每行的固定高度 */
  line-height: 40px;
  /* 设置行内文字的行高，使其垂直居中 */
}

:deep(.table-style) {
  width: 100%;
  font-size: calc(var(--vscode-font-size) - 3px) !important;
  /* background-color: var(--vscode-editorHoverWidget-background); */
  background-color: var(--vscode-panel-background);
  --el-table-tr-bg-color: var(--vscode-panel-background);
  --el-table-header-bg-color: var(--vscode-panel-background);
  --el-table-row-hover-bg-color: var(--vscode-input-background);
  --el-table-border-color: var(--vscode-editorHoverWidget-border);
  --el-table-text-color: var(--vscode-editor-foreground);
  /* --el-table-header-text-color: var(--vscode-editor-foreground); */
}

.file-path {
  font-size: calc(var(--vscode-font-size) - 2px) !important;
  margin-top: 10px;
  display: flex;
  flex-wrap: nowrap;
  font-size: var(--vscode-font-size);
  color: var(--vscode-editor-foreground);
}

.file-label {
  white-space: nowrap;
}

.file-path-content {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-all;
  font-style: italic;
  margin-top: 2px;
  margin-left: 8px;
}
</style>
/* Ended by AICoder, pid:e085c8fe3696a38148c70b7a30765d7cce374ee1 */