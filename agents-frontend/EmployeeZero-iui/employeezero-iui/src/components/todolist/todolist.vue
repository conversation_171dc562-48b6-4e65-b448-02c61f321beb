/* Started by AICoder, pid:g25b23e63ct31f314dce090c7291c24ba0b925d8 */
/* Started by AICoder, pid:c37f5w85da2a74d1474d09df64c37c467a51712e */
<template>
  <div class="icon-text-container">
    <p>
        <span v-for="(item, index) in taskStatusStore.statusText.content" :key="index">
          <!-- 检查是否为对象类型且含有 type 属性 -->
          <span v-if="isHtmlType(item)" v-html="item.value"></span>
          <component v-else-if="isComponentType(item)" :is="item.value" class="icon"></component>
          <!-- 处理纯文本 -->
          <template v-else>{{ item }}</template>
        </span>
    </p>

    <!-- <button @click="updateStatus(StatusEnum.TASKLOADING)">Loading</button>
    <button @click="updateStatus(StatusEnum.CODEGENERATE)">CODEGENERATE</button>
    <button @click="updateStatus(StatusEnum.CODEEND)">CODEEND</button>
    <button @click="updateStatus(StatusEnum.TASKEND)">TASKEND</button>
    <button @click="updateStatus(StatusEnum.CODEREPAIRE)">CODEREPAIRE</button>
    <button @click="updateStatus(StatusEnum.JOBERROR)">JOBERROR</button> -->
  </div>
  <div class="task-container">

    <!-- 整体折叠按钮 -->
    <!-- <div class="icons-right" v-if="tasks.length > 0"> -->
    <div class="icons-right" v-if="todoListStore.tasks.length > 0">
      <el-tooltip content="启动批量任务执行" placement="top" v-if="showGenerateCode">
        <div class="icon-wrapper" v-if="showGenerateCode">
          <el-icon @click="generateCode">
            <Promotion/>
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip content="md文件" placement="top">
        <div class="icon-wrapper">
          <el-icon @click="openTaskListFile">
            <Document/>
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip content="全部收起" placement="top">
        <div class="icon-wrapper">
          <el-icon @click="collapseAll">
            <ArrowUp/>
          </el-icon>
        </div>
      </el-tooltip>
    </div>

    <!-- 单个任务卡片 -->
    <div v-for="(task, index) in todoListStore.tasks" :key="task.taskId" class="task-card">
      <!-- <div v-for="(task, index) in tasks" :key="task.taskId" class="task-card"> -->
      <div class="task-header"
           :style="{ 'border-bottom': !collapseStates[index] ? '1px solid var(--vscode-editorHoverWidget-border)' : 'none' }">
        <!-- 任务标题和状态信息 -->
        <div class="task-icon-title">
          <el-icon v-if="task.generateStatus === 'init'" :size="15">
            <List/>
          </el-icon>
          <el-icon v-if="task.generateStatus === 'begin'" color="#409EFC" :size="15" class="is-loading">
            <Loading/>
          </el-icon>
          <el-icon v-if="task.generateStatus === 'end'" :size="15">
            <VideoPause/>
          </el-icon>
          <el-icon v-if="task.generateStatus === 'confirm'" color="#409EFC" :size="15">
            <CircleCheck/>
          </el-icon>
          <span class="task-title" :title="titleData(index)">
            Task{{ task.taskId }}: {{ task.description }}
          </span>
        </div>

        <div class="confirm-icons-right">
          <el-tooltip content="下一步" placement="top">
            <el-icon :color="task.confirmStatus ? '#409EFC' : ''" @click="confirm(task)" :size="15">
              <Finished/>
            </el-icon>
          </el-tooltip>
        </div>
        <!-- 折叠按钮 -->
        <el-link :underline="false" @click="toggleCollapse(index)">
          <el-icon>
            <template v-if="collapseStates[index]">
              <ArrowRight/>
            </template>
            <template v-else>
              <ArrowDown/>
            </template>
          </el-icon>
        </el-link>
      </div>

      <div v-if="!collapseStates[index]" class="task-meta">
        <div class="task-meta-item">任务ID: {{ task.taskId }}</div>
        <!-- <div>任务状态: {{ task.status }}</div> -->
        <div class="task-meta-item">任务描述: {{ task.description }}</div>
        <div class="task-meta-item">测试用例表:</div>
      </div>

      <!-- 使用 Task 组件 -->
      <Task v-if="!collapseStates[index]" :task="task"/>
      <CodeView v-if="!collapseStates[index]" :taskId="task.taskId"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeUnmount, ref, toRefs, watch} from "vue";
import Task from './task.vue';
import {
  ArrowDown,
  ArrowRight,
  ArrowUp,
  CircleCheck,
  Document,
  Finished,
  List,
  Loading,
  Promotion,
  VideoPause
} from '@element-plus/icons-vue';
import {fileContentCatchStatus, fileContentMap, useVscodeParamStore} from '../../stores/vscodeparam/index';
import CodeView from './codeview.vue';
import {codeViewStore, StatusEnum, useTaskStatusStore, useTodoListStore} from '../../stores/todolist/index';
import {contentStore} from "../../stores/index";
import {commonStore} from "../../stores/common/index";

const taskStatusStore = useTaskStatusStore();

const updateStatus = (status: StatusEnum) => {
  taskStatusStore.setStatus(status);
};

const isHtmlType = (item: string | { type: 'html' | 'component'; value: any }): item is {
  type: 'html';
  value: string
} =>
    typeof item === 'object' && item.type === 'html';


const isComponentType = (item: string | { type: 'html' | 'component'; value: any }): item is {
  type: 'component';
  value: any
} =>
    typeof item === 'object' && item.type === 'component';


const {codeBaseUrl, codeGenerateUrl} = toRefs(commonStore())

const contentstore = contentStore();
const {
  tasksContent,
  codeContent,
  hilContents,
  taskLoading,
  runContent,
  repairContent,
  taskStateContent
} = toRefs(contentstore);

const {resultMap} = toRefs(codeViewStore());
const todoListStore = useTodoListStore();
const showGenerateCode = ref(true);

// 监听消息
window.addEventListener("message", function (event) {
  if (event.data.type === "vscode_return.zero_sendSettingChangeMsg") {
    console.log("===> from setting data:", event.data.data);
    codeBaseUrl.value = event.data.data.codeBaseUrl;
    codeGenerateUrl.value = event.data.data.codeGenerateUrl;
    // repairContent.value.push(`{"taskId": 1, "desc":"运行中，请等待。。。"}`)
    // hilContents.value = `{"hil_id": "12345", "business":"task_list"}`
    // if(codeGenerateUrl.value === "10.57.190.79:1"){
    //   taskStateContent.value = `{ "business":"generate_code","taskId":1,"status":"begin"}`
    // }else if(codeGenerateUrl.value === "10.57.190.79:2"){
    //   taskStateContent.value = `{ "business":"generate_code","taskId":1,"status":"end"}`
    // }
  }
});

/* Started by AICoder, pid:b73f234f9e57c1514ad90ada811471202211515f */
function generateCode() {
  resultMap.value.clear();
  showGenerateCode.value = false;
  updateStatus(StatusEnum.CODEGENERATE);
  humanInterveneReq("continue");
}

async function humanInterveneReq(action: string) {
  const url = `http://${codeGenerateUrl.value}/zero-employee/human/intervene`;
  const hilJson = JSON.parse(hilContents.value);
  const body = {
    hil_id: hilJson.hil_id,
    action: action,
    tasks: []
  };
  if (action === "continue") {
    body.tasks = todoListStore.tasks;
  }
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error("Response body is null");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";

    while (true) {
      const {done, value} = await reader.read();

      if (done) {
        console.log("Stream complete");
        if (buffer) {
          const message = {
            type: "zero_showDataInOutputChannel",
            data: {
              channelName: "zero-chat",
              content: buffer,
            },
          };
          window.parent.postMessage(message, "*");
        }
        break;
      }

      const chunk = decoder.decode(value, {stream: true});
      buffer += chunk;

      const lines = buffer.split("\n");

      // 输出除最后一行的所有行
      for (let i = 0; i < lines.length - 1; i++) {
        const message = {
          type: "zero_showDataInOutputChannel",
          data: {
            channelName: "zero-chat",
            content: " " + lines[i],
          },
        };
        window.parent.postMessage(message, "*");
      }

      // 将最后一行保留在缓冲区中
      buffer = lines[lines.length - 1];
    }
  } catch (error) {
    console.error("Error fetching data:", error);
  }
}

/* Started by AICoder, pid:cfac1s2803q4cb3140d7082650578432a7b56692 */
function confirm(task: Task) {
  if (task.confirmStatus) {
    console.log('already confirmed:', task.confirmStatus);
    return;
  }
  task.generateStatus = 'confirm';
  task.confirmStatus = true;
  humanInterveneReq("next_task");
}

/* Started by AICoder, pid:98b8688d2878a1d1462a0a1770095a445e06fa81 */
watch(taskStateContent, async (newData) => {
  if (newData) {
    console.log('task state content change:', newData);
    try {
      const taskStateJson = JSON.parse(newData);
      todoListStore.tasks.forEach(task => {
        if (task.taskId === taskStateJson.taskId && taskStateJson.business === "generate_code") {
          task.generateStatus = taskStateJson.status;
        }
      });

      if (taskStateJson.business === "split_task" && taskStateJson.status === "begin") {
        updateStatus(StatusEnum.TASKLOADING);
      }

      if (taskStateJson.business === "split_task" && taskStateJson.status === "end") {
        updateStatus(StatusEnum.TASKEND);
      }

      if (taskStateJson.business === "generate_code" && taskStateJson.status === "begin") {
        updateStatus(StatusEnum.CODEGENERATE);
      }

      if (taskStateJson.business === "generate_code" && taskStateJson.status === "end") {
        updateStatus(StatusEnum.CODEEND);
        const taskId = String(taskStateJson.taskId);
        const taskData = resultMap.value.get(taskId)?.data;
        if (taskData) {
          for (let i = 0; i < taskData.length; i++) {
            taskData[i].fileStatus = true;
          }
        }
      }

      if (taskStateJson.business === "run" && taskStateJson.status === "begin") {
        updateStatus(StatusEnum.CODERUNNING);
      }

      if (taskStateJson.business === "run" && taskStateJson.status === "end") {
        updateStatus(StatusEnum.CODEEND);
      }

      if (taskStateJson.business === "repair" && taskStateJson.status === "begin") {
        updateStatus(StatusEnum.CODEREPAIRE);
      }

      if (taskStateJson.business === "repair" && taskStateJson.status === "end") {
        updateStatus(StatusEnum.CODEEND);
      }
    } catch (error) {
      console.error('task state JSON 解析错误:', newData, error);
    }
  }
});
/* Ended by AICoder, pid:98b8688d2878a1d1462a0a1770095a445e06fa81 */
/* Ended by AICoder, pid:cfac1s2803q4cb3140d7082650578432a7b56692 */

watch(codeContent, async (newData) => {
  await processCodeContent(newData);
}, {deep: true});

async function processCodeContent(newData) {
  if (newData) {
    if (newData.length === 0) {
      resultMap.value.clear();
      console.log("----clear code content");
      return
    }
    try {
      newData.forEach(item => {
        const codeJson = JSON.parse(item); // 反序列化字符串为对象
        const taskId = String(codeJson.task_id);
        const path = codeJson.path;
        const fileName = path.substring(path.lastIndexOf('/') + 1);
        const codeData = {
          isOpenDiffEditor: false,
          confirmStatus: false,
          fileName: fileName,
          path: path,
          code: codeJson.code
        };
        // 检查 resultMap 中是否已经存在该 taskId
        if (resultMap.value.has(taskId)) {
          // 如果存在，将新的数据条目添加到现有的 data 数组中
          const existingResult = resultMap.value.get(taskId);

          // 检查 existingResult 中是否已经存在相同的 path
          const existingIndex = existingResult.data.findIndex(data => data.path === path);

          if (existingIndex !== -1) {
            // 如果存在相同的 path，覆盖上一次的 codeData
            existingResult.data[existingIndex] = codeData;
            console.log("----update existing path");
          } else {
            // 如果不存在相同的 path，将新的数据条目添加到现有的 data 数组中
            existingResult.data.push(codeData);
            console.log("----update new path");
          }
          todoListStore.tasks.forEach(task => {
            task.generateStatus = 'success';
          });
          console.log(resultMap.value);
        } else {
          // 如果不存在，创建一个新的 result 对象并存储到 resultMap 中
          const result = {
            data: [codeData],
            runInfo: ["运行信息:"],
            fixInfo: ["修复信息:"],
            fileStatus: false,
          };
          resultMap.value.set(taskId, result);
          console.log("----insert");
          console.log(resultMap.value);
        }
      });
      console.error('解析后的code数据:', resultMap.value);
    } catch (error) {
      console.error('code JSON 解析错误:', newData, error);
    }
  }
}

/* Ended by AICoder, pid:b73f234f9e57c1514ad90ada811471202211515f */

/* Started by AICoder, pid:n0aa64bc3bde70414b0b0bcd90c7a8392179a9a2 */
/* Started by AICoder, pid:5bf7d72375232d914b9809f3c0bfec3ad6c3fb6a */
watch(runContent, (newData) => {
  if (newData && newData.length > 0) {
    try {
      const codeJson = JSON.parse(newData[newData.length - 1]); // 反序列化字符串为对象
      const taskId = String(codeJson.taskId);
      const runDesc = codeJson.desc;

      // 直接添加 runDesc 到对应的数组中
      resultMap.value.get(taskId).runInfo.push(runDesc);

      console.log('更新runDesc后的 resultMap.value:', resultMap.value);
    } catch (error) {
      console.error('code JSON 解析错误:', newData, error);
    }
  }
}, {deep: true});

watch(repairContent, (newData) => {
  if (newData && newData.length > 0) {
    try {
      const codeJson = JSON.parse(newData[newData.length - 1]); // 反序列化字符串为对象
      const taskId = String(codeJson.taskId);
      const fixDesc = codeJson.desc;

      // 直接添加 fixDesc 到对应的数组中
      resultMap.value.get(taskId).fixInfo.push(fixDesc);

      console.log('更新fixDesc后的 resultMap.value:', resultMap.value);
    } catch (error) {
      console.error('code JSON 解析错误:', newData, error);
    }
  }
}, {deep: true});
/* Ended by AICoder, pid:5bf7d72375232d914b9809f3c0bfec3ad6c3fb6a */

/* Started by AICoder, pid:u1fda6d3e46213414c8c0b4bf0cad52d3d190f4e */
watch(hilContents, (newData) => {
  if (newData) {
    console.log('hil content change:', newData);
    try {
      const hilJson = JSON.parse(newData);
      if (hilJson.business === "code_generate") {
        for (const [, taskInfo] of resultMap.value.entries()) {
          for (let i = 0; i < taskInfo.data.length; i++) {
            taskInfo.data[i].fileStatus = true;
          }
        }
        console.log('code generate finish:', resultMap.value);
      } else if (hilJson.business === "run_complete") {
        for (const [, taskInfo] of resultMap.value.entries()) {
          for (let i = 0; i < taskInfo.data.length; i++) {
            if (!taskInfo.data[i].isOpenDiffEditor) {
              window.parent.postMessage({
                type: "zero_writeFullContentToFile",
                data: {filePath: taskInfo.data[i].path, fileContent: taskInfo.data[i].code}
              }, "*");
              taskInfo.data[i].isOpenDiffEditor = true;
              taskInfo.data[i].fileStatus = true;
            }
          }
        }
        console.log('code run repair finish:', resultMap.value);
      }
    } catch (error) {
      console.error('hil JSON 解析错误:', newData, error);
    }
  }
});
/* Ended by AICoder, pid:u1fda6d3e46213414c8c0b4bf0cad52d3d190f4e */
/* Ended by AICoder, pid:n0aa64bc3bde70414b0b0bcd90c7a8392179a9a2 */

interface TestCase {
  testId: number;
  description: string;
  given: string;
  when: string;
  then: string;
}

interface Task {
  confirmStatus: boolean;
  generateStatus: string;
  taskId: number;
  description: string;
  status: string;
  priority: number;
  path: string;
  dependencies: number[];
  tests: TestCase[];
}

/* Started by AICoder, pid:1cb6501951q838d14300087d9087ea81edb45244 */

// 测试任务数据
const tasks = ref<Task[]>([
  {
    confirmStatus: false,
    generateStatus: 'init',
    taskId: 1,
    description:
        "实现指标模型实体类，包含属性：指标名、PromQL表达式、类别、分组、中文名称、英文名称、中文单位、英文单位、PromQL标签",
    status: "待处理",
    priority: 1,
    path: "com/zte/daip/manager/mms/dashboard/server/domain/model/MetricModel.java",
    dependencies: [],
    tests: [
      {
        testId: 1,
        description: "验证指标模型实体类的构造函数",
        given: "没有前提条件",
        when: "使用有效的参数创建一个新的指标模型实例",
        then: "指标模型实例的所有属性值与构造时提供的参数一致",
      },
    ],
  },
  {
    confirmStatus: false,
    generateStatus: 'init',
    taskId: 2,
    description: "实现指标模型服务类，包含新增指标模型方法",
    status: "待处理",
    priority: 2,
    path: "com/zte/daip/manager/mms/dashboard/server/service/MetricModelService.java",
    dependencies: [1],
    tests: [
      {
        testId: 2,
        description: "验证新增指标模型方法在正常情况下的行为",
        given: "一个指标模型服务实例和一个包含所有必要信息的指标模型对象",
        when: "调���新增指标模型方法并传入指标模型对象",
        then: "新增指标模型方法成功添加指标模型，并返回true",
      },
      {
        testId: 3,
        description: "验证新增指标模型方法在缺失指标名时的行为",
        given: "一个指标模型服务实例和一个没有指标名的指标模型对象",
        when: "调用新增指标模型方法并传入缺少指标名的指标模型对象",
        then: "新增指标模型方法失败，并返回false",
      },
      {
        testId: 4,
        description: "验证新增指标模型方法在缺失PromQL表达式时的行为",
        given: "一个指标模型服务实例和一个没有PromQL表达式的指标模型对象",
        when: "调用新增指标模型方法并传入缺少PromQL表达式的指标模型对象",
        then: "新增指标模型方法失败，并返回false",
      },
      {
        testId: 5,
        description: "验证新增指标模型方法在无效PromQL表达式格式时的行为",
        given: "一个指标模型服务实例和一个PromQL表达式格式不正确的指标模型对象",
        when: "调用新增指标模型方法并传入PromQL表达式格式不正确的指标模型对象",
        then: "新增指标模型方法失败，并返回false",
      },
    ],
  },
  {
    confirmStatus: false,
    generateStatus: 'init',
    taskId: 3,
    description: "实现指标模型控制器类，包含新增指标模型接口",
    status: "待处理",
    priority: 3,
    path: "com/zte/daip/manager/mms/dashboard/server/controller/MetricModelController.java",
    dependencies: [2],
    tests: [
      {
        testId: 6,
        description: "验证新增指标模型接口在正常情况下的行为",
        given: "一个指标模型控制器实例和一个包含所有必要信息的指标模型对象",
        when: "调用新增指标模型接口并传入指标模型对象",
        then: "新增指标模型接口成功添加指标模型，并返回true",
      },
      {
        testId: 7,
        description: "验证新增指标模型接口在缺失指标名时的行为",
        given: "一个指标模型控制器实例和一个没有指标名的指标模型对象",
        when: "调用新增指标模型接口并传入缺少指标名的指标模型对象",
        then: "新增指标模型接口失败，并返回false",
      },
      {
        testId: 8,
        description: "验证新增指标模型接口在缺失PromQL表达式时的行为",
        given: "一个指标模型控制器实例和一个没有PromQL表达式的指标模型对象",
        when: "调用新增指标模型接口并传入缺少PromQL表达式的指标模型对象",
        then: "新增指标模型接口失败，并返回false",
      },
      {
        testId: 9,
        description: "验证新增指标模型接口在无效PromQL表达式格式时的行为",
        given: "一个指标模型控制器实例和一个PromQL表达式格式不正确的指标模型对象",
        when: "调用新增指标模型接口并传入PromQL表达式格式不正确的指标模型对象",
        then: "新增指标模型接口失败，并返回false",
      },
    ],
  },
]);

// 发送查看文件变化信号
const sendGetFileListMsg = () => {
  const dataTemp: string[] = []

  dataTemp.push(getTaskListFilePath())
  window.parent.postMessage({type: "tdd_readFilesContent", data: dataTemp}, "*");
}

// 监听文件变化，如果文件是tasklist，则发送查看文件信号
window.addEventListener('message', function (event) {

});

// 查看文件内容，此处获取
window.addEventListener('message', function (event) {
  if (event.data?.type === 'vscode_return.zero_watchFileChanged') {
    if (event.data.data.path.endsWith(getTaskListFilePath())) {
      sendGetFileListMsg()
    }
  } else if (event.data?.type === 'vscode_tdd_returnFileContent.tdd_readFilesContent') {
    console.log('Message received read files content:', event.data);

    // 清空现有文件内容
    fileContentMap.value.clear();
    fileContentCatchStatus.value = false;

    // 遍历文件并处理
    event.data.data.forEach((fileInfo: { filePath: string; fileContent: string }) => {
      processFileContent(fileInfo);
    });
  }
  // if (event.data?.type === 'vscode_tdd_returnFileContent.tdd_readFilesContent') {
  //   console.log('Message received read files content:', event.data);
  //   fileContentMap.value.clear();
  //   event.data.data.forEach((fileInfo: { filePath: string; fileContent: string; }) => {
  //     fileContentMap.value.set(fileInfo.filePath, fileInfo.fileContent);
  //   });
  //   fileContentCatchStatus.value = false
  // }
});

function processFileContent(fileInfo: { filePath: string; fileContent: string }) {
  // 将文件内容存储到 fileContentMap 中
  fileContentMap.value.set(fileInfo.filePath, fileInfo.fileContent);

  // 如果是 "taskList.md" 文件，解析其内容
  const fileName = fileInfo.filePath.split('/').pop() as string;
  if (fileName === "taskList.md") {
    try {
      const parsedData = JSON.parse(fileInfo.fileContent); // 反序列化字符串为对象
      if (Array.isArray(parsedData)) {
        const tasksArray: Task[] = parsedData.map((task: any): Task => ({
          confirmStatus: false,
          generateStatus: 'init',
          taskId: task.taskId,
          description: task.description,
          status: task.status,
          priority: task.priority,
          path: task.path,
          dependencies: task.dependencies,
          tests: task.tests.map((test: any): TestCase => ({
            testId: test.testId,
            description: test.description,
            given: test.given,
            when: test.when,
            then: test.then,
          })),
        }));
        todoListStore.setTasks(tasksArray); // 更新任务列表
      }
    } catch (error) {
      console.error('JSON 解析错误:', error);
      // ElMessage.error('文件格式错误，请修改格式后重试')
    } finally {
      fileContentCatchStatus.value = false;
    }
  }
}


watch(tasksContent, (newData) => {
  if (newData) {
    try {
      showGenerateCode.value = true;
      const parsedData = JSON.parse(newData); // 反序列化字符串为对象
      if (Array.isArray(parsedData)) {
        createTaskListFile(JSON.stringify(parsedData, null, 4)) // 将数据写入到md文件中
        const tasksArray: Task[] = parsedData.map((task: any): Task => ({
          confirmStatus: false,
          generateStatus: 'init',
          taskId: task.taskId,
          description: task.description,
          status: task.status,
          priority: task.priority,
          path: task.path,
          dependencies: task.dependencies,
          tests: task.tests.map((test: any): TestCase => ({
            testId: test.testId,
            description: test.description,
            given: test.given,
            when: test.when,
            then: test.then,
          })),
        }));
        todoListStore.setTasks(tasksArray)
        collapseStates.value = todoListStore.tasks.map(() => true); // 任务列表变化时，默认折叠
        // tasks.value = tasksArray; // 更新 tasks 的值
        console.log('解析后的任务数据:', todoListStore.tasks);
      }
    } catch (error) {
      console.error('JSON 解析错误:', error);
    }
  } else {
    // 如果 newData 为空，清空任务列表
    todoListStore.setTasks([]); // 清空任务列表
    collapseStates.value = []
  }
});
/* Ended by AICoder, pid:1cb6501951q838d14300087d9087ea81edb45244 */

/* Started by AICoder, pid:k6d63ye5cexce6d14e550a4fa0876b28a5623e3b */
const vscodeParamStore = useVscodeParamStore();
const {createFile, openFile} = vscodeParamStore;
const {currentTaskDirPath} = toRefs(vscodeParamStore);

function getTaskListFilePath(): string {
  return `${currentTaskDirPath.value}/taskList/taskList.md`;
}

const createTaskListFile = (taskListContent: string) => {
  // currentTaskDirPath.value = getCurrentFormattedTime();
  createFile(getTaskListFilePath(), taskListContent);
};

const openTaskListFile = () => {
  openFile(getTaskListFilePath());
};

function getCurrentFormattedTime(): string {
  const now = new Date();
  const pad = (num: number) => String(num).padStart(2, '0');
  return `/employee-zero-data/ZeroAiFiles/ZeroAiTemp${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
}
/* Ended by AICoder, pid:k6d63ye5cexce6d14e550a4fa0876b28a5623e3b */

// 折叠状态
const collapseStates = ref<boolean[]>(todoListStore.tasks.map(() => true));

// 全局折叠状态
const allCollapsed = ref(false);

// 切换单个任务的折叠状态
const toggleCollapse = (index: number) => {
  collapseStates.value[index] = !collapseStates.value[index];
};

// 计算title内容
const titleData = (index: number) => {
  return `Task${todoListStore.tasks[index].taskId}: ${todoListStore.tasks[index].description}`;
  // return `Task`+index;
};

// 切换所有任务的折叠状态
const collapseAll = () => {
  collapseStates.value = collapseStates.value.map(() => true); // 将所有任务折叠
  allCollapsed.value = true; // 更新全局折叠状态
};

// // 定义响应式变量
// const dots = ref(''); // 动态点的内容
// let timer: number | null = null; // 保存计时器ID

// // 开始加载动画
// const startLoadingAnimation = () => {
//   let count = 0;
//   timer = window.setInterval(() => {
//     count = (count + 1) % 4; // 循环从 0 到 3
//     dots.value = ' .'.repeat(count); // 根据 count 设置点的数量
//   }, 500); // 每隔 500ms 更新一次
// };

// // 停止加载动画
// const stopLoadingAnimation = () => {
//   if (timer !== null) {
//     clearInterval(timer); // 停止计时器
//     timer = null;
//   }
//   dots.value = ''; // 清空点的内容
// };

// // 监听加载状态变化
// watch(taskLoading, (newVal) => {
//   if (newVal) {
//     startLoadingAnimation();
//   } else {
//     stopLoadingAnimation();
//   }
// });

// // 组件销毁时清理计时器
// onBeforeUnmount(() => {
//   stopLoadingAnimation();
// });
/* Started by AICoder, pid:2301cdb632vab001482c0811c07b092f4cf8852e */
// 定义响应式变量
const dots = ref(''); // 动态点的内容
let timer: number | null = null; // 保存计时器ID

// 开始加载动画
const startLoadingAnimation = () => {
  let count = 0;
  timer = window.setInterval(() => {
    dots.value = ' .'.repeat((count = (count + 1) % 4)); // 根据 count 设置点的数量，循环从 0 到 3
  }, 500); // 每隔 500ms 更新一次
};

// 停止加载动画
const stopLoadingAnimation = () => {
  if (timer) {
    clearInterval(timer); // 停止计时器
    timer = null;
  }
  dots.value = ''; // 清空点的内容
};

// 监听加载状态变化
watch(taskLoading, (newVal) => {
  newVal ? startLoadingAnimation() : stopLoadingAnimation();
});

// 组件销毁时清理计时器
onBeforeUnmount(stopLoadingAnimation);
/* Ended by AICoder, pid:2301cdb632vab001482c0811c07b092f4cf8852e */
</script>

<style scoped>
.task-container {
  margin-top: 2px;
  /* background-color: var(--vscode-editorHoverWidget-background); */
  background-color: var(--vscode-panel-background);
  color: var(--vscode-editor-foreground);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.global-collapse {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.icons-right {
  display: flex;
  /* align-items: center; */
  justify-content: flex-end;
  margin-left: auto;
  /* margin-right: 5px; */
  margin-top: 15px;
}

.task-card {
  border: 0.1px solid #ddd;
  border-color: var(--vscode-editorHoverWidget-border);
  border-radius: 1px;
  padding: 6px 10px 4px 10px;
  background-color: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editor-foreground);
}

.task-header {
  font-size: calc(var(--vscode-font-size) - 2px);
  padding-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* background-color: var(--vscode-panel-background); */
  border-color: var(--vscode-editorHoverWidget-border);
}

.task-meta {
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--vscode-editor-foreground);
}

.task-meta-item {
  font-size: calc(var(--vscode-font-size) - 2px) !important;
  margin: 8px 0 8px 0;
}

.icon-wrapper {
  display: flex;
  display: inline-block;
  align-items: center;
  justify-content: center;
  border: 1px solid;
  border-color: var(--vscode-editorHoverWidget-border);
  padding: 5px;
  margin-left: 8px;
  margin-right: 2px;
  cursor: pointer;
}

.el-icon {
  margin: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-title {
  display: inline-block;
  /* 使其成为块级元素 */
  max-width: 200px;
  /* 设置最大宽度为 200px（20个字符的宽度） */
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  padding-left: 10px;
}

.task-icon-title {
  display: flex;
  align-items: center;
  /* 垂直居中对齐 */
}

.loading {
  margin: 10px;
  font-size: calc(var(--vscode-font-size) - 2px);
  /* font-weight: bold; */
  color: #858585;
}

.confirm-icons-right {
  justify-content: flex-end;
  margin-left: auto;
  margin-right: 10px;
}

.icon-text-container {
  background-color: var(--vscode-panel-background);
  color: var(--vscode-editor-foreground);
  font-size: 12px;
  line-height: 1;
  margin: 0 2px -20px 2px;
  /* display: inline-flex; */
  align-items: center;
}

.icon-text-container .icon {
  font-size: 12px;
  width: 12px;
  height: 12px;
  margin: 0 2px;
  display: inline-block;
}
</style>
/* Ended by AICoder, pid:c37f5w85da2a74d1474d09df64c37c467a51712e */
/* Ended by AICoder, pid:g25b23e63ct31f314dce090c7291c24ba0b925d8 */
