<template>
  <div class="mention-input">
    <div
      contenteditable="true"
      class="input-box"
      ref="inputRef"
      @input="handleInput"
      @keydown="handleKeydown"
      @click="closeSelect"
    >
      <span v-for="(mention, index) in mentions" :key="index" class="mention">
        {{ mention }}
      </span>
    </div>
    <div v-if="showSelect" class="select-box">
      <div
        v-for="(option, index) in options"
        :key="index"
        class="option"
        @click="selectOption(option)"
      >
        {{ option }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const inputRef = ref(null);
const options = ref(["Alice", "<PERSON>", "<PERSON>"]); // 下拉框选项
const mentions = ref([]); // 用于存储插入的内容
const showSelect = ref(false); // 控制下拉框显示与隐藏
const caretPosition = ref(0); // 光标位置

// 监听输入
const handleInput = (event) => {
  const input = event.target;
  const text = input.innerText;

  // 检测是否输入了 "@"
  if (text.endsWith("@")) {
    caretPosition.value = getCaretPosition(input);
    showSelect.value = true;
  }
};

// 监听键盘事件
const handleKeydown = (event) => {
  if (showSelect.value && event.key === "Escape") {
    // 按下 Escape 键关闭下拉框
    showSelect.value = false;
  }
};

// 选择选项
const selectOption = (option) => {
  mentions.value.push(option); // 将选项内容加入数组

  // 更新输入框的内容
  const input = inputRef.value;
  const text = input.innerText;
  const updatedText = text.slice(0, caretPosition.value - 1); // 去掉 @
  input.innerText = updatedText;

  const mentionSpan = document.createElement("span");
  mentionSpan.className = "mention";
  mentionSpan.innerText = option;
  input.appendChild(mentionSpan);

  const newTextNode = document.createTextNode(" ");
  input.appendChild(newTextNode); // 添加空格，方便后续输入

  // 重新聚焦到输入框
  placeCaretAtEnd(input);

  showSelect.value = false;
};

// 关闭下拉框
const closeSelect = () => {
  if (showSelect.value) showSelect.value = false;
};

// 获取光标位置
const getCaretPosition = (element) => {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    return range.startOffset;
  }
  return 0;
};

// 将光标定位到末尾
const placeCaretAtEnd = (element) => {
  const range = document.createRange();
  const selection = window.getSelection();
  range.selectNodeContents(element);
  range.collapse(false);
  selection.removeAllRanges();
  selection.addRange(range);
  element.focus();
};
</script>

<style>
.mention-input {
  position: relative;
  width: 400px;
}

.input-box {
  border: 1px solid #ccc;
  padding: 8px;
  min-height: 40px;
  border-radius: 4px;
  outline: none;
  cursor: text;
}

.mention {
  background-color: #d9f7be;
  color: #389e0d;
  padding: 2px 4px;
  border-radius: 4px;
  margin: 0 2px;
}

.select-box {
  position: absolute;
  top: 45px;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  z-index: 10;
}

.option {
  padding: 8px;
  cursor: pointer;
}

.option:hover {
  background-color: #f5f5f5;
}
</style>
