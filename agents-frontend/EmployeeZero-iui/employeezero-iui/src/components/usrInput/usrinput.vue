/* Started by AICoder, pid:4adeehb8a0ib6bf14c400bb4d1417a9b3379e8a2 */
<style scoped>
/* Started by AICoder, pid:o5f0fp57fa301b1145bb0ae141b1f815c4f585b1 */
.no-border >>> .el-textarea__inner {
  border: none !important;
  box-shadow: none !important;
}

.radio-group-vertical .el-radio-button {
  position: absolute;
  display: block;
  margin-bottom: 10px;
  /* 可以根据需要调整间距 */
}

.container {
  position: relative;
  /* background-color: var(--vscode-input-background); */
  border: 0.1px solid #ddd;
  border-color: var(--vscode-editorHoverWidget-border);
  border-radius: 1px;
  padding: 10px;
  background-color: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editor-foreground);
  min-height: 100px;
}

.generate {
  /* background-color: var(--vscode-input-background); */
  /* border: 0.1px solid #ddd;
  border-color: var(--vscode-editorHoverWidget-border);
  border-radius: 1px;
  padding: 10px; */
  background-color: var(--vscode-editorHoverWidget-background);
  /* color: var(--vscode-editor-foreground); */
}

.file-select {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-commandCenter-inactiveBorder);
  color: var(--vscode-editor-foreground);
  border-radius: 2px;
  max-height: 600px;
  position: absolute;
  width: 400px;
  top: 20px;
  left: 0px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

:deep(.el-radio__inner) {
  display: none !important;
}

:deep(.el-radio.el-radio--large) {
  height: 20px;
}

:deep(.el-radio) {
  margin-right: 0px;
}

:deep(.el-radio.el-radio--large .el-radio__label) {
  color: var(--vscode-input-foreground);
}

:deep(.el-select__wrapper) {
  border-radius: 2em;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
}

.custom-radio-group {
  max-height: 100px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  width: 400px;
  overflow-x: hidden;
}

.custom-radio {
  display: flex;
  /* 确保子元素对齐 */
  align-items: center;
  width: 100%;
  padding: 4px 8px;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  /* 超长打点显示 */
}

.radio-label {
  display: block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  /* 超长打点显示 */
}

.file-select-hover:hover {
  background-color: rgb(25, 65, 118);
  border-radius: 0.1rem;
}

/* Started by AICoder, pid:393f6eaa70393e91466c0b17d027050ee3e6c020 */
.file-item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  width: 100%;
}
/* Ended by AICoder, pid:393f6eaa70393e91466c0b17d027050ee3e6c020 */

.text {
  display: flex;
  flex-grow: 0;
  line-height: 20px;
  margin-left: 6px;
  margin-right: 6px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.secondary-text {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.typeahead-popover {
  border-radius: 2px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  width: auto;
}

.mentions-menu {
  width: 250px;
}

[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  font-style: italic;
  color: #cccccc63;
  font-size: var(--vscode-font-size);
}

/* Ended by AICoder, pid:o5f0fp57fa301b1145bb0ae141b1f815c4f585b1 */

/* Started by AICoder, pid:dafacd6c304a46d14b1308ac30e20f2cf742ade6 */
#usrInput {
  /* 其他样式 */
  overflow-y: auto; /* 添加竖向滚动条 */
  overflow-x: hidden;
  max-height: 80px; /* 设置最大高度 */
  font-family: "Arial", "Microsoft YaHei", sans-serif; /* 设置字体，确保中英文一致 */
  font-size: 16px; /* 设置统一的字体大小 */
}

/* Webkit 浏览器（如 Chrome 和 Safari） */
#usrInput::-webkit-scrollbar {
  width: 4px; /* 设置滚动条宽度为更小的值 */
  background: transparent; /* 滚动条轨道背景 */
}

#usrInput::-webkit-scrollbar-thumb {
  background-color: var(--vscode-input-foreground);
  border-radius: 10px; /* 圆角 */
  height: 30px; /* 设置滚动条的高度，调整度 */
}

#usrInput::-webkit-scrollbar-track {
  background: transparent; /* 滚动条轨道背景 */
}

.selected {
  background-color: rgb(25, 65, 118);
}
/* Ended by AICoder, pid:dafacd6c304a46d14b1308ac30e20f2cf742ade6 */
</style>

<template>
  <div class="container">
    <div
      id="usrInput"
      contenteditable="true"
      ref="inputRef"
      @input="handleInput"
      @keydown="handleKeyDown"
      @focus="handleFocus"
      @paste.prevent="handlePaste"
      :data-placeholder="isEmpty ? '请用@选择文件，并输入用户故事描述' : ''"
      style="
        resize: none;
        grid-area: 1 / 1 / 1 / 1;
        overflow-y: auto;
        height: 80px;
        font-family: inherit;
        font-size: inherit;
        color: var(--vscode-input-foreground);
        background-color: transparent;
        display: block;
        outline: none;
        scrollbar-width: none;
        box-sizing: border-box;
        border: none;
        overflow-wrap: break-word;
        word-break: break-word;
        padding: 0 2px;
        user-select: text;
        white-space: pre-wrap;
        min-height: 80px;
        margin-left: 2px;
        font-size: var(--vscode-font-size);
      "
    ></div>

    <div>
      <div
        id="dropDown"
        v-if="showDropdown"
        :style="{
          top: `${dropdownPosition.top}px`,
          left: '8px',
          display: 'inline-block',
        }"
        class="typeahead-popover mentions-menu"
        style="
          background-color: var(--vscode-editor-background);
          border: 1px solid var(--vscode-commandCenter-inactiveBorder);
          color: var(--vscode-editor-foreground);
          font-size: var(--vscode-font-size);
          border-radius: 0.1rem;
          max-height: 300px;
          position: absolute;
          overflow-y: auto;
          overflow-x: hidden;
          z-index: 10;
          display: inline-block;
          min-width: 300px;
        "
      >
        <ul
          style="
            padding-inline-start: 0px;
            margin: 0px;
            max-height: 300px;
            overflow-y: auto;
            overflow-x: hidden;
          "
        >
          <li
            v-for="(value, key, index) in filterFiles"
            :key="key"
            @click="selectItem(value, key)"
            @mouseenter="selectedIndex = index"
            :class="{ selected: index === selectedIndex }"
            class="file-select-hover file-item"
            tabindex="-1"
          >
            <span class="text">{{ value }}</span>
            <span
              class="secondary-text"
              style="
                color: rgb(160, 160, 160);
                display: inline-flex;
                align-items: center;
                margin-right: 6px;
              "
              >{{ key }}</span
            >
          </li>
        </ul>
      </div>
    </div>

    <div
      style="
        text-align: right;
        color: var(--vscode-input-foreground);
        background-color: var(--vscode-input-background);
        font-size: var(--vscode-font-size);
        z-index: 1;
      "
    >
      <!-- 右侧的文字和图标 -->
      <div id="bottom-content" class="generate">
        <!-- <span>Shift+Enter换行</span> -->
        <el-icon
          color="#409eff"
          @click="clickIcon"
          style="
            cursor: pointer;
            color: var(--vscode-input-foreground);
            font-size: var(--vscode-font-size);
          "
        >
          <Promotion />
        </el-icon>
        <span style="margin: 0 5px; cursor: pointer" @click="clickIcon"
          >生成</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref, reactive, toRefs } from "vue";
import { Promotion } from "@element-plus/icons-vue";
import { contentStore } from "../../stores/index";
import { watch } from "vue";
import { onMounted } from "vue";
import { commonStore } from "../../stores/common/index";
import { ElMessage } from "element-plus";
import { StatusEnum, useTaskStatusStore } from "../../stores/todolist/index";

const { codeGenerateUrl } = toRefs(commonStore());

const usrInput = ref("");
const fileRadio = ref("");
const dropdownPosition = reactive({ top: 200, left: 100 });
const showDropdown = ref(false);
const inputRef = ref(null);

const contentstore = contentStore();
const {
  tasksContent,
  codeContent,
  hilContents,
  indexStatus,
  workSpacePath,
  systemFlag,
  taskLoading,
  runContent,
  repairContent,
  taskStateContent,
} = toRefs(contentstore);

const consoleContent = ref("");
let abortController: AbortController | null = null; // 制流请求的中止
const creatIndexResponse = ref(null); // 存储请求的响应结果
const creatIndexError = ref(null); // 存储错误信息
const showSelectFile = ref("");
const filterFiles = ref({});
const fileName = ref();
const filePath = ref();
const isEmpty = ref(true);
const errorDisplayed = ref(false);
const selectedIndex = ref(0); // 新增：跟踪当前选中的li索引
const preventEnter = ref(false);

const taskStatusStore = useTaskStatusStore();

const updateStatus = (status: StatusEnum) => {
  taskStatusStore.setStatus(status);
};

const clickIcon = () => {
  // 点击图标，发起请求
  generateTodolist();
};

/* Started by AICoder, pid:4fed9e624a56c7b1442d0a25b06b6a67a7e3556d */
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !preventEnter.value) {
    if (event.shiftKey) {
      event.preventDefault();
      // 获取当前选区
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);

      // 创建并插入换行符
      const br = document.createElement("br");
      range.deleteContents();
      range.insertNode(br);

      // 将光标移动到换行符后
      range.setStartAfter(br);
      range.setEndAfter(br);
      selection.removeAllRanges();
      selection.addRange(range);

      // 确保滚动条自动下移
      const div = inputRef.value;
      div.scrollTop = div.scrollHeight; // 滚动到最底部
    } else {
      event.preventDefault();
      // 调用后台接口
      generateTodolist();
      preventEnter.value = false;
    }
  } else if (event.key === "Backspace" || event.key === "Delete") {
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    const div = inputRef.value;

    // 检查是否在空行上
    if (range.startContainer === div) {
      // 当光标直接在div中时
      const childNode = div.childNodes[range.startOffset - 1];
      if (childNode && childNode.nodeName === "BR") {
        event.preventDefault();
        childNode.remove();

        // 如果删除的是最后一个br，确保至少保留一行高度
        if (div.innerHTML === "") {
          div.innerHTML = "<br>";
        }

        // 调整div高度
        div.style.height = "auto";
        div.style.height = `${Math.min(div.scrollHeight, 200)}px`;
      }
    } else if (range.startContainer.nodeType === Node.TEXT_NODE) {
      // 当光标在文本节点中时
      const textNode = range.startContainer;
      const offset = range.startOffset;

      // 在文本开始处按删除键
      if (offset === 0) {
        const previousNode = textNode.previousSibling;
        if (previousNode && previousNode.nodeName === "BR") {
          event.preventDefault();
          previousNode.remove();

          // 合并相邻的文本节点（如果有）
          if (
            previousNode.previousSibling &&
            previousNode.previousSibling.nodeType === Node.TEXT_NODE
          ) {
            const prevText = previousNode.previousSibling;
            const currentText = textNode.textContent;
            prevText.textContent += currentText;
            textNode.remove();

            // 将光标放在合并后的正确位置
            range.setStart(prevText, prevText.length - currentText.length);
            range.setEnd(prevText, prevText.length - currentText.length);
            selection.removeAllRanges();
            selection.addRange(range);
          }

          // 调整div高度
          div.style.height = "auto";
          div.style.height = `${Math.min(div.scrollHeight, 200)}px`;
        }
      }
    }
    // 检查并更新是否为空
    isEmpty.value = div.textContent.trim() === "";
  }

  /* Started by AICoder, pid:kc123lec381744b147dd0974f094a5108077ebe3 */
  if (showDropdown.value) {
    if (event.key === "ArrowDown") {
      event.preventDefault();
      selectedIndex.value =
        (selectedIndex.value + 1) % Object.keys(filterFiles.value).length; // 向下选择
    } else if (event.key === "ArrowUp") {
      event.preventDefault();
      selectedIndex.value =
        (selectedIndex.value - 1 + Object.keys(filterFiles.value).length) %
        Object.keys(filterFiles.value).length; // 向上选择
    } else if (event.key === "Enter") {
      event.preventDefault();
      const selectedKey = Object.keys(filterFiles.value)[selectedIndex.value];
      const selectedValue = filterFiles.value[selectedKey];
      selectItem(selectedValue, selectedKey); // 确认选择
      selectedIndex.value = 0;
    }
  }
  /* Ended by AICoder, pid:kc123lec381744b147dd0974f094a5108077ebe3 */
};
/* Ended by AICoder, pid:4fed9e624a56c7b1442d0a25b06b6a67a7e3556d */

import {
  fileContentCatchStatus,
  fileListCatchStatus,
  showHistoryChat,
  toggleHistoryChat,
  toggleNewChat,
  useVscodeParamStore,
} from '@/stores/vscodeparam'
const vscodeParamStore = useVscodeParamStore()
const { currentTaskDirPath } = toRefs(vscodeParamStore)
const { createFile } = vscodeParamStore

/* Started by AICoder, pid:b6935x6fe5fd795149d8089d31333601ca90fb04 */
const generateTodolist = async () => {
  showHistoryChat.value = false
  toggleHistoryChat.value = ''
  // 如果已经有一个控制器，先中止之前的请求
  if (abortController) {
    abortController.abort();
  }

  abortController = new AbortController();
  try {
    const div = inputRef.value;
    let filePathContent: string[] = [];
    let input = "";

    // 遍历所有子节点
    div.childNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE && node.tagName === "SPAN") {
        // 收集所有 span 元素的 id
        filePathContent.push(node.id);
      } else if (node.nodeType === Node.TEXT_NODE) {
        input += node.textContent;
      }
    });

    // 清理 input 中的空白字符
    input = input.trim();
    filePathContent = filePathContent.map((path) => {
      if (typeof path === "string") {
        return path.replace(/^[/\\]/, "");
      }
      return path;
    });
    const body = {
      inputs: input || "",
      codebase: filePathContent || [], // 使用逗号连接所有文件路径
      workspace: workSpacePath.value || "",
      lang: "java",
      setting: {},
    };
    const response = await fetch(
      `http://${codeGenerateUrl.value}/zero-employee/supervisor/stream`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json; charset=utf-8",
        },
        signal: abortController.signal,
        body: JSON.stringify(body),
      }
    );

    console.info("创建文件", `${currentTaskDirPath.value}/codeInput/codeInput.json`);
    createFile(`${currentTaskDirPath.value}/codeInput/codeInput.json`, JSON.stringify(body, null, 4))

    // 检查响应状态
    if (!response.ok) {
      console.error("接口调用失败：", response.statusText);
      return;
    }
    tasksContent.value = "";
    codeContent.value = [];
    taskLoading.value = true;
    // 获取 ReadableStream
    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = ""; // 用于存储未处理完的数据

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        console.log("数据流读取完毕");
        break;
      }

      // 解码新收到的数据块
      const newText = decoder.decode(value, { stream: true });
      buffer += newText;

      // 处理完整的消息
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";
      // 处理完整的
      for (const line of lines) {
        if (line.trim()) {
          consoleContent.value = line;
          sendConsoleContent(consoleContent.value);
          processChunk(line);
        }
      }
    }

    // 处理最后剩余的数据
    if (buffer.trim()) {
      consoleContent.value = buffer;
      sendConsoleContent(consoleContent.value);
      processChunk(buffer);
    }
  } catch (error) {
    // 检查是否是因为请求被中止而产生的错误
    if (error.name === "AbortError") {
      console.log("请求被中止");
    } else {
      /* Started by AICoder, pid:l0da2566f09018514af30a7f607d3e1a55c8ffd1 */
      /* Started by AICoder, pid:b04eajed2fi95c614dde098c803dcf2a17857558 */
      if (error.message === "Failed to fetch") {
        if (!errorDisplayed.value) {
          // 检查是否已经显示过错误消息
          // 创建新的 span 元素
          const errorSpan = document.createElement("span");
          errorSpan.textContent = "服务未启动，请稍后重试！";
          errorSpan.style.color = "red";
          errorSpan.style.marginRight = "10px";
          errorSpan.style.display = "inline-block";
          errorSpan.style.float = "left";

          // 获取 bottom-content 的 div
          const bottomContentDiv = document.getElementById("bottom-content");
          bottomContentDiv.prepend(errorSpan);
          updateStatus(StatusEnum.JOBERROR);

          errorDisplayed.value = true; // 设置标志为已显示

          // 在 5 秒后移除 span 并重置标志
          setTimeout(() => {
            bottomContentDiv.removeChild(errorSpan);
            updateStatus(StatusEnum.JOBCOMPETLETE);
            errorDisplayed.value = false; // 重置标志
          }, 5000);
        }
      }
      /* Ended by AICoder, pid:b04eajed2fi95c614dde098c803dcf2a17857558 */
      /* Ended by AICoder, pid:l0da2566f09018514af30a7f607d3e1a55c8ffd1 */
    }
    taskLoading.value = false;
  } finally {
    // 清理 abortController
    abortController = null;
    taskLoading.value = false;
  }
  console.log("consoleContent.value", consoleContent.value);
};
/* Ended by AICoder, pid:b6935x6fe5fd795149d8089d31333601ca90fb04 */

/* Started by AICoder, pid:cac3648113l2df3149cb0acda0a7c1383db97d28 */
// 处理每段数据
const processChunk = (chunk: string) => {
  const extractContent = (chunk: string, searchKey: string): string | null => {
    const startIndex = chunk.indexOf(searchKey);
    if (startIndex !== -1) {
      if (searchKey === "json-start-tasklist") {
        taskLoading.value = false;
      }
      const remainingString = chunk.slice(startIndex);
      const firstLine = remainingString.split("\n")[0];
      return firstLine.replace(searchKey, "").trim();
    }
    return null;
  };

  if (extractContent(chunk, "json-start-tasklist")) {
    tasksContent.value = extractContent(chunk, "json-start-tasklist");
  }
  if (extractContent(chunk, "json-start-code")) {
    codeContent.value.push(extractContent(chunk, "json-start-code"));
  }
  if (extractContent(chunk, "json-start-hil")) {
    hilContents.value = extractContent(chunk, "json-start-hil");
    const hilJson = JSON.parse(hilContents.value);
    if (hilJson.business === "task_list") {
      setTimeout(() => {
        consoleContent.value =
          "\n请仔细核对生成的任务列表（tasklist），确保其准确无误。若无误，请点击上方的“生成代码”按钮以继续操作。";
        sendConsoleContent(consoleContent.value);
      }, 1000);
    }
  }
  if (extractContent(chunk, "json-start-run")) {
    runContent.value.push(extractContent(chunk, "json-start-run"));
  }
  if (extractContent(chunk, "json-start-repair")) {
    repairContent.value.push(extractContent(chunk, "json-start-repair"));
  }
  if (extractContent(chunk, "json-stage-change")) {
    taskStateContent.value = extractContent(chunk, "json-stage-change");
  }
};
/* Ended by AICoder, pid:cac3648113l2df3149cb0acda0a7c1383db97d28 */

const sendConsoleContent = (value) => {
  const message = {
    type: "zero_showDataInOutputChannel",
    data: {
      channelName: "zero-chat",
      content: value,
    },
  };
  window.parent.postMessage(message, "*");
};

/* Started by AICoder, pid:752c71c7a2l34f214ebb0bd9a09f7041ac004a5c */
const handleInput = (value) => {
  if (!inputRef.value) return;

  const div = inputRef.value;
  let textContent = "";

  // Iterate over child nodes to collect text content excluding span elements
  div.childNodes.forEach((node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      textContent += node.textContent.trim();
    }
  });

  if (textContent === "") {
    showDropdown.value = false;
  }

  const textBeforeAt = textContent.split("@").slice(0, -1).join("@").trim();

  // Existing logic for handling input
  const cursorPos = window.getSelection().anchorOffset;
  if (value?.data?.endsWith("@") && textBeforeAt === "") {
    selectedIndex.value = 0;
    filterFiles.value = {
      CodeBase: "CodeBase",
      "→": "File",
      "→→": "Directories",
    };
    preventEnter.value = true;
    showDropdown.value = true;
    fileRadio.value = "";
    setDropdownPosition(div, cursorPos);
    return;
  }

  // 过滤文件
  if (showSelectFile.value == "file") {
    const filterContent = inputRef.value.innerText;
    const message = {
      type: "zero_globalSearchFiles",
      data: {
        searchText: filterContent.substring(filterContent.lastIndexOf("@") + 1),
      },
    };
    sendMessage(message);
  }

  // 过滤目录
  if (showSelectFile.value == "directory") {
    const filterContent = inputRef.value.innerText;
    const message = {
      type: "zero_globalSearchDirs",
      data: {
        searchText: filterContent.substring(filterContent.lastIndexOf("@") + 1),
      },
    };
    sendMessage(message);
  }
};
/* Ended by AICoder, pid:752c71c7a2l34f214ebb0bd9a09f7041ac004a5c */

// 简单的防抖函数
function debounce(fn, delay) {
  let timer = null;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

const sendMessage = debounce((message) => {
  window.parent.postMessage(message, "*");
}, 200);

/* Started by AICoder, pid:66cdbv45b4u928c149bf0bc940ff2e6ff290e319 */
// 支持@选择多个文件
const selectItem = (value, key) => {
  if (value !== "File" && value !== "Directories") {
    const div = inputRef.value;

    // 清除所有文本节点
    const children = Array.from(div.childNodes);
    children.forEach((node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        node.textContent = "";
      }
    });

    // 创建新的 span 元素
    const span = document.createElement("span");
    span.id = key;
    span.textContent = "@" + value;
    span.style.backgroundColor = "rgb(25, 65, 118)"; // 蓝色背景
    span.style.color = "white"; // 白色文字，提高可读性
    span.style.padding = "0px 2px"; // 添加内边距
    span.style.margin = "0px 2px"; // 添加边距
    span.style.borderRadius = "5px"; // 圆角
    span.contentEditable = "false"; // 防止span被编辑

    // 插入 span 到 div 中
    div.appendChild(span);

    // 在 span 后添加一个空格
    const space = document.createTextNode("\u00A0"); // 使用不间断空格
    div.appendChild(space);

    // 设置光标位置到 span 后的空格处
    const range = document.createRange();
    const selection = window.getSelection();
    range.setStartAfter(space);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);

    // 更新其他状态
    showDropdown.value = false;
    fileName.value = value;
    filePath.value = key;

    // 确保 div 保持焦点
    div.focus();

    showSelectFile.value = "codebase";
    preventEnter.value = false;
  }

  if (value === "File") {
    showSelectFile.value = "file";
    const message = {
      type: "zero_globalSearchFiles",
      data: {
        searchText: "",
      },
    };
    window.parent.postMessage(message, "*");
  }

  /* Started by AICoder, pid:s7d07hc42f92300147260b9440c9ba196900e9f9 */
  if (value === "Directories") {
    showSelectFile.value = "directory";
    const message = {
      type: "zero_globalSearchDirs",
      data: {
        searchText: "",
      },
    };
    window.parent.postMessage(message, "*");
  }
  /* Ended by AICoder, pid:s7d07hc42f92300147260b9440c9ba196900e9f9 */
};
/* Ended by AICoder, pid:66cdbv45b4u928c149bf0bc940ff2e6ff290e319 */

// 只支持@选择一个文件
// const selectItem1 = (value, key) => {
//   if (value !== "File") {
//     const div = inputRef.value;

//     // 创建新的 span 元素
//     const span = document.createElement("span");
//     span.id = key;
//     span.textContent = "@" + value;
//     span.style.backgroundColor = "#409EFF"; // 蓝色背
//     span.style.color = "white"; // 白色文字，提高可读性
//     span.style.padding = "2px 6px"; // 添加内边距
//     span.style.borderRadius = "3px"; // 圆角
//     span.style.margin = "0 2px"; // 添加外边距
//     span.contentEditable = "false"; // 防止span被编辑

//     // 清空 div 中的所有内容
//     div.innerHTML = "";

//     // 插入 span
//     div.appendChild(span);

//     // 在 span 后添加一个空格
//     const space = document.createTextNode("\u00A0"); // 使用不间断空格
//     div.appendChild(space);

//     // 设置光标位置到 span 后的空格处
//     const range = document.createRange();
//     const selection = window.getSelection();
//     range.setStartAfter(space);
//     range.collapse(true);
//     selection.removeAllRanges();
//     selection.addRange(range);

//     // 更新其他状态
//     showDropdown.value = false;
//     fileName.value = value;
//     filePath.value = key;

//     // 确保 div 保持焦点
//     div.focus();
//   }

//   if (value === "File") {
//     showSelectFile.value = true;
//     const message = {
//       type: "zero_globalSearchFiles",
//       data: {
//         searchText: "",
//       },
//     };
//     window.parent.postMessage(message, "*");
//   }
// };

const setDropdownPosition = (textarea, cursorPosition) => {
  const { top, left } = calculateCaretPosition(textarea, cursorPosition);
  dropdownPosition.top = top + 8;
  dropdownPosition.left = left;
};

/* Started by AICoder, pid:y1181ef1067d95714d540a9eb017ea2f14a4b625 */
const calculateCaretPosition = (textarea, cursorPosition) => {
  const mirrorDiv = document.createElement("div");
  const style = window.getComputedStyle(textarea);
  for (const prop of style) {
    mirrorDiv.style[prop] = style[prop];
  }
  mirrorDiv.style.position = "absolute";
  mirrorDiv.style.visibility = "hidden";
  mirrorDiv.style.whiteSpace = "pre-wrap";
  mirrorDiv.style.wordWrap = "break-word";
  mirrorDiv.style.width = `${textarea.offsetWidth}px`;
  document.body.appendChild(mirrorDiv);
  const beforeCursor = textarea.innerText?.slice(0, cursorPosition);
  mirrorDiv.textContent = beforeCursor;
  const span = document.createElement("span");
  span.textContent = "\u200b";
  mirrorDiv.appendChild(span);
  const rect = span.getBoundingClientRect();
  document.body.removeChild(mirrorDiv);
  return {
    top: rect.top + 15,
    left: rect.left + 3,
  };
};
/* Ended by AICoder, pid:y1181ef1067d95714d540a9eb017ea2f14a4b625 */

/* Started by AICoder, pid:kd55d22c0euc5c11452708ef807c8e4b8de2d75e */
// 监听消息，获取workspace
window.addEventListener("message", function (event) {
  /* Started by AICoder, pid:y9dd0p86dfje1b1149d808dec085941afc819082 */
  /* Started by AICoder, pid:j68a1m5e0died80147e20bf8b0f81b14b06371f0 */
  if (event.data.type === "zero_generateUnitTest") {
    console.log("get generate unit test: ", event.data);
    const fileName = event.data.data.filePath.substring(
      event.data.data.filePath.lastIndexOf("/") + 1
    );
    const div = inputRef.value;
    div.innerHTML = "";
    isEmpty.value = true;
    selectItem(fileName, event.data.data.filePath);
    const txt = document.createTextNode("请生成单元测试");
    div.appendChild(txt);
    generateTodolist();
  }
  /* Ended by AICoder, pid:j68a1m5e0died80147e20bf8b0f81b14b06371f0 */
  /* Ended by AICoder, pid:y9dd0p86dfje1b1149d808dec085941afc819082 */
  if (
    event.data.type === "vscode_return.zero_getWorkspaceRootFolder" &&
    event.data.data.path
  ) {
    /* Started by AICoder, pid:582e277b4ce840714bf00a89e0e1d0017c3666a6 */
    workSpacePath.value = event.data.data.path;
    /* Ended by AICoder, pid:582e277b4ce840714bf00a89e0e1d0017c3666a6 */
  }
  /* Started by AICoder, pid:i78e92363dq8a0f1409a09f1a01bb42613f08d5e */
  if (
    event.data.type === "vscode_return.zero_globalSearchFiles" ||
    event.data.type === "vscode_return.zero_globalSearchDirs"
  ) {
    const result = {};
    const files = event.data.data.searchResult;
    if (files.length > 0) {
      files.forEach((path) => {
        // 提取文件名
        const fileName = path.split(/[\\\/]/).pop();
        // 将路径作为键，文件名作为值
        result[path.replace(workSpacePath.value, "")] = fileName;
        filterFiles.value = result;
      });
    } else {
      filterFiles.value = {
        "请重试！": "未匹配到目标",
      };
    }
  }
  /* Ended by AICoder, pid:i78e92363dq8a0f1409a09f1a01bb42613f08d5e */
  if (event.data.type === "vscode_return.zero_newChat") {
    console.log('Message received from new chat:', event.data);
    toggleNewChat.value = true
    toggleHistoryChat.value = 'chat'
    fileListCatchStatus.value = false
    fileContentCatchStatus.value = false
    // 如果存在正在进行的请求，中止它
    if (abortController) {
      abortController.abort();
      abortController = null;
    }

    // 清空任务相关内容
    tasksContent.value = "";
    codeContent.value = [];
    taskLoading.value = false; // 确保加载状态被重置
    hilContents.value = "";
    runContent.value = [];
    repairContent.value = [];
    taskStateContent.value = "";
    updateStatus(StatusEnum.JOBCOMPETLETE);

    // 清空输入框内容
    const inputDiv = inputRef.value;
    if (inputDiv) {
      inputDiv.innerHTML = "";
      isEmpty.value = true;
    }
  }
});
/* Ended by AICoder, pid:kd55d22c0euc5c11452708ef807c8e4b8de2d75e */

/* Started by AICoder, pid:p96fet234bdef3b144d80b0dd0b9b0303c21cf05 */
const createIndex = async () => {
  const repoName = getFileName(workSpacePath.value);
  const body = {
    repo_path: workSpacePath.value,
    // repo_path: "/test",
    interpret: false,
    language: "java",
  };
  try {
    creatIndexError.value = null; // 清空错误
    creatIndexResponse.value = null; // 清上次响应
    // 发送异步请求
    const res = await fetch("http://10.55.26.91:31070/repo/index", {
      method: "POST", // 请求方法
      headers: {
        "Content-Type": "application/json", // 设置内容类型
      },
      body: JSON.stringify(body), // 转换为 JSON 字符串
    });

    if (!res.ok) {
      throw new Error(`HTTP 错误！状态码: ${res.status}`);
    }

    // 解析响应数据
    const data = await res.json();
    indexStatus.value = data.status;
  } catch (err) {
    creatIndexError.value = err.message; // 捕获错误并显示
  }
};
/* Ended by AICoder, pid:p96fet234bdef3b144d80b0dd0b9b0303c21cf05 */

/* Started by AICoder, pid:14d90cfc60qc735142bd0878101fa51280451a38 */
// 方法：提取路径中最后一个斜杠后的内容
const getFileName = (path) => {
  if (!path) return "";
  const parts = path.split("/");
  return parts[parts.length - 1];
};

const handleFocus = () => {
  const message = {
    type: "zero_getWorkspaceRootFolder",
    data: {},
  };
  window.parent.postMessage(message, "*");
  console.log("发送了获取workspace消息");
};
/* Ended by AICoder, pid:14d90cfc60qc735142bd0878101fa51280451a38 */

/* Started by AICoder, pid:ze56crbd63c597f146690a9a304b2d232b1308ae */
const handlePaste = (event: ClipboardEvent) => {
  // 获取剪贴板中的文本
  const text = event.clipboardData?.getData("text/plain");
  if (text) {
    // 获取当前内容
    const div = inputRef.value;
    const currentContent = div.innerHTML;

    /* Started by AICoder, pid:r0c888ad73519cf146d10bd380af59021fe4568e */
    // 将文本插入到 div 中，使用 textContent 来保留格式
    const tempDiv = document.createElement("div");
    tempDiv.textContent = text;
    div.innerHTML = currentContent + tempDiv.textContent;
    /* Ended by AICoder, pid:r0c888ad73519cf146d10bd380af59021fe4568e */

    // 设置光标位置到内容的最后
    const range = document.createRange();
    const selection = window.getSelection();
    range.selectNodeContents(div);
    range.collapse(false);
    selection.removeAllRanges();
    selection.addRange(range);
    div.focus();

    // 滚动到最底部
    div.scrollTop = div.scrollHeight; // 确保滚动条在最下端
  }
};
/* Ended by AICoder, pid:ze56crbd63c597f146690a9a304b2d232b1308ae */

onMounted(async () => {
  // 发送获取workspacepath消息
  // const message = {
  //   type: "zero_getWorkspaceRootFolder",
  //   data: {},
  // };
  // window.parent.postMessage(message, "*");
  // console.log("发送了获取workspace消息");
});
</script>
/* Ended by AICoder, pid:4adeehb8a0ib6bf14c400bb4d1417a9b3379e8a2 */
