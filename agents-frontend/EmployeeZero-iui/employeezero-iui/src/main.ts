import { createApp } from 'vue';
import './style.css';
import App from './App.vue';
import ElementPlus from 'element-plus';
import { createPinia } from 'pinia';
import * as ElementPlusIconVue from '@element-plus/icons-vue';

const app = createApp(App);
for(const [key, component] of Object.entries(ElementPlusIconVue)) {
  app.component(key, component);
}

app.use(ElementPlus);
app.use(createPinia());
createApp(App).mount('#app');
