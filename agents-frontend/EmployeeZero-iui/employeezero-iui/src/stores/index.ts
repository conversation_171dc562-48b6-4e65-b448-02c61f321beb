import { defineStore } from 'pinia';
import { ref } from 'vue';

export function parsedDateDirList(fileList: { file: string[]; dir: string[] }):  { dir: string; date: Date }[] {

  // 存储匹配的目录和日期对象
  const dirWithDates: { dir: string; date: Date }[] = [];

  // 遍历 dir 列表，进行时间解析并存储
  for (const dir of fileList.dir) {
    const match = dir.match(/ZeroAiTemp(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/);

    if (match) {
      const [_, year, month, day, hours, minutes, seconds] = match;

      // 创建一个 Date 对象
      const dirDate = new Date(
          Number(year),
          Number(month) - 1, // JavaScript 的月份是从 0 开始的
          Number(day),
          Number(hours),
          Number(minutes),
          Number(seconds)
      );

      // 将目录和日期对象存储到数组中
      dirWithDates.push({ dir, date: dirDate });
    }
  }

  dirWithDates.sort((a, b) => b.date.getTime() - a.date.getTime());

  return dirWithDates
}

export const contentStore = defineStore('contentstore', () => {

  const taskLoading = ref(false);

  const consoleContent = ref('');

  const tasksContent = ref('');

  const codeContent = ref([]);

  const runContent = ref([]);

  const repairContent = ref([]);

  const hilContents = ref('');

  const indexStatus = ref('Start');

  const workSpacePath = ref('');

  const systemFlag = ref('linux');

  const taskStateContent = ref('');

  return { consoleContent, tasksContent, codeContent, hilContents, indexStatus,
    workSpacePath, taskLoading, runContent, repairContent, systemFlag, taskStateContent };
});