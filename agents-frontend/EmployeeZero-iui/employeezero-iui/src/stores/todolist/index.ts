/* Started by AICoder, pid:o726e702edkc13214f1c0a24c0094f038c371e66 */
import {defineStore} from 'pinia';
import {computed, onBeforeUnmount, ref, watch} from 'vue';
import {Document, Finished, Promotion, VideoPlay} from '@element-plus/icons-vue';
// import { Repair } from '../../assets/repair.svg';

export const codeViewStore = defineStore('codeViewStore', () => {
  const resultMap = ref(new Map<any, any>());
  return {resultMap};
});

/* Ended by AICoder, pid:o726e702edkc13214f1c0a24c0094f038c371e66 */

interface TestCase {
  testId: number;
  description: string;
  given: string;
  when: string;
  then: string;
}

interface Task {
  confirmStatus: boolean;
  generateStatus: string;
  taskId: number;
  description: string;
  status: string;
  priority: number;
  path: string;
  dependencies: number[];
  tests: TestCase[];
}

export const useTodoListStore = defineStore('todolist', {
  state: () => ({
    tasks: [] as Task[],
  }),
  actions: {
    setTasks(newTasks: Task[]) {
      this.tasks = newTasks;
    },
  },
});

/* Started by AICoder, pid:x73937d26cp031c14c2008f080ab9d3ecb40af5a */
export enum StatusEnum {
  TASKLOADING = 'Task loading',
  TASKEND = 'Task end',
  CODEGENERATE = 'Code generate',
  CODERUNNING = 'Code Running',
  CODEEND = 'Code end',
  CODEREPAIRE = 'Code repaire',
  JOBCOMPETLETE = 'Job compelete',
  JOBERROR = 'Job error',
}

/* Ended by AICoder, pid:x73937d26cp031c14c2008f080ab9d3ecb40af5a */

/* Started by AICoder, pid:8d8a9w60b1qc629148120919c11753020aa2a3b5 */
export const useTaskStatusStore = defineStore('taskstatus', () => {
  const currentStatus = ref<StatusEnum>(StatusEnum.JOBCOMPETLETE);

  // 动态点逻辑
  const dots = ref('');
  let timer: number | null = null;

  const startLoadingAnimation = () => {
    let count = 0;
    timer = window.setInterval(() => {
      dots.value = '.'.repeat((count = (count + 1) % 4)); // 动态生成 0~3 个点
    }, 500); // 每 500ms 更新一次
  };

  const stopLoadingAnimation = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    dots.value = ''; // 清空点
  };

  onBeforeUnmount(() => {
    stopLoadingAnimation(); // 组件销毁时清理计时器
  });

  // 根据状态生成内容
  const statusText = computed(() => {
    const statusMap: Record<StatusEnum, { content: (string | { type: 'component' | 'html'; value: any })[] }> = {
      [StatusEnum.TASKLOADING]: { content: [`任务拆分中${dots.value}`] },
      [StatusEnum.CODEGENERATE]: { content: [`代码生成中${dots.value}`] },
      [StatusEnum.CODERUNNING]: { content: [`代码运行中${dots.value}`] },
      [StatusEnum.TASKEND]: {
        content: ['若需修改任务，请点击',
          { type: 'component', value: Document }, '，确认任务后请点击',
          { type: 'component', value: Promotion }, '进行批量执行。'],
      },
      [StatusEnum.CODEEND]: {
        content: ['任务1完成，点击', { type: 'component', value: VideoPlay }, '运行代码或点击',
          { type: 'html', value: '<img src="../../src/assets/repair.svg" style="width: 12px; height: 12px; margin: 0 2px; vertical-align: middle;"/> 修复代码。<br>', },
          '随后可点击', { type: 'component', value: Finished }, '确认任务1并自动执行任务2', ]
      },
      [StatusEnum.CODEREPAIRE]: { content: [`代码修复中${dots.value}`] },
      [StatusEnum.JOBCOMPETLETE]: { content: [''] },
      [StatusEnum.JOBERROR]: { content: ['网络错误, 请重新输入'] },
    };

    return statusMap[currentStatus.value] || statusMap[StatusEnum.TASKEND];
  });

  // 根据状态控制点的显示与否
  watch(
    () => currentStatus.value,
    (newStatus) => {
      if ([StatusEnum.TASKLOADING, StatusEnum.CODEGENERATE, StatusEnum.CODERUNNING].includes(newStatus)) {
        startLoadingAnimation();
      } else {
        stopLoadingAnimation();
      }
    },
    { immediate: true }
  );

  const setStatus = (status: StatusEnum) => {
    currentStatus.value = status;
  };

  return {
    currentStatus,
    statusText,
    setStatus,
  };
});
/* Ended by AICoder, pid:8d8a9w60b1qc629148120919c11753020aa2a3b5 */