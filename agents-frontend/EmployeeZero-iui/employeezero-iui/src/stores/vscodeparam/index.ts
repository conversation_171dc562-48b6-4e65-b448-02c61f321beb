/* Started by AICoder, pid:v4a0d226589b58c147990990107d92311eb91700 */
import {defineStore} from 'pinia';
import {Ref, ref} from 'vue';

export const inputData = ref('')
export const fileListCatchStatus = ref(false)
export const fileContentCatchStatus = ref(false)
export const openFileContentCatchStatus = ref(false)
export const fileList = ref()
export const fileContentMap = ref<Map<string, string>>(new Map<string, string>())
export const toggleNewChat = ref(false)
export const toggleHistoryChat = ref('')
export const showHistoryChat = ref(false)
export const userInfo = ref('')
export const waitUserInfoCatchStatus = ref(false)

const sendMessage = (message: any) => {
  window.parent.postMessage(message, '*');
};

export async function waitResponseCatched(status: Ref<any>, expected: any) {
  return new Promise(reslove => {
    const timer = setInterval(() => {
      if (status.value === expected) {
        clearInterval(timer);
        reslove(true);
      }
    }, 100);
  })
}

export const useVscodeParamStore = defineStore('vscodeParamStore', () => {
  // 文件路径，带时间戳父路径
  const currentTaskDirPath = ref('');

  const getFileList = (dir: string) => {
    fileListCatchStatus.value = true
    fileList.value = {file: [], dir: []}
    let message = {
      type: "tdd_getFileNameList",
      data: {
        dirPath: dir
      },
      useExtensionPath: true
    }
    sendMessage(message)
  }



  const deleteFile = (path: string) => {
    sendMessage({
      type: 'tdd_deleteFile',
      data: {filePath: path},
      useExtensionPath: true
    });
  };

  const createFile = (path: string, content: string) => {
    sendMessage({
      type: 'tdd_createFile',
      data: {
        filePath: path,
        fileContent: content
      },
      useExtensionPath: true
    });
  };

  const openFile = (path: string) => {
    sendMessage({
      type: 'tdd_openFile',
      data: {
        filePath: path
      },
      useExtensionPath: true
    });
  };

  const readFileContent = (data: string[]) => {
    sendMessage({
      type: 'tdd_readFilesContent',
      data: data,
      useExtensionPath: true
    });
  };

  const getFilesContent = (files: string[]) => {
    fileContentCatchStatus.value = true
    fileContentMap.value = new Map<string, string>()
    let message = {
      type: "tdd_readFilesContent",
      data: files,
      useExtensionPath: true
    }
    sendMessage(message)
  }

  return {
    currentTaskDirPath,
    deleteFile,
    createFile,
    openFile,
    readFileContent,
    getFileList,
    getFilesContent
  };
});
/* Ended by AICoder, pid:v4a0d226589b58c147990990107d92311eb91700 */


window.addEventListener('message', function(event) {
  if (event.data.type === 'vscode_tdd_returnFileNameList') {
    console.log('Message received from get file name list:', event.data);
    fileList.value = event.data.data
    fileListCatchStatus.value = false;
    console.log('fileListCatchStatus', fileListCatchStatus);
  }else if (event.data.type === 'vscode_return.zero_historyChat') {
    console.log('Message received from history chat:', event.data);
    toggleHistoryChat.value = 'history'
  }
});
