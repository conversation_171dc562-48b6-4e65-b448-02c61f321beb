name: base
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - anaconda-anon-usage=0.4.4=py311hfc0e8ea_100
  - archspec=0.2.3=pyhd3eb1b0_0
  - boltons=23.0.0=py311h06a4308_0
  - brotli-python=1.0.9=py311h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - c-ares=1.19.1=h5eee18b_0
  - ca-certificates=2024.9.24=h06a4308_0
  - certifi=2024.8.30=py311h06a4308_0
  - cffi=1.17.1=py311h1fdaa30_0
  - conda=24.9.2=py311h06a4308_0
  - conda-content-trust=0.2.0=py311h06a4308_1
  - conda-libmamba-solver=24.9.0=pyhd3eb1b0_0
  - conda-package-handling=2.3.0=py311h06a4308_0
  - conda-package-streaming=0.10.0=py311h06a4308_0
  - distro=1.9.0=py311h06a4308_0
  - fmt=9.1.0=hdb19cb5_1
  - frozendict=2.4.2=py311h06a4308_0
  - icu=73.1=h6a678d5_0
  - jsonpatch=1.33=py311h06a4308_1
  - krb5=1.20.1=h143b758_1
  - ld_impl_linux-64=2.40=h12ee557_0
  - libarchive=3.7.4=hfab0078_0
  - libcurl=8.9.1=h251f7ec_0
  - libedit=3.1.20230828=h5eee18b_0
  - libev=4.33=h7f8727e_1
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libmamba=1.5.8=hfe524e5_3
  - libmambapy=1.5.8=py311h2dafd23_3
  - libnghttp2=1.57.0=h2d74bed_0
  - libsolv=0.7.24=he621ea3_1
  - libssh2=1.11.0=h251f7ec_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libxml2=2.13.1=hfdd30dd_2
  - lz4-c=1.9.4=h6a678d5_1
  - menuinst=2.1.2=py311h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.15=h5eee18b_0
  - pcre2=10.42=hebb0a14_1
  - pip=24.2=py311h06a4308_0
  - platformdirs=3.10.0=py311h06a4308_0
  - pybind11-abi=4=hd3eb1b0_1
  - pycosat=0.6.6=py311h5eee18b_1
  - pysocks=1.7.1=py311h06a4308_0
  - python=3.11.10=he870216_0
  - readline=8.2=h5eee18b_0
  - reproc=14.2.4=h6a678d5_2
  - reproc-cpp=14.2.4=h6a678d5_2
  - requests=2.32.3=py311h06a4308_0
  - ruamel.yaml=0.18.6=py311h5eee18b_0
  - ruamel.yaml.clib=0.2.8=py311h5eee18b_0
  - setuptools=75.1.0=py311h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - truststore=0.8.0=py311h06a4308_0
  - urllib3=2.2.3=py311h06a4308_0
  - wheel=0.44.0=py311h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - yaml-cpp=0.8.0=h6a678d5_1
  - zlib=1.2.13=h5eee18b_1
  - zstandard=0.23.0=py311h2c38b39_0
  - zstd=1.5.6=hc292b87_0
  - pip:
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.11.7
      - aiosignal==1.3.1
      - annotated-types==0.7.0
      - anyio==4.6.2.post1
      - attrs==24.2.0
      - bcrypt==4.2.1
      - beautifulsoup4==4.12.3
      - bm25s==0.2.6
      - charset-normalizer==3.4.0
      - click==8.1.7
      - coloredlogs==15.0.1
      - cryptography==43.0.3
      - dataclasses-json==0.6.7
      - deprecated==1.2.15
      - deprecation==2.1.0
      - dirtyjson==1.0.8
      - fastapi==0.115.5
      - filelock==3.16.1
      - filetype==1.2.0
      - frozenlist==1.5.0
      - fsspec==2024.12.0
      - gitdb==4.0.11
      - gitpython==3.1.43
      - greenlet==3.1.1
      - grpcio==1.68.1
      - grpcio-tools==1.68.1
      - h11==0.14.0
      - h2==4.1.0
      - hpack==4.0.0
      - httpcore==1.0.7
      - httpx==0.27.2
      - huggingface-hub==0.27.0
      - humanfriendly==10.0
      - hyperframe==6.0.1
      - idna==3.10
      - importlib-metadata==8.5.0
      - inflection==0.5.1
      - iniconfig==2.0.0
      - jieba==0.42.1
      - jinja2==3.1.5
      - jiter==0.8.0
      - joblib==1.4.2
      - jsonpointer==3.0.0
      - jsonref==1.1.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - lancedb==0.17.0
      - langchain==0.2.17
      - langchain-community==0.2.7
      - langchain-core==0.2.43
      - langchain-openai==0.1.25
      - langchain-text-splitters==0.2.4
      - langgraph==0.2.32
      - langgraph-checkpoint==2.0.6
      - langsmith==0.1.146
      - llama-cloud==0.1.7
      - llama-index==0.12.8
      - llama-index-agent-openai==0.4.1
      - llama-index-cli==0.4.0
      - llama-index-core==0.12.8
      - llama-index-embeddings-huggingface==0.4.0
      - llama-index-embeddings-openai==0.3.1
      - llama-index-indices-managed-llama-cloud==0.6.3
      - llama-index-llms-openai==0.3.12
      - llama-index-llms-vllm==0.5.0
      - llama-index-multi-modal-llms-openai==0.4.1
      - llama-index-program-openai==0.3.1
      - llama-index-question-gen-openai==0.3.0
      - llama-index-readers-file==0.4.1
      - llama-index-readers-llama-parse==0.4.0
      - llama-index-storage-chat-store-redis==0.4.0
      - llama-index-storage-docstore-redis==0.3.0
      - llama-index-storage-kvstore-redis==0.3.0
      - llama-index-vector-stores-lancedb==0.3.0
      - llama-index-vector-stores-qdrant==0.4.1
      - llama-index-vector-stores-redis==0.4.0
      - llama-parse==0.5.18
      - loguru==0.7.2
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - marshmallow==3.23.1
      - mdurl==0.1.2
      - ml-dtypes==0.4.1
      - mpmath==1.3.0
      - msgpack==1.1.0
      - multidict==6.1.0
      - mypy-extensions==1.0.0
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - nltk==3.9.1
      - numpy==1.26.4
      - nvidia-cublas-cu12==12.4.5.8
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==9.1.0.70
      - nvidia-cufft-cu12==11.2.1.3
      - nvidia-curand-cu12==10.3.5.147
      - nvidia-cusolver-cu12==11.6.1.9
      - nvidia-cusparse-cu12==12.3.1.170
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - openai==1.58.1
      - orjson==3.10.12
      - overrides==7.7.0
      - packaging==24.2
      - pandas==2.2.3
      - paramiko==3.5.0
      - pathspec==0.12.1
      - pbr==6.1.0
      - pillow==11.0.0
      - pluggy==1.5.0
      - portalocker==2.10.1
      - propcache==0.2.0
      - protobuf==5.29.2
      - pyarrow==18.1.0
      - pycparser==2.22
      - pydantic==2.9.2
      - pydantic-core==2.23.4
      - pygerrit2==2.0.15
      - pygments==2.18.0
      - pyjwt==2.10.0
      - pylance==0.20.0
      - pymysql==1.1.1
      - pynacl==1.5.0
      - pypdf==5.1.0
      - pyperclip==1.9.0
      - pysher==1.0.8
      - pytest==8.3.3
      - pytest-asyncio==0.24.0
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-graphviz==0.20.3
      - python-multipart==0.0.17
      - pytz==2024.2
      - pyyaml==6.0.2
      - qdrant-client==1.12.1
      - rank-bm25==0.2.2
      - redis==5.2.1
      - redisvl==0.3.7
      - referencing==0.35.1
      - regex==2024.11.6
      - requests-toolbelt==1.0.0
      - rich==13.9.4
      - rpds-py==0.21.0
      - safetensors==0.4.5
      - scikit-learn==1.6.0
      - scipy==1.14.1
      - semver==3.0.2
      - sentence-transformers==3.3.1
      - sentry-sdk==2.19.0
      - six==1.16.0
      - smmap==5.0.1
      - sniffio==1.3.1
      - soupsieve==2.6
      - sqlalchemy==2.0.36
      - starlette==0.41.3
      - striprtf==0.0.26
      - sympy==1.13.1
      - tabulate==0.9.0
      - tantivy==0.22.0
      - tenacity==8.5.0
      - threadpoolctl==3.5.0
      - tiktoken==0.8.0
      - tokenizers==0.21.0
      - torch==2.5.1
      - tqdm==4.67.1
      - transformers==4.47.1
      - tree-sitter==0.22.3
      - tree-sitter-c==0.21.0
      - tree-sitter-cpp==0.21.0
      - tree-sitter-java==0.21.0
      - tree-sitter-python==0.21.0
      - triton==3.1.0
      - typing-extensions==4.12.2
      - typing-inspect==0.9.0
      - tzdata==2024.2
      - unidiff==0.7.5
      - uvicorn==0.32.1
      - websocket-client==1.8.0
      - wrapt==1.17.0
      - yarl==1.18.0
      - zipp==3.21.0
prefix: /home/<USER>/miniconda3
