#!/bin/bash

# Remove the zero-agents directory if it exists
rm -rf zero-agents

# Create a temporary directory named zero-agents
mkdir -p zero-agents

# Copy src, run.bat, and run.sh into the zero-agents directory
cp -r ../../src zero-agents/
cp ../../run.bat zero-agents/
cp ../../run.sh zero-agents/
chmod 777 zero-agents/run.bat
chmod 777 zero-agents/run.sh

# Step 1: Extract node_modules package
if [ -f "node_modules.tar.gz" ]; then
    tar -xzvf node_modules.tar.gz
else
    echo "node_modules.tar.gz not found!"
    exit 1
fi

# Step 2: Run npm build
if command -v npm &> /dev/null; then
    npm run build
else
    echo "npm is not installed!"
    exit 1
fi
