<!-- Started by AICoder, pid:ef328f3c4c85f1014b710ab6a0571973d4712a78 -->
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Help</title>
        <style>
            .webFrame-container {
                 width: calc(100vw - 30px);
                 height: calc(100vh - 30px);
                 border: none;
             }
        </style>
    </head>
    <body>
        <iframe id="zeroWebFrame" class="webFrame-container" src="http://************:8090/iui/employee-zero-iui/index.html?mode=help" ></iframe>
        <script>
            const vscode = acquireVsCodeApi();
            const iframeIds = ['zeroWebFrame'];

            window.onload = () => {
                // 发送vscode样式变量数据到web端
                sendVsCodeStyleDataToAllIframe();
            }

            // vscode作为webview端与web端的消息中转
            window.addEventListener('message', e => {
                // 监听主题变化事件，重新传递样式变量给iframe
                if (e.data?.type === 'vscode_themeChange') {
                    sendVsCodeStyleDataToAllIframe();
                    console.log('===> help panel vscode post message to iframe:', e);
                    return;
                }
                // 转发从webview到web端的消息
                if (e.data?.from === 'webview') {
                    const webFrame = window.document.getElementById('zeroWebFrame');
                    if (webFrame) {
                        webFrame.contentWindow.postMessage(e.data, '*');  // 发送消息到web端
                        console.log('===> help panel vscode post message to iframe:', e);
                    }
                } else {
                    // 从web端发送消息到webview
                    vscode.postMessage(e.data, '*');
                    console.log('===> help panel vscode post message to webview:', e);
                }
            });

            // 获取唯一显示的iframe
            function getIsShowWebIframe() {
                for (let i = 0; i < iframeIds.length; i++) {
                    const id = iframeIds[i];
                    const webFrame = window.document.getElementById(id);
                    if (webFrame.style.display === 'block') {
                        return webFrame;
                    }
                }
            }

            // 向所有iframe发送vscode颜色变量
            function sendVsCodeStyleDataToAllIframe() {
                for (let i = 0; i < iframeIds.length; i++) {
                    const id = iframeIds[i];
                    const webFrame = window.document.getElementById(id);
                    if (webFrame) {
                        webFrame.contentWindow.postMessage({type: 'vscodeStyle', data: document.documentElement.getAttribute('style')}, '*');  // 发送消息到web端
                    }
                }
            }
        </script>
    </body>
</html>
<!-- Ended by AICoder, pid:ef328f3c4c85f1014b710ab6a0571973d4712a78 -->