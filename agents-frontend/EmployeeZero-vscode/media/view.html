<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAT</title>
    <style>
      #modeSelect {
        border-radius: 6px; /* 设置圆角 */
        padding: 4px; /* 设置内部字体与边框的距离 */
        border-color: var(--vscode-settings-checkboxBorder);
        background-color: var(--vscode-settings-checkboxBackground); /* 设置背景颜色 */
        color: var(--vscode-settings-textInputForeground); /* 设置字体颜色 */
        font-size: 12px; /* 设置字体大小 */
        /* appearance: none; 去掉默认样式 */
        outline: none; /* 去掉焦点轮廓 */
        overflow: hidden; /* 确保内容不超出边界 */
      }
      /* 额外的样式以确保面板的圆角 */
      #modeSelect option {
        border-radius: 6px; /* 设置选项的圆角 */
      }
      .webFrame-container {
        width: calc(100vw - 30px);
        height: calc(100vh - 60px);
        border: none;
        margin-top: 36px;
      }
      .customBtn {
        display: block;
        transform: translateY(2px);
        cursor: pointer;
        margin-right: 8px;
      }
    </style>
  </head>
  <body>
    <div style="position: absolute; top: 4px; right: 12px;">
      <div style="display: flex; align-items: center;">
        <!-- 1 new chat btn -->
        <!-- Started by AICoder, pid:34f37e7901f640b14f3a09891086441dc5b245f5 -->
        <span id="newChatBtn" class="customBtn" onclick="toggleNewChat()">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
               width="20px" height="20px" viewBox="0 0 960 960" style="enable-background:new 0 0 960 960;" xml:space="preserve">
            <style>
              path {
                fill: var(--vscode-editor-foreground);
              }
            </style>
            <path d="M450,120c-16.6,0-30,13.4-30,30v270H150c-16.6,0-30,13.4-30,30s13.4,30,30,30h270v270c0,16.6,13.4,30,30,30s30-13.4,30-30
            V480h270c16.6,0,30-13.4,30-30s-13.4-30-30-30H480V150C480,133.4,466.6,120,450,120z"/>
          </svg>
        </span>
        <!-- Ended by AICoder, pid:34f37e7901f640b14f3a09891086441dc5b245f5 -->

        <!-- 2 history chat btn (for tdd) -->
        <span id="historyChatBtn" class="customBtn" onclick="toggleHistoryChat()">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            width="20px" height="20px" viewBox="0 0 960 960" style="enable-background:new 0 0 960 960;" xml:space="preserve">
            <style>
              path {
                fill: var(--vscode-editor-foreground);
              }
            </style>
            <g>
              <path d="M480,47C241.8,47,48,240.8,48,479c0,238.2,193.8,432,432,432c238.2,0,432-193.8,432-432C912,240.8,718.2,47,480,47z
                M480,863C268.3,863,96,690.7,96,479S268.3,95,480,95s384,172.3,384,384S691.7,863,480,863z"/>
              <path d="M480,469.9V215c0-13.3-10.7-24-24-24s-24,10.7-24,24v264c0,5.9,2.2,11.6,6.1,15.9l192,216c4.7,5.3,11.3,8.1,18,8.1
                c5.7,0,11.4-2,15.9-6c9.9-8.8,10.8-24,2-33.9L480,469.9z"/>
            </g>
            </svg>

        </span>

        <!-- 3 setting btn -->
        <span id="settingBtn" class="customBtn" onclick="toggleSettings()">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            width="20px" height="20px" viewBox="0 0 960 960" style="enable-background:new 0 0 960 960;" xml:space="preserve">
            <style>
              path {
                fill: var(--vscode-editor-foreground); /* 使用 VSCode 的主题前景色 */
              }
            </style>
            <path d="M481.4,324.9c-83.5,0-151.4,67.9-151.4,151.4s67.9,151.4,151.4,151.4s151.4-67.9,151.4-151.4S564.8,324.9,481.4,324.9z
              M481.4,567.6c-50.4,0-91.4-41-91.4-91.4s41-91.4,91.4-91.4s91.4,41,91.4,91.4C572.7,526.6,531.7,567.6,481.4,567.6z"/>
            <path d="M904,370l-14.7-5.9c-36.3-14.5-65.5-39.4-84.3-72c-18.8-32.5-25.8-70.1-20.3-108.7l2.2-15.7L775.3,157
              C726.8,112.7,671,80.6,609.5,61.6L594.7,57l-12.3,9.4c-30.3,23.1-65.7,35.3-102.4,35.3s-72.1-12.2-102.4-35.3L365.3,57l-14.8,4.6
              c-61.4,19-119.3,52.5-167.4,96.8L172,168.8l1.7,15.1c4.2,36.9-3.1,72.8-21.1,104c-18.3,31.7-46.4,56.2-81.3,70.9l-14,5.9l-3.5,14.8
              C46,412.9,42,446.7,42,480.1c0,30.2,3.4,61.4,10.1,92.5l3.3,15.2l14.3,6.1c34.5,14.8,62.4,39.2,80.5,70.6
              c18.6,32.3,25.7,69.6,20.4,107.9l-2.1,15.3l11.3,10.6c46.9,44.2,103.6,78,163.9,97.8l15.2,5l12.6-9.8
              c30.6-23.8,66.5-36.4,103.8-36.4c38.3,0,74.9,13.2,105.9,38.2l12.4,10l15.3-4.7c60.4-18.7,117.2-51,164.1-93.3l12-10.8l-2.4-15.9
              c-5.8-39,1.2-76.9,20.1-109.7c19.2-33.3,49.1-58.5,86.5-72.8l15.1-5.8l3.5-15.8c6.9-31.5,10.4-63.2,10.4-94.2s-3.6-62.8-10.6-94.6
              L904,370z M852,546.5c-43.1,20.1-78,51.7-101.4,92.2c-23,39.9-33.1,85.2-29.4,131.8c-34.3,28.4-73.8,50.9-115.9,65.8
              c-38.9-27.1-83.5-41.4-130.2-41.4c-45.3,0-88.9,13.5-127.1,39.2c-42.1-15.8-81.9-39.6-116.4-69.6c3.3-46-6.9-90.7-29.6-130
              c-22-38.2-54.4-68.7-94.2-88.9c-3.9-22.2-5.9-44.1-5.9-65.5c0-24.1,2.4-48.5,7.2-72.7c40.4-20.2,73.1-50.9,95.4-89.5
              c21.9-37.9,32.1-80.7,30-124.9c35.5-30.4,76.5-54.1,119.8-69.5c37.8,25,80.9,38.1,125.6,38.1s87.8-13.1,125.6-38.2
              c43,15.1,82.5,37.9,117.9,67.9c-3.4,46.3,6.7,91.1,29.5,130.7c22.9,39.7,56.8,70.9,98.8,91c4.1,22.6,6.2,45.1,6.2,67
              C858,501.9,856,524.2,852,546.5z"/>
          </svg>
        </span>
        <!-- Started by AICoder, pid:3ac35e8bffd50c5140ff0a6750ad882c20684f05 -->
        <!-- 4 help btn -->
        <span id="helpBtn" class="customBtn" onclick="toggleHelpPage()">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
               width="20px" height="20px" viewBox="0 0 960 960" style="enable-background:new 0 0 960 960;" xml:space="preserve">
            <style>
                path {
                  fill: var(--vscode-editor-foreground);
                }
                circle {
                  fill: var(--vscode-editor-foreground);
                }
            </style>
            <g>
              <path d="M809.4,298.3c-19.7-46.5-47.8-88.2-83.6-124c-35.9-35.8-77.6-64-124-83.6C553.7,70.3,502.6,60,450,60
                  c-52.6,0-103.7,10.3-151.7,30.6c-46.5,19.7-88.2,47.8-124,83.6c-35.8,35.9-64,77.6-83.6,124C70.3,346.3,60,397.4,60,450
                  c0,52.6,10.3,103.7,30.6,151.7c19.7,46.5,47.8,88.2,83.6,124c35.9,35.8,77.6,64,124,83.6C346.3,829.7,397.4,840,450,840
                  c52.6,0,103.7-10.3,151.7-30.6c46.5-19.7,88.2-47.7,124-83.6s64-77.6,83.6-124C829.7,553.7,840,502.6,840,450
                  C840,397.4,829.7,346.3,809.4,298.3z M450,780c-182,0-330-148-330-330s148-330,330-330s330,148,330,330S632,780,450,780z"/>
              <circle cx="450" cy="637.7" r="37.3"/>
              <path d="M468.9,240.4c-37.4-2-68.3,8.3-91.7,30.4c-31.7,29.9-38.8,71.8-40.4,90.1c-0.3,1.6-0.4,3.3-0.4,5.1c0,0.5,0,1,0,1.5
                  c0,0.9,0,1.7,0,2.1l0.2,0c1.8,14.9,14.4,26.4,29.8,26.4c15.2,0,27.8-11.4,29.7-26.1l0.3,0v0.2c0-0.4,0.9-36,22.2-55.9
                  c11.2-10.4,26.6-15,47.2-13.9c20.6,1.1,34.6,7.3,42.8,19.1c11.5,16.6,9.3,40.6,7.3,47c-3.9,12.1-11.4,18.1-25.5,28.4
                  c-9.5,6.9-20.2,14.7-29.5,26.1c-26.5,32.4-33.4,68.9-34.9,79.3c-2.3,15.7-3.4,37.5-3.4,38.4c-0.8,16.5,12,30.6,28.5,31.4
                  c0.5,0,1,0,1.5,0c15.9,0,29.1-12.5,29.9-28.3c0.1-0.2,1-19.9,2.9-32.9c1.8-12.9,8.7-33.6,21.9-49.8c4.5-5.5,11-10.2,18.5-15.7
                  c15.8-11.5,37.3-27.2,47.3-58.7c5.8-18.4,10.1-62.7-15.2-99.3C544.7,266.2,518.5,243,468.9,240.4z"/>
            </g>
          </svg>
        </span>
        <!-- Ended by AICoder, pid:3ac35e8bffd50c5140ff0a6750ad882c20684f05 -->

        <!-- 5 mode select -->
        <select id="modeSelect">
          <!-- <option value="simple">简单模式</option> -->
          <option value="tdd">TDD模式</option>
          <option value="zero" selected>0号员工模式</option>
        </select>
      </div>
    </div>

    <!-- 嵌入的web -->
    <iframe id="webFrameTdd" class="webFrame-container" style="display: none;" src="http://************:8080/?mode=tdd"></iframe>
    <!-- <iframe id="webFrameSimple" class="webFrame-container" style="display: none;" src="http://************:8080/?mode=simple" ></iframe> -->
    <iframe id="webFrameZero" class="webFrame-container" style="display: block;" src="http://************:8090/iui/employee-zero-iui/index.html?mode=zero" ></iframe>

    <!-- <iframe id="webFrameTdd" class="webFrame-container" style="display: block;" src="http://localhost:4200/?mode=tdd"></iframe>
    <iframe id="webFrameSimple" class="webFrame-container" style="display: none;" src="http://localhost:4200/?mode=simple" ></iframe>
    <iframe id="webFrameZero" class="webFrame-container" style="display: none;" src="http://localhost:4200/?mode=zero" ></iframe> -->
    <script>
      const vscode = acquireVsCodeApi();
      const iframeIds = ['webFrameTdd', 'webFrameZero'];
      const elementIds = [...iframeIds, 'settingBtn', 'newChatBtn', 'historyChatBtn', 'helpBtn'];

      // 发送vscode样式变量数据到web端
      window.onload = () => {
        sendVsCodeStyleDataToAllIframe();
        showCurModeWebFrame('zero'); // default
        execZeroAgentServiceScript();
      }

      // vscode作为webview端与web端的消息中转
      window.addEventListener('message', e => {
        // 监听主题变化事件，重新传递样式变量给iframe
        if (e.data?.type === 'vscode_themeChange') {
          sendVsCodeStyleDataToAllIframe();
          return;
        }
        // 转发从webview到web端的消息
        if (e.data?.from === 'webview') {
          const webFrame = getIsShowWebIframe();
          if (webFrame) {
            webFrame.contentWindow.postMessage(e.data, '*');  // 发送消息到web端
            console.log('chat view post message webview to web:', e);
          }
        } else {
          // 从web端发送消息到webview
          vscode.postMessage(e.data, '*');
          console.log('chat view post message web to webview:', e);
        }
      });

      // 监听三种模式下拉框的变化
      window.document.getElementById('modeSelect').addEventListener('change', function () {
        showCurModeWebFrame(this.value);
      });

      function execZeroAgentServiceScript() {
        // 启动本地后端服务
        vscode.postMessage({ type: 'zero_runShellScript', data: {} });
      }

      function toggleNewChat() {
        let mode = window.document.getElementById('modeSelect').value
        switch(mode) {
          case 'tdd':
            vscode.postMessage({ type: 'tdd_newChat' });
            break;
          case 'zero':
            vscode.postMessage({ type: 'zero_newChat' });
            break;
        }
      }

      function toggleHistoryChat(){
        let mode = window.document.getElementById('modeSelect').value
        switch(mode) {
          case 'tdd':
            vscode.postMessage({ type: 'tdd_historyChat' })
            break;
          
        }
      }

      function toggleSettings() {
        let mode = window.document.getElementById('modeSelect').value
        switch(mode) {
          case 'tdd':
            vscode.postMessage({ type: 'tdd_settings' })
            break;
          case 'zero':
            vscode.postMessage({ type: 'zero_showSettingPanel' });
            break;
        }
      }

      function toggleHelpPage() {
        vscode.postMessage({ type: 'zero_showHelpPanel' })
      }

      /* Started by AICoder, pid:wcb68wcd26x75a9149b10916507384394069be49 */
      function showCurModeWebFrame(mode) {
        switch (mode) {
                // case 'simple':
                //   setDisplayById('webFrameSimple');
                //   settingBtn.style.display = 'none';
                //   newChatBtn.style.display = 'none';
                //   break;
          case 'tdd':
            setElementDisplay({
              webFrameTdd: true,
              webFrameZero: false,
              settingBtn: true,
              newChatBtn: true,
              historyChatBtn: true,
              helpBtn: false,
            });
            break;
          case 'zero':
            setElementDisplay({
              webFrameTdd: false,
              webFrameZero: true,
              settingBtn: true,
              newChatBtn: true,
              historyChatBtn: false,
              helpBtn: true,
            });
            break;
        }
      }

      function setElementDisplay(butOption) {
        elementIds.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
            const isShow = butOption[id];
            element.style.display = isShow ? 'block' : 'none';
          }
        });
      }
      /* Ended by AICoder, pid:wcb68wcd26x75a9149b10916507384394069be49 */

      // 获取唯一显示的iframe
      function getIsShowWebIframe() {
        for (let i = 0; i < iframeIds.length; i++) {
          const id = iframeIds[i];
          const webFrame = window.document.getElementById(id);
          if (webFrame.style.display === 'block') {
            return webFrame;
          }
        }
      }

      // 向所有iframe发送vscode颜色变量
      function sendVsCodeStyleDataToAllIframe() {
        iframeIds.forEach(elementId => {
          const webFrame = window.document.getElementById(elementId);
          webFrame.contentWindow.postMessage({type: 'vscodeStyle', data: document.documentElement.getAttribute('style')}, '*');  // 发送消息到web端
        })
      }
    </script>
  </body>
</html>