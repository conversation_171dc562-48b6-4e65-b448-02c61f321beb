{"name": "employee-zero", "displayName": "0号员工", "description": "employee zero project", "version": "0.0.1", "publisher": "zero", "icon": "media/images/publish-icon.png", "private": true, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-extension-samples"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "extensionDependencies": ["RAN-TAG.accounts"], "activationEvents": [], "main": "./dist/extension", "contributes": {"commands": [{"command": "employeeZero.showChatView", "title": "0号员工对话", "group": "0号员工"}, {"command": "employeeZero.showSettingPanel", "title": "0号员工配置", "group": "0号员工"}, {"command": "employeeZero.showHelpPanel", "title": "0号员工帮助", "group": "0号员工"}, {"command": "employeeZero.generateUnitTest", "title": "0号员工-生成单元测试", "group": "0号员工"}, {"command": "employeeZero.copyToInputCode", "title": "TDD模式-拷贝代码到输入信息中的代码框", "group": "0号员工"}, {"command": "employeeZero.copyToDesignCode", "title": "TDD模式-拷贝代码到设计信息中的代码框", "group": "0号员工"}], "keybindings": [{"command": "employeeZero.showChatView", "key": "ctrl+f10", "mac": "cmd+f10"}, {"command": "employeeZero.showSettingPanel", "key": "ctrl+f11", "mac": "cmd+f11"}, {"command": "employeeZero.showHelpPanel", "key": "ctrl+f12", "mac": "cmd+f12"}, {"command": "employeeZero.generateUnitTest", "key": "ctrl+f9", "mac": "cmd+f9"}, {"command": "employeeZero.copyToInputCode", "key": "ctrl+shift+f9", "mac": "cmd+shift+f9"}, {"command": "employeeZero.copyToDesignCode", "key": "ctrl+shift+f10", "mac": "cmd+shift+f10"}], "submenus": [{"id": "employeeZero.submenu", "label": "0号员工"}], "menus": {"editor/context": [{"submenu": "employeeZero.submenu", "group": "0号员工"}], "employeeZero.submenu": [{"command": "employeeZero.showChatView", "group": "0号员工@1"}, {"command": "employeeZero.showSettingPanel", "group": "0号员工@2"}, {"command": "employeeZero.showHelpPanel", "group": "0号员工@3"}, {"command": "employeeZero.generateUnitTest", "group": "0号员工@4"}, {"command": "employeeZero.copyToInputCode", "group": "0号员工@5"}, {"command": "employeeZero.copyToDesignCode", "group": "0号员工@6"}]}, "viewsContainers": {"activitybar": [{"id": "employee-zero-view", "title": "0号员工", "icon": "media/images/chat-icon.svg"}]}, "views": {"employee-zero-view": [{"id": "employeeZero.chatView", "name": "0号员工对话", "type": "webview"}]}}, "scripts": {"vsce": "vsce", "build": "node ./node_modules/@vscode/vsce/vsce package", "vscode:prepublish": "webpack --mode production", "webpack": "webpack --mode development", "dev": "webpack --mode development --watch", "test-compile": "tsc -p ./", "lint": "eslint \"src/**/*.ts\""}, "devDependencies": {"@types/node": "^16.18.34", "@types/shelljs": "^0.8.15", "@types/vscode": "^1.73.0", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "@vscode/vsce": "^3.2.1", "eslint": "^8.26.0", "ts-loader": "^7.0.5", "typescript": "^5.1.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "dependencies": {"shelljs": "^0.8.5"}}