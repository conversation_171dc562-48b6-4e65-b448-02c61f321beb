import * as vscode from 'vscode';
import {COPY_TO_CODE_INPUT_DESTINATION, VSCODE_COMMANDS, VSCODE_VIEWS_ID} from './constant.global';
import {helpPanelObject, settingPanelObject, zeroViewProvider} from '../extension';
import { FileOperation } from '../utils-class/file-operation.class';

const showSettingsCommand = vscode.commands.registerCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_SETTING_PANEL, () => {
  settingPanelObject.showPanel();
});

const showHelpPageCommand = vscode.commands.registerCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_HELP_PANEL, () => {
  helpPanelObject.showPanel();
});

const showChatViewCommand = vscode.commands.registerCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_CHAT_VIEW, () => {
  // 显示 WebView
  vscode.commands.executeCommand(VSCODE_VIEWS_ID.EMPLOYEE_ZERO_CHAT_VIEW + '.focus');
});

/* Started by AICoder, pid:57372507062ecac1436b08bcb026610224772136 */
const generateUnitTestCommand = vscode.commands.registerCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_GENERATE_UNIT_TEST, () => {
  // 显示 WebView
  console.log('showChatViewCommand');
  vscode.commands.executeCommand(VSCODE_VIEWS_ID.EMPLOYEE_ZERO_CHAT_VIEW + '.focus');
  console.log('generateUnitTest4File start');
  FileOperation.generateUnitTest4File(zeroViewProvider);
});
/* Ended by AICoder, pid:57372507062ecac1436b08bcb026610224772136 */

/* Started by AICoder, pid:y53c8b88a0t2d39144900a3780014a0162e7f162 */
const copyToInputCodeCommand = vscode.commands.registerCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_COPY_TO_INPUT_CODE, () => {
  FileOperation.sendCopyToCodeInputMsg(COPY_TO_CODE_INPUT_DESTINATION.INPUT, zeroViewProvider);
});

const copyToDesignCommand = vscode.commands.registerCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_COPY_TO_DESIGN_CODE, () => {
  FileOperation.sendCopyToCodeInputMsg(COPY_TO_CODE_INPUT_DESTINATION.DESIGN, zeroViewProvider);
});
/* Ended by AICoder, pid:y53c8b88a0t2d39144900a3780014a0162e7f162 */

export const commands = [
  showSettingsCommand,
  showHelpPageCommand,
  showChatViewCommand,
  copyToInputCodeCommand,
  copyToDesignCommand,
  generateUnitTestCommand,
];