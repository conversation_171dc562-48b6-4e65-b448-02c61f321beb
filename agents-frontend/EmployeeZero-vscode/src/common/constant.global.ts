export const VSCODE_COMMANDS = {
  EMPLOYEE_ZERO_SHOW_CHAT_VIEW: 'employeeZero.showChatView',
  EMPLOYEE_ZERO_SHOW_SETTING_PANEL: 'employeeZero.showSettingPanel',
  EMPLOYEE_ZERO_SHOW_HELP_PANEL: 'employeeZero.showHelpPanel',
  EMPLOYEE_ZERO_COPY_TO_INPUT_CODE: 'employeeZero.copyToInputCode',
  EMPLOYEE_ZERO_COPY_TO_DESIGN_CODE: 'employeeZero.copyToDesignCode',
  EMPLOYEE_ZERO_GENERATE_UNIT_TEST: 'employeeZero.generateUnitTest',
};

export const VSCODE_VIEWS_ID = {
  EMPLOYEE_ZERO_CHAT_VIEW: 'employeeZero.chatView',
};

// provider commands
/* Started by AICoder, pid:xef2bk7edf3ec9e14b390ac52025922cc90406a8 */
export const PROVIDER_COMMANDS = {
  VSCODE_THEME_CHANGE: 'vscode_themeChange',
  TDD_CREATE_FILE: 'tdd_createFile',
  TDD_OPEN_FILE: 'tdd_openFile',
  TDD_READ_FILE: 'tdd_readFile',
  TDD_READ_FILES_CONTENT: 'tdd_readFilesContent',
  TDD_GET_ALL_OPENED_FILES_CONTENT: 'tdd_getAllOpenedFilesContent',
  TDD_DELETE_FILE: 'tdd_deleteFile',
  TDD_GET_FILES_LIST: 'tdd_getFileNameList',
  TDD_NEW_CHAT: 'tdd_newChat',
  TDD_HISTORY_CHAT: 'tdd_historyChat',
  TDD_SETTINGS: 'tdd_settings',
  ZERO_SHOW_DATA_IN_OUTPUT_CHANNEL: 'zero_showDataInOutputChannel',
  ZERO_CLOSE_OUTPUT_CHANNEL: 'zero_closeOutputChannel',
  ZERO_WRITE_FULL_CONTENT_TO_FILE: 'zero_writeFullContentToFile',
  ZERO_SAVE_FILE_AT_PATH: 'zero_saveFileAtPath',
  ZERO_UNDO_FILE_AT_PATH: 'zero_undoFileAtPath',
  ZERO_GET_WORKSPACE_ROOT_FOLDER: 'zero_getWorkspaceRootFolder',
  ZERO_GLOBAL_SEARCH_FILES: 'zero_globalSearchFiles',
  ZERO_GLOBAL_SEARCH_DIRS: 'zero_globalSearchDirs',
  ZERO_SHOW_SETTING_PANEL: 'zero_showSettingPanel',
  ZERO_SEND_SETTING_CHANGE_MSG: 'zero_sendSettingChangeMsg',
  ZERO_COPY_TO_CODE_INPUT: 'zero_copyToCodeInput',
  ZERO_GET_EXTENSION_PATH: 'zero_getExtensionPath',
  ZERO_NEW_CHAT: 'zero_newChat',
  ZERO_SHOW_HELP_PANEL: 'zero_showHelpPanel',
  ZERO_RUN_SHELL_SCRIPT: 'zero_runShellScript',
  COMMON_UAC_AUTH: 'common_uac_auth',
  ZERO_GENERATE_UNIT_TEST: 'zero_generateUnitTest',
};

export const PROVIDER_RETURN_COMMANDS = {
  TDD_FILE_CONTENT: 'vscode_tdd_returnFileContent.tdd_readFile',
  TDD_FILES_CONTENT: 'vscode_tdd_returnFileContent.tdd_readFilesContent',
  TDD_ALL_OPENED_FILES_CONTENT: 'vscode_tdd_returnFileContent.tdd_getAllOpenedFilesContent',
  TDD_FILE_LIST: 'vscode_tdd_returnFileNameList',
  TDD_NEW_CHAT: 'vscode_return.tdd_newChat',
  TDD_HISTORY_CHAT: 'vscode_return.tdd_historyChat',
  TDD_SETTINGS: 'vscode_return.tdd_settings',
  ZERO_GET_WORKSPACE_ROOT_FOLDER: 'vscode_return.zero_getWorkspaceRootFolder',
  ZERO_GLOBAL_SEARCH_FILES: 'vscode_return.zero_globalSearchFiles',
  ZERO_GLOBAL_SEARCH_DIRS: 'vscode_return.zero_globalSearchDirs',
  ZERO_SHOW_SETTING_PANEL: 'vscode_return.zero_showSettingPanel',
  ZERO_SEND_SETTING_CHANGE_MSG: 'vscode_return.zero_sendSettingChangeMsg',
  ZERO_GET_EXTENSION_PATH: 'vscode_return.zero_getExtensionPath',
  ZERO_NEW_CHAT: 'vscode_return.zero_newChat',
  ZERO_SHOW_HELP_PANEL: 'vscode_return.zero_showHelpPanel',
  ZERO_RUN_SHELL_SCRIPT: 'vscode_return.zero_runShellScript',
  COMMON_UAC_AUTH: 'vscode_return.common_uac_auth',
  ZERO_OPEN_EXTERNAL_WEB_LINK: 'vscode_return.zero_openExternalWebLink',
};
/* Ended by AICoder, pid:xef2bk7edf3ec9e14b390ac52025922cc90406a8 */

// panel commands
export const PANEL_COMMANDS = {
  VSCODE_THEME_CHANGE: 'vscode_themeChange',
  ZERO_SEND_SETTING_CHANGE_MSG: 'zero_sendSettingChangeMsg',
  ZERO_GET_WORKSPACE_ROOT_FOLDER: 'zero_getWorkspaceRootFolder',
  ZERO_OPEN_EXTERNAL_WEB_LINK: 'zero_openExternalWebLink'
};

export const PANEL_RETURN_COMMANDS = {
  ZERO_SEND_SETTING_CHANGE_MSG: 'vscode_return.zero_sendSettingChangeMsg',
  ZERO_GET_WORKSPACE_ROOT_FOLDER: 'vscode_return.zero_getWorkspaceRootFolder',
  ZERO_OPEN_EXTERNAL_WEB_LINK: 'vscode_return.zero_openExternalWebLink'
};

export const COPY_TO_CODE_INPUT_DESTINATION = {
  DESIGN: 'design_area',
  INPUT: 'input_area'
};

/* Started by AICoder, pid:g3350y37e6raa3114d2d089a70f99b0c8248d0e5 */
// linux	Linux	常见的 Linux 发行版
// win32	Windows	所有 Windows 系统，32 位和 64 位都返回 win32
// darwin	macOS	Apple 的 macOS 系统
export const SYSTEM_PLATFORM = {
  LINUX: 'linux',
  WINDOWS: 'win32',
  MACOS: 'darwin'
};
/* Ended by AICoder, pid:g3350y37e6raa3114d2d089a70f99b0c8248d0e5 */