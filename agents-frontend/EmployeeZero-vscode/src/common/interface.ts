import * as vscode from 'vscode';

export interface BroadcastMessage {
  type: string;
  data?: any;
  useExtensionPath?: boolean;
  from?: 'vscode' | 'webview';
}
export type ReceiveMessageListener = (message: BroadcastMessage) => void;

export type ViewProviderAfterInit = (view: vscode.WebviewView) => void;
export type ViewPanelAfterInit = (view: vscode.WebviewPanel) => void;

export interface CreatePanelOption {
  extensionUri: vscode.Uri;
  htmlName: string;
  viewType: string;
  title: string;
}