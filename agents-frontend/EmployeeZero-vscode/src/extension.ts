/*---------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/

import * as vscode from 'vscode';
import { EmployeeZeroViewProvider } from './webview/view-provider';
import { commands } from './common/commands';
import { FileOperation } from './utils-class/file-operation.class';
import {EmployeeZeroWebviewPanel} from './webview/zero-panel';
import {
  VSCODE_COMMANDS, VSCODE_VIEWS_ID
} from './common/constant.global';
import { LogOperation } from './utils-class/log-operation.class';

export let extensionContext: vscode.ExtensionContext;
export let settingPanelObject: EmployeeZeroWebviewPanel;
export let helpPanelObject: EmployeeZeroWebviewPanel;
export let zeroViewProvider: EmployeeZeroViewProvider;

/**
 * 插件被激活时触发，所有代码总入口
 * @param {*} context 插件上下文
 */
export function activate(context: vscode.ExtensionContext) {
  console.log('插件已被激活，初始化全局资源');
  extensionContext = context;
  // 1）侧边栏chat面板
  zeroViewProvider = new EmployeeZeroViewProvider(context.extensionUri);

  // 2) setting面板
  settingPanelObject = new EmployeeZeroWebviewPanel({
    extensionUri: context.extensionUri,
    htmlName: 'setting.html',
    viewType: VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_SETTING_PANEL,
    title: '0号员工设置',
  });

  // 3) help面板
  helpPanelObject = new EmployeeZeroWebviewPanel({
    extensionUri: context.extensionUri,
    htmlName: 'help.html',
    viewType: VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_HELP_PANEL,
    title: '0号员工帮助',
  });

  // 4) 文件监听
  const workSpaceFileWatcher: vscode.FileSystemWatcher = FileOperation.createWorkSpaceFileWatcher(zeroViewProvider, settingPanelObject);
  const extensionDataFileWatcher: vscode.FileSystemWatcher = FileOperation.createExtensionDataFileWatcher(zeroViewProvider);

  context.subscriptions.push(
    ...commands,
    workSpaceFileWatcher,
    extensionDataFileWatcher,
    vscode.window.registerWebviewViewProvider(
      VSCODE_VIEWS_ID.EMPLOYEE_ZERO_CHAT_VIEW,
      zeroViewProvider,
      { webviewOptions: { retainContextWhenHidden: true } }
    ),
  );
}

/**
 * 插件被停用时触发
 */
export function deactivate() {
  // task
  cleanAll();
}

function cleanAll() {
  // 释放终端和面板对象
  const resources = [...commands, zeroViewProvider, settingPanelObject, helpPanelObject];
  resources.forEach(resource => {
    if (resource) {
      resource.dispose(); // 释放每个资源
    }
  });

  LogOperation.disposeAll();
}