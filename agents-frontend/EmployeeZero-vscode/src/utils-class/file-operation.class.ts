import * as vscode from 'vscode';
import { EmployeeZeroViewProvider } from '../webview/view-provider';
import {extensionContext} from '../extension';
import * as path from 'path';
import * as fs from 'node:fs';
import * as os from 'node:os';
import { PROVIDER_COMMANDS } from '../common/constant.global';
import {EmployeeZeroWebviewPanel} from '../webview/zero-panel';
declare let Buffer: any;

export class FileOperation {

  static showErrorMessage(text: string) {
    vscode.window.showErrorMessage(text);
  }

  // 获取工作区文件夹路径
  static getWorkspaceFolder(): vscode.Uri | undefined {
    const folders = vscode.workspace.workspaceFolders;
    if (folders && folders.length > 0) {
      return folders[0].uri; // 返回第一个工作区文件夹
    }
    this.showErrorMessage('No workspace folder is open.');
    return undefined;
  }

  static getVscodePluginInstallPath(): string {
    return extensionContext.extensionPath;
  }

  static getExtensionPath(): string {
    // 获取当前扩展的安装路径
    const extensionPath = extensionContext.extensionPath;

    // 获取扩展目录的父目录
    const parentDir = path.dirname(extensionPath);

    // 检查父目录是否存在
    if (fs.existsSync(parentDir)) {
      // 如果父目录存在，返回父目录的路径
      return parentDir;
    } else {
      // 如果父目录不存在，返回默认的.vscode/extensions目录路径
      return path.join(os.homedir(), '.vscode', 'extensions');
    }
  }

  // 获取vscode的文件路径
  static getFilePath(fileName: string, useExtensionPath: boolean | undefined): vscode.Uri | undefined {
    if (!fileName) {
      this.showErrorMessage('file path is empty');
      return undefined;
    }
    let folder: string | undefined = '';
    if(useExtensionPath) {
      folder = this.getExtensionPath();
      return vscode.Uri.file(path.join(folder, fileName));
    } else {
      const workspaceFolder = this.getWorkspaceFolder();
      if (!workspaceFolder) {
        return undefined; // 如果没有打开工作区，直接返回
      }
      return vscode.Uri.joinPath(workspaceFolder, fileName);
    }
  }

  /* Started by AICoder, pid:sefafwbcf459d28140ce08b1d0b437125433875a */
  // 文件是否存在
  static async fileIsExist(fileName: string, useExtensionPath: boolean | undefined): Promise<boolean> {
    try {
      const filePath = this.getFilePath(fileName, useExtensionPath);
      if (!filePath) {
        return false;
      }
      await vscode.workspace.fs.stat(filePath);
      return true;
    } catch (error: any) {
      return false;
    }
  }
  /* Ended by AICoder, pid:sefafwbcf459d28140ce08b1d0b437125433875a */

  // 创建文件并写入内容
  static async createFile(fileName: string, fileContent: string, useExtensionPath: boolean | undefined): Promise<vscode.Uri | undefined> {
    const filePath = this.getFilePath(fileName, useExtensionPath);
    if (!filePath) {
      return undefined;
    }
    try {
      // 创建文件并初始化
      await vscode.workspace.fs.writeFile(filePath, Buffer.from(fileContent));
      return filePath; // 返回文件路径
    } catch (error: any) {
      this.showErrorMessage(`Failed to create file: ${error.message}`);
      return undefined;
    }
  }

  // 打开文件到编辑区
  static async openFile(fileName: string, useExtensionPath: boolean | undefined): Promise<vscode.TextEditor | undefined> {
    const filePath = this.getFilePath(fileName, useExtensionPath);
    if (!filePath) {
      return undefined;
    }
    try {
      const doc = await vscode.workspace.openTextDocument(filePath);
      await vscode.window.showTextDocument(doc);
      return vscode.window.activeTextEditor; // 返回当前活动的编辑器
    } catch (error: any) {
      this.showErrorMessage(`Failed to open file: ${error.message}`);
      return undefined;
    }
  }

  // 打开文件到编辑区
  static async openFileWithVsCode(filePath: vscode.Uri): Promise<vscode.TextEditor | undefined> {
    try {
      const doc = await vscode.workspace.openTextDocument(filePath);
      await vscode.window.showTextDocument(doc);
      return vscode.window.activeTextEditor; // 返回当前活动的编辑器
    } catch (error: any) {
      this.showErrorMessage(`Failed to open file: ${error.message}`);
      return undefined;
    }
  }

  // 创建文件&打开文件到编辑器
  static async createAndOpenFile(fileName: string, fileContent: string, useExtensionPath: boolean | undefined): Promise<vscode.TextEditor | undefined> {
    const filePath = await this.createFile(fileName, fileContent, useExtensionPath);
    if (filePath) {
      return this.openFileWithVsCode(filePath); // 调用打开文件的方法
    }
    return undefined; // 如果文件创建失败，返回undefined
  }

  // 删除文件
  static async deleteFile(fileName: string, useExtensionPath: boolean | undefined): Promise<any> {
    const filePath = this.getFilePath(fileName, useExtensionPath);
    if (!filePath) {
      this.showErrorMessage(`Failed to get file path when deleteFile: ${filePath}`);
      return undefined;
    }
    await vscode.workspace.fs.delete(filePath, {recursive: true, useTrash: false});
  }

  // 读取单个文件内容
  static async readFile(fileName: string, useExtensionPath: boolean | undefined): Promise<string | undefined> {
    try {
      const filePath = this.getFilePath(fileName, useExtensionPath);
      if (!filePath) {
        this.showErrorMessage(`Failed to get file path when readFile: ${filePath}`);
        return undefined;
      }
      const data = await vscode.workspace.fs.readFile(filePath);
      const content = data.toString();
      return content;
    } catch (error: any) {
      this.showErrorMessage(`Failed to read file content: ${error.message}`);
      return undefined;
    }

  }

  // 读取文件列表的内容
  static async readFileListContent(fileNameList: Array<string>, useExtensionPath: boolean | undefined): Promise<{ filePath: string; fileContent: string | undefined }[]> {
    const result = [];
    for await (const fileName of fileNameList) {
      const content = await this.readFile(fileName, useExtensionPath);
      if (content !== undefined) {
        result.push({
          filePath: fileName,
          fileContent: content
        });
      }
    }
    return result;
  }

  // 获取当前激活文件的文件路径和内容
  static getCurOpenedFileWithContent() {
    const openedFile = vscode.window.visibleTextEditors.map(editor => {
      const fullPath = editor.document.uri.fsPath; // 获取文件路径
      const relativePath = vscode.workspace.asRelativePath(fullPath); // 转换为相对路径
      const fileContent = editor.document.getText(); // 获取文件内容
      return { filePath: relativePath, fileContent: fileContent }; // 返回路径和内容
    });
    return openedFile;
  }

  // 获取所有打开文件的路径
  static getAllOpenedFilePaths() {
    const openedFilePaths = vscode.window.tabGroups.all.flatMap(({ tabs }) => tabs.map(tab => {
      if (tab.input instanceof vscode.TabInputText || tab.input instanceof vscode.TabInputNotebook) {
        const fullPath = tab.input.uri.fsPath; // 获取文件的完整路径
        const relativePath = vscode.workspace.asRelativePath(fullPath); // 转换为相对路径
        return relativePath;
      }
      return null; // 其他类型的标签不返回路径
    })).filter(Boolean); // 过滤掉 null 值
    return openedFilePaths;
  }

  // 获取所有打开文件的路径和文件内容
  static async getAllOpenedFilesWithContent(): Promise<{ filePath: string; fileContent: string | undefined }[]> {
    const openedFiles = this.getAllOpenedFilePaths();
    const result = [];
    for await (const filePath of openedFiles) {
      if (filePath) {
        const content = await this.readFile(filePath, false);
        if (content !== undefined) {
          result.push({
            filePath: filePath,
            fileContent: content
          });
        }
      }
    }
    return result;
  }

  // 保存当前文件：ctrl + s
  static async saveCurrentFile(filePath: string, useExtensionPath: boolean | undefined) {
    // 先找到文件，打开为激活态
    this.openFile(filePath, useExtensionPath).then(async(activeTextEditor) => {
      if (activeTextEditor) {
        await vscode.commands.executeCommand('workbench.action.files.save');
      }
    });
  }

  // 撤消当前文件：ctrl + z
  static async cancelCurrentFile(filePath: string, useExtensionPath: boolean | undefined) {
    // 先找到文件，打开为激活态
    this.openFile(filePath, useExtensionPath).then(async (activeTextEditor) => {
      if (activeTextEditor) {
        await vscode.commands.executeCommand('undo');
      }
    });
  }

  /**
   * 获取指定目录下的文件和子目录，并返回包含文件和目录数组的对象
   * @param dirPath - 要读取的目录路径
   * @returns - 包含 file 和 dir 的对象
   */
  static async getFileNamesInDirectory(dirPath: string, useExtensionPath: boolean | undefined): Promise<{ file: string[]; dir: string[] } | undefined> {
    const fullPath = await this.getFilePath(dirPath, useExtensionPath);

    if (!fullPath) {
      this.showErrorMessage(`Failed to get file path when getFileNamesInDirectory: ${fullPath}`);
      return undefined;
    }

    try {
      // 读取目录内容
      const entries = await vscode.workspace.fs.readDirectory(fullPath);

      // 初始化结果对象
      const result = {
        file: [] as string[],
        dir: [] as string[],
      };

      // 遍历 entries，将文件和目录分别加入 result 中
      for (const [name, fileType] of entries) {
        if (fileType === vscode.FileType.File) {
          result.file.push(name);
        } else if (fileType === vscode.FileType.Directory) {
          result.dir.push(name);
        }
      }

      return result;
    } catch (error) {
      vscode.window.showErrorMessage(`Error happened when getFileNamesInDirectory: ${dirPath}, error: ${error}`);
      return undefined;
    }
  }

  static createWorkSpaceFileWatcher(chatProvider: EmployeeZeroViewProvider, settingPanel: EmployeeZeroWebviewPanel): vscode.FileSystemWatcher {
    // 创建文件系统观察者
    const workSpaceFileWatcher = vscode.workspace.createFileSystemWatcher('**/*', false, false, false);

    // 监听文件创建事件
    workSpaceFileWatcher.onDidCreate((uri) => {
      // vscode.window.showInformationMessage(`文件或目录创建: ${uri.fsPath}`);
      chatProvider.sendMessage({ type: 'vscode_return.zero_watchFileChanged', data: { type: 'create', path: uri.fsPath }, from: 'webview' });
      settingPanel.sendMessage({ type: 'vscode_return.zero_watchFileChanged', data: { type: 'create', path: uri.fsPath }, from: 'webview' });
    });

    // 监听文件修改事件（可选）
    workSpaceFileWatcher.onDidChange((uri) => {
      // vscode.window.showInformationMessage(`文件修改: ${uri.fsPath}`);
      chatProvider.sendMessage({ type: 'vscode_return.zero_watchFileChanged', data: { type: 'modify', path: uri.fsPath }, from: 'webview' });
      settingPanel.sendMessage({ type: 'vscode_return.zero_watchFileChanged', data: { type: 'modify', path: uri.fsPath }, from: 'webview' });
    });

    // 监听文件删除事件（可选）
    workSpaceFileWatcher.onDidDelete((uri) => {
      // vscode.window.showInformationMessage(`文件删除: ${uri.fsPath}`);
      chatProvider.sendMessage({ type: 'vscode_return.zero_watchFileChanged', data: { type: 'delete', path: uri.fsPath }, from: 'webview' });
      settingPanel.sendMessage({ type: 'vscode_return.zero_watchFileChanged', data: { type: 'delete', path: uri.fsPath }, from: 'webview' });
    });
    return workSpaceFileWatcher;
  }

  static createExtensionDataFileWatcher(provider: EmployeeZeroViewProvider): vscode.FileSystemWatcher {
    // 创建文件系统观察者
    const extensionDataFileWatcher = vscode.workspace.createFileSystemWatcher(new vscode.RelativePattern(vscode.Uri.file(this.getExtensionPath()), '**/*'), false, false, false);

    // 监听文件创建事件
    extensionDataFileWatcher.onDidCreate((uri) => {
      // vscode.window.showInformationMessage(`文件或目录创建: ${uri.fsPath}`);
      provider.sendMessage({ type: 'vscode_return.zero_watchExtensionDataFileChanged', data: { type: 'create', path: uri.fsPath }, from: 'webview' });
    });

    // 监听文件修改事件（可选）
    extensionDataFileWatcher.onDidChange((uri) => {
      // vscode.window.showInformationMessage(`文件修改: ${uri.fsPath}`);
      provider.sendMessage({ type: 'vscode_return.zero_watchExtensionDataFileChanged', data: { type: 'modify', path: uri.fsPath }, from: 'webview' });
    });

    // 监听文件删除事件（可选）
    extensionDataFileWatcher.onDidDelete((uri) => {
      // vscode.window.showInformationMessage(`文件删除: ${uri.fsPath}`);
      provider.sendMessage({ type: 'vscode_return.zero_watchExtensionDataFileChanged', data: { type: 'delete', path: uri.fsPath }, from: 'webview' });
    });
    return extensionDataFileWatcher;
  }

  /* Started by AICoder, pid:y89aekc56cr906c14df50b28c09a71220b500d4e */
  static getSelectedCode() {
    let code = '';
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      const selection = editor.selection;
      code = editor.document.getText(selection);
    }
    return code;
  }

  static sendCopyToCodeInputMsg(destination: string, zeroViewProvider: EmployeeZeroViewProvider) {
    const code = this.getSelectedCode();
    if(code) {
      zeroViewProvider.sendMessage({
        type: PROVIDER_COMMANDS.ZERO_COPY_TO_CODE_INPUT,
        data: { destination: destination, code: code },
        from: 'webview',
      });
    }
  }
  /* Ended by AICoder, pid:y89aekc56cr906c14df50b28c09a71220b500d4e */

  /* Started by AICoder, pid:j9e139080dhc697141d80a1d103dcd2dbe142d14 */
  // 生成当前文件的单元测试
  static generateUnitTest4File(zeroViewProvider: EmployeeZeroViewProvider) {
    const fileData = this.getCurActiveFileWithContent();
    console.log('fileData', fileData);
    if (fileData) {
      zeroViewProvider.sendMessage({
        type: PROVIDER_COMMANDS.ZERO_GENERATE_UNIT_TEST,
        data: fileData,
        from: 'webview',
      });
    }
  }

  // 获取当前激活文件的文件路径和内容
  static getCurActiveFileWithContent(): { filePath: string, fileContent: string } | undefined {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      const fullPath = editor.document.uri.fsPath;
      const relativePath = vscode.workspace.asRelativePath(fullPath);
      const fileContent = this.getSelectedCode(); // 获取选中的代码内容
      return { filePath: relativePath, fileContent: fileContent };
    }
    return undefined;
  }
  /* Ended by AICoder, pid:j9e139080dhc697141d80a1d103dcd2dbe142d14 */
}
