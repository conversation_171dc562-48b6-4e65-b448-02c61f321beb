/* Started by AICoder, pid:wf3c417a26n156d145490997d1a64a231f13bd01 */
import * as vscode from 'vscode';
import * as fs from 'node:fs';
import * as path from 'path';

export interface FileItem {
  filePath: string,
  fileName: string,
  continuousMatchLength: number,
}

export class FileSearch {

  private static exclude = '{**/node_modules/**,**/.angular/**}';
  private static excludedDirs = ['node_modules', '.angular']; // 你可以在此数组中添加任何你想排除的文件夹名称

  // 查找所有文件
  static async findWorkSpaceAllFiles(): Promise<vscode.Uri[]> {
    const files = await vscode.workspace.findFiles('**/*', this.exclude);
    return files;
  }

  /* Started by AICoder, pid:gb798le81fj87cc145ba098980d2ea32ff81866d */
  // 获取当前工作区的所有文件夹
  static async findWorkSpaceAllDirs(): Promise<string[]> {
    const folders = vscode.workspace.workspaceFolders;
    if (folders && folders.length > 0) {
      const root = folders[0].uri.fsPath;
      const dirs = this.getAllSubdirectories(root);
      return dirs;
    }
    return [];
  }

  // 获取路径下所有子文件夹
  static getAllSubdirectories(dirPath: string): string[] {
    let subdirectories: string[] = [];
    try {
      const files = fs.readdirSync(dirPath);
      files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        // 排除文件夹
        if (fs.statSync(fullPath).isDirectory() && !this.excludedDirs.some(excludedDir => fullPath.includes(excludedDir))) {
          subdirectories.push(fullPath);
          // 递归调用获取子文件夹
          subdirectories = subdirectories.concat(this.getAllSubdirectories(fullPath));
        }
      });
    } catch (err) {
      vscode.window.showErrorMessage(`Error read directory: ${err}`);
      return [];
    }
    return subdirectories;
  }
  /* Ended by AICoder, pid:gb798le81fj87cc145ba098980d2ea32ff81866d */

  /* Started by AICoder, pid:zaaacb54fe0676f149f6095e305ae408cca9a7fd */
  static returnLimitFiles(allFiles: string[], limitNum = 8) {
    if (limitNum < 0) {
      limitNum = 8;
    }
    if (allFiles.length <= limitNum) {
      return allFiles;
    }
    return allFiles.slice(0, limitNum);
  }
  /* Ended by AICoder, pid:zaaacb54fe0676f149f6095e305ae408cca9a7fd */

  /*
    * 模糊匹配规则:
    * 1)匹配所有包含 searchText 的文件名，不匹配的直接过滤掉。
    * 2)连续匹配优先：连续匹配字符的数量越多，排名越靠前。
    * 3)字母排序：在匹配优先规则相同的情况下，按文件名的字母顺序升序排序。
    * */
  static fuzzyMatchAndSortV2(files: vscode.Uri[], searchText: string): string[] {
    if (!files || files?.length === 0) {
      return [];
    }
    if (!searchText) {
      const newFiles = files.map(item => item.fsPath);
      return this.returnLimitFiles(newFiles);
    }
    // 不区分大小写的匹配
    const lowerSearchText = searchText.toLowerCase();
    // 筛选并评分
    const scoredFiles = files
      .map(file => {
        const filePath = file.fsPath;
        const fileName = filePath.split('/').pop() || '';
        const lowerFileName = fileName.toLowerCase();

        // 检查是否包含 searchText
        const index = lowerFileName.indexOf(lowerSearchText);
        if (index === -1) {
          return null;
        }
        // 计算连续匹配数量
        const continuousMatchLength = this.calculateMaxContinuousMatch(lowerFileName, lowerSearchText);
        return {filePath: filePath, fileName: fileName, continuousMatchLength: continuousMatchLength} as FileItem;
      })
      .filter(item => item !== null) as FileItem[];

    scoredFiles.sort((a, b) => {
      if (b.continuousMatchLength !== a.continuousMatchLength) {
        return b.continuousMatchLength - a.continuousMatchLength; // 按连续匹配数量降序排列
      }
      return a.fileName.localeCompare(b.fileName); // 按首字母升序
    });

    // 返回排序后的文件名数组
    const sortFiles = scoredFiles.map(item => item.filePath);
    return this.returnLimitFiles(sortFiles);
  }

  static calculateMaxContinuousMatch(fileName: string, searchText: string): number {
    let maxMatch = 0;
    let currentMatch = 0;

    for (let i = 0, j = 0; i < fileName.length; i++) {
      if (fileName[i] === searchText[j]) {
        currentMatch++;
        j++;
        maxMatch = Math.max(maxMatch, currentMatch);
      } else {
        currentMatch = 0;
        if (fileName[i] === searchText[0]) {
          j = 1;
          currentMatch = 1;
        } else {
          j = 0;
        }
      }

      if (j === searchText.length) {
        j = 0;
      }
    }

    return maxMatch;
  }

  /*
    * 模糊匹配规则:
    * 1)匹配所有包含 searchText 中至少一个字符的文件名，不匹配的直接过滤掉。
    * 2)按照匹配到的个数，降序排序
    * */
  static fuzzyMatchAndSortV1(files: vscode.Uri[], searchText: string): string[] {
    // 转小写进行不区分大小写的匹配
    const lowerSearchText = searchText.toLowerCase();
    // 模糊匹配逻辑
    const matchedFiles = files
      .map(file => ({
        uri: file,
        score: FileSearch.calculateFuzzyScore(file.fsPath, lowerSearchText),
      }))
      .filter(item => item.score > 0) // 仅保留匹配到的文件
      .sort((a, b) => b.score - a.score) // 按匹配度排序
      .map(item => item.uri.fsPath);
    return matchedFiles;
  }

  // 模糊匹配评分函数
  static calculateFuzzyScore(filePath: string, searchText: string): number {
    const lowerFileName = filePath.split('/').pop()?.toLowerCase() || '';
    const lowerSearchText = searchText.toLowerCase();
    let score = 0;
    let lastMatchIndex = -1;

    for (const char of lowerSearchText) {
      const index = lowerFileName.indexOf(char, lastMatchIndex + 1);
      if (index === -1) {
        return 0; // 一旦某个字符无法匹配，得分为0
      }
      score += 1;
      lastMatchIndex = index;
    }
    return score;
  }

  /* Started by AICoder, pid:34837me5b0x1b43144dc084f00888d4e229397da */
  /*
    * 模糊匹配规则:
    * 1)匹配所有包含 searchText 的文件夹名，不匹配的直接过滤掉。
    * 2)连续匹配优先：连续匹配字符的数量越多，排名越靠前。
    * 3)字母排序：在匹配优先规则相同的情况下，按文件名的字母顺序升序排序。
    * */
  static fuzzyMatchAndSortDirs(dirs: string[], searchText: string): string[] {
    if (!dirs || dirs?.length === 0) {
      return [];
    }
    if (!searchText) {
      return this.returnLimitFiles(dirs);
    }
    // 不区分大小写的匹配
    const lowerSearchText = searchText.toLowerCase();
    // 筛选并评分
    const scoredDirs = dirs
      .map(dirPath => {
        const dirName = dirPath.split('/').pop() || '';
        const lowerDirName = dirName.toLowerCase();

        // 检查是否包含 searchText
        const index = lowerDirName.indexOf(lowerSearchText);
        if (index === -1) {
          return null;
        }
        // 计算连续匹配数量
        const continuousMatchLength = this.calculateMaxContinuousMatch(lowerDirName, lowerSearchText);
        return {filePath: dirPath, fileName: dirName, continuousMatchLength: continuousMatchLength} as FileItem;
      })
      .filter(item => item !== null) as FileItem[];

    scoredDirs.sort((a, b) => {
      if (b.continuousMatchLength !== a.continuousMatchLength) {
        return b.continuousMatchLength - a.continuousMatchLength; // 按连续匹配数量降序排列
      }
      return a.fileName.localeCompare(b.fileName); // 按首字母升序
    });

    // 返回排序后的文件名数组
    const sortDirs = scoredDirs.map(item => item.filePath);
    return this.returnLimitFiles(sortDirs);
  }
  /* Ended by AICoder, pid:34837me5b0x1b43144dc084f00888d4e229397da */
}
/* Ended by AICoder, pid:wf3c417a26n156d145490997d1a64a231f13bd01 */