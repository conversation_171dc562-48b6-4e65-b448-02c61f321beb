/* Started by AICoder, pid:x4d8acdc60h5e961492d0b01c0b93d3d21b84796 */
import * as vscode from 'vscode';

const outputChannels: Map<string, vscode.OutputChannel> = new Map();

export class LogOperation {

  static getOutputChannel(name: string) {
    let outputChannel = outputChannels.get(name);
    if (!outputChannel) {
      outputChannel = vscode.window.createOutputChannel(name, 'json');
      outputChannels.set(name, outputChannel);
    }
    return outputChannel;
  }

  static appendTimePrefix(text: string) {
    const date = new Date();
    const formattedDate = date.getFullYear() + '-' +
      String(date.getMonth() + 1).padStart(2, '0') + '-' +
      String(date.getDate()).padStart(2, '0') + ' ' +
      String(date.getHours()).padStart(2, '0') + ':' +
      String(date.getMinutes()).padStart(2, '0') + ':' +
      String(date.getSeconds()).padStart(2, '0');
    return `[${formattedDate}] ${text}`;
  }

  static dispose(name: string) {
    const c = outputChannels.get(name);
    if (c) {
      c.dispose();
      outputChannels.delete(name);
    }
  }

  static disposeAll() {
    if (outputChannels.size > 0) {
      outputChannels.forEach(c => c.dispose());
      outputChannels.clear();
    }
  }

  static getOutputChannels() {
    return outputChannels;
  }
}
/* Ended by AICoder, pid:x4d8acdc60h5e961492d0b01c0b93d3d21b84796 */