import * as vscode from 'vscode';

export class TextEditorHandler {
  private editor: vscode.TextEditor | undefined;

  constructor(
    editor: vscode.TextEditor | undefined
  ) {
    this.editor = editor;
  }

  // 覆盖当前激活文件Tab的所有内容
  public async replaceText(text: string, options?: { showFileComparison?: boolean }) {
    if (!this.editor) {
      return;
    }
    const document = this.editor.document;
    const showFileComparison = options?.showFileComparison;
    // 创建一个编辑器事务
    const edit = new vscode.WorkspaceEdit();

    // 获取整个文档范围
    const { lineCount } = document;
    const lastLineText = document.lineAt(lineCount - 1).text;
    const range = new vscode.Range(
      new vscode.Position(0, 0),
      new vscode.Position(lineCount, lastLineText.length)
    );

    // 替换全部文本
    edit.replace(document.uri, range, text);

    // 应用编辑器事务
    await vscode.workspace.applyEdit(edit);

    // 打开文件对比
    showFileComparison !== false && this.openFileComparison();
  }

  private async openFileComparison() {
    await vscode.commands.executeCommand('workbench.files.action.compareWithSaved');
    // 判断是否是内联视图，true则不是内联，切换到内联
    const diffEditorSettings = vscode.workspace.getConfiguration('diffEditor');
    const renderSideBySide = diffEditorSettings.get('renderSideBySide');
    renderSideBySide && await vscode.commands.executeCommand('toggle.diff.renderSideBySide');
  }
}