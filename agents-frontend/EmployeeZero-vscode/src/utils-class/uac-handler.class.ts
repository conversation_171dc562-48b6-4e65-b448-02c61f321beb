import { extensions, window } from 'vscode';

export function getUserInfo() {
  const uacExtension = extensions.getExtension('RAN-TAG.accounts');
  let userInfo;

  if (uacExtension) {
    const importedApi = uacExtension.exports;
    const uacUserInfo = importedApi.getUserInfo();

    if (uacUserInfo && uacUserInfo.userId !== undefined && uacUserInfo.userId !== '' && uacUserInfo.gerritHttpPassword !== undefined && uacUserInfo.gerritHttpPassword !== '') {
      userInfo = uacUserInfo;
    } else {
      window.showErrorMessage('未获取到认证信息，请先登录');
    }

  } else {
    window.showErrorMessage('请先安装登录验证（accounts）插件');
  }

  return userInfo;
}