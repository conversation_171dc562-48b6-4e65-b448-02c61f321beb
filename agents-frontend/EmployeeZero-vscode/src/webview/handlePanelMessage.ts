/* Started by <PERSON><PERSON>oder, pid:955a370ad0n09581409209dc10283118e6016723 */
import { BroadcastMessage } from '../common/interface';
import {PANEL_COMMANDS, PANEL_RETURN_COMMANDS, PROVIDER_RETURN_COMMANDS} from '../common/constant.global';
import {FileOperation} from '../utils-class/file-operation.class';
import {helpPanelObject, settingPanelObject, zeroViewProvider} from '../extension';
import * as vscode from 'vscode';

// 处理返回工作区根目录路径的请求
const handleGetWorkspaceRootFolder = async (message: BroadcastMessage) => {
  const workspaceFolder = FileOperation.getWorkspaceFolder();
  if (workspaceFolder) {
    const rootPath = workspaceFolder.fsPath;
    settingPanelObject.sendMessage({
      type: PANEL_RETURN_COMMANDS.ZERO_GET_WORKSPACE_ROOT_FOLDER,
      data: { path: rootPath },
      from: 'webview'
    });
  }
};

/* Started by AICoder, pid:u728ey8efbl68c0141a40868104ea50478572ccd */
const handleSendSettingChangeMsg = async (message: BroadcastMessage) => {
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.ZERO_SEND_SETTING_CHANGE_MSG,
    data: message.data,
    from: 'webview'
  });
};
/* Ended by AICoder, pid:u728ey8efbl68c0141a40868104ea50478572ccd */

/* Started by AICoder, pid:50ffbo1a76t91f2148530b9540e6c41aa263efe5 */
const handleOpenExternalWebLink = async (message: BroadcastMessage) => {
  const url = message.data?.url;
  if (url) {
    vscode.env.openExternal(vscode.Uri.parse(url));
    helpPanelObject.sendMessage({
      type: PANEL_RETURN_COMMANDS.ZERO_OPEN_EXTERNAL_WEB_LINK,
      data: {},
      from: 'webview'
    });
  } else {
    vscode.window.showErrorMessage('open url is empty.');
  }
};
/* Ended by AICoder, pid:50ffbo1a76t91f2148530b9540e6c41aa263efe5 */

// 将处理函数添加到消息监听器对象
export const panelMessageListenersMap: Record<string, (message: BroadcastMessage) => Promise<void>> = {
  [PANEL_COMMANDS.ZERO_GET_WORKSPACE_ROOT_FOLDER]: handleGetWorkspaceRootFolder,
  [PANEL_COMMANDS.ZERO_SEND_SETTING_CHANGE_MSG]: handleSendSettingChangeMsg,
  [PANEL_COMMANDS.ZERO_OPEN_EXTERNAL_WEB_LINK]: handleOpenExternalWebLink,
};
/* Ended by AICoder, pid:955a370ad0n09581409209dc10283118e6016723 */