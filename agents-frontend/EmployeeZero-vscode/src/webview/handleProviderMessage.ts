import * as vscode from 'vscode';
import { FileOperation } from '../utils-class/file-operation.class';
import { BroadcastMessage } from '../common/interface';
import { TextEditorHandler } from '../utils-class/texteditor-handler.class';
import { FileSearch } from '../utils-class/file-search.class';
import { getUserInfo } from '../utils-class/uac-handler.class';
import {PROVIDER_COMMANDS, PROVIDER_RETURN_COMMANDS, SYSTEM_PLATFORM, VSCODE_COMMANDS} from '../common/constant.global';
import {settingPanelObject, zeroViewProvider} from '../extension';
import * as path from 'path';
import * as fs from 'node:fs';
import {spawn} from 'node:child_process';
import { LogOperation } from '../utils-class/log-operation.class';
declare let process: any;

// 定义创建文件的请求
const handleCreateFile = async (message: BroadcastMessage) => {
  await FileOperation.createFile(message.data?.filePath, message.data?.fileContent, message.useExtensionPath);
};

// 处理打开文件的请求
const handleOpenFile = async (message: BroadcastMessage) => {
  await FileOperation.openFile(message.data?.filePath, message.useExtensionPath);
};

// 处理读取文件的请求
const handleReadFile = async (message: BroadcastMessage) => {
  const content = await FileOperation.readFile(message.data?.filePath, message.useExtensionPath);
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_FILE_CONTENT,
    data: [{ filePath: message.data?.filePath, fileContent: content }],
    from: 'webview'
  });
};

// 处理读取多个文件内容的请求
const handleReadFilesContent = async (message: BroadcastMessage) => {
  const res = await FileOperation.readFileListContent(message.data, message.useExtensionPath);
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_FILES_CONTENT,
    data: res,
    from: 'webview'
  });
};

// 处理获取所有已打开文件内容的请求
const handleGetAllOpenedFilesContent = async (message: BroadcastMessage) => {
  const filePathContentList = await FileOperation.getAllOpenedFilesWithContent();
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_ALL_OPENED_FILES_CONTENT,
    data: filePathContentList,
    from: 'webview'
  });
};

// 处理删除文件的请求
const handleDeleteFile = async (message: BroadcastMessage) => {
  await FileOperation.deleteFile(message.data?.filePath, message.useExtensionPath);
};

// 处理保存指定路径文件的请求
const handleSaveFileAtPath = async (message: BroadcastMessage) => {
  await FileOperation.saveCurrentFile(message.data?.filePath, message.useExtensionPath); // 调用保存文件的方法
};

// 处理撤消指定路径文件的请求
const handleUndoFileAtPath = async (message: BroadcastMessage) => {
  await FileOperation.cancelCurrentFile(message.data?.filePath, message.useExtensionPath); // 调用撤消文件的方法
};

// 处理返回工作区根目录路径的请求
const handleGetWorkspaceRootFolder = async (message: BroadcastMessage) => {
  const workspaceFolder = FileOperation.getWorkspaceFolder();
  if (workspaceFolder) {
    const rootPath = workspaceFolder.fsPath;
    zeroViewProvider.sendMessage({
      type: PROVIDER_RETURN_COMMANDS.ZERO_GET_WORKSPACE_ROOT_FOLDER,
      data: { path: rootPath },
      from: 'webview'
    });
  }
};

// 处理在Output显示数据的请求
const handleShowDataInOutputChannel = async (message: BroadcastMessage) => {
  try {
    const dataToShow = message.data?.content;
    const channelName = message.data?.channelName;
    if (dataToShow && channelName) {
      const c = LogOperation.getOutputChannel(channelName);
      c.show(true);
      c.appendLine(dataToShow);
    } else {
      vscode.window.showErrorMessage('show in output data is empty.');
    }
  } catch (e: any) {
    vscode.window.showErrorMessage('show data to output error:', e);
  }
};

// 处理关闭Output Channel的请求
const handleCloseOutputChannel = async (message: BroadcastMessage) => {
  const channelName = message.data?.channelName;
  if (channelName) {
    LogOperation.dispose(channelName);
  } else {
    vscode.window.showErrorMessage('channelName is empty.');
  }
};

// 处理写入完整内容到文件的请求
const handleWriteFullContentToFile = async (message: BroadcastMessage) => {
  const fileExist = await FileOperation.fileIsExist(message.data?.filePath, message.useExtensionPath);
  if (!fileExist) {
    await FileOperation.createFile(message.data?.filePath, '', message.useExtensionPath);
  }
  // 先找到文件，打开为激活态
  const activeTextEditor = await FileOperation.openFile(message.data?.filePath, message.useExtensionPath);
  if (activeTextEditor) {
    // 替换ALL内容
    const textEditorHandler = new TextEditorHandler(activeTextEditor);
    await textEditorHandler.replaceText(message.data?.fileContent || '', { showFileComparison: true });
  }
};

/* Started by AICoder, pid:n9c29sdc68rab6614fa10b145079471b1099c1ce */
// 处理全局搜索文件的请求
const handleGlobalSearchFiles = async (message: BroadcastMessage) => {
  try {
    const searchText = message.data?.searchText;
    // 查找所有文件
    const files = await FileSearch.findWorkSpaceAllFiles();
    // 模糊匹配逻辑
    const matchedFiles = FileSearch.fuzzyMatchAndSortV2(files, searchText);
    zeroViewProvider.sendMessage({
      type: PROVIDER_RETURN_COMMANDS.ZERO_GLOBAL_SEARCH_FILES,
      data: { searchResult: matchedFiles },
      from: 'webview',
    });
  } catch (error: any) {
    vscode.window.showErrorMessage(`search file error: ${error.message}`);
  }
};
/* Ended by AICoder, pid:n9c29sdc68rab6614fa10b145079471b1099c1ce */

/* Started by AICoder, pid:e6b71a42c3n78f1145790b61b066d516b2860f63 */
// 处理全局搜索文件夹的请求
const handleGlobalSearchDirs = async (message: BroadcastMessage) => {
  try {
    const searchText = message.data?.searchText;
    const dirs = await FileSearch.findWorkSpaceAllDirs();
    // 模糊匹配逻辑
    const matchedDirs = FileSearch.fuzzyMatchAndSortDirs(dirs, searchText);
    zeroViewProvider.sendMessage({
      type: PROVIDER_RETURN_COMMANDS.ZERO_GLOBAL_SEARCH_DIRS,
      data: { searchResult: matchedDirs },
      from: 'webview',
    });
  } catch (error: any) {
    vscode.window.showErrorMessage(`Search dir error: ${error.message}`);
  }
};
/* Ended by AICoder, pid:e6b71a42c3n78f1145790b61b066d516b2860f63 */

const handleShowSettingPanel = async (message: BroadcastMessage) => {
  vscode.commands.executeCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_SETTING_PANEL);
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.ZERO_SHOW_SETTING_PANEL,
    data: {},
    from: 'webview',
  });
};

// 处理返回插件配置信息目录路径
const handleGetExtensionPath = async (message: BroadcastMessage) => {
  const path = FileOperation.getExtensionPath();
  if (path) {
    // 去除前缀并获取根路径
    const extensionPath = path.replace(/^file:\/\//, '');
    zeroViewProvider.sendMessage({
      type: PROVIDER_RETURN_COMMANDS.ZERO_GET_EXTENSION_PATH,
      data: { path: extensionPath },
      from: 'webview'
    });
  }
};

const handleGetFileNameList = async (message: BroadcastMessage) => {
  const filesList = await FileOperation.getFileNamesInDirectory(message.data?.dirPath, message.useExtensionPath);
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_FILE_LIST,
    data: filesList,
    from: 'webview'
  });
};

/* Started by AICoder, pid:o392bz448faa9f214b960896f0c70a00f248ed8a */
const handleTddNewChat = async (message: BroadcastMessage) => {
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_NEW_CHAT,
    data: {},
    from: 'webview',
  });
};
/* Ended by AICoder, pid:o392bz448faa9f214b960896f0c70a00f248ed8a */

/* Started by AICoder, pid:o392bz448faa9f214b960896f0c70a00f248ed8a */
const handleZeroNewChat = async (message: BroadcastMessage) => {
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.ZERO_NEW_CHAT,
    data: {},
    from: 'webview',
  });
};
/* Ended by AICoder, pid:o392bz448faa9f214b960896f0c70a00f248ed8a */

/* Started by AICoder, pid:o392bz448faa9f214b960896f0c70a00f248ed8a */
const handleZeroShowHelp = async (message: BroadcastMessage) => {
  vscode.commands.executeCommand(VSCODE_COMMANDS.EMPLOYEE_ZERO_SHOW_HELP_PANEL);
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.ZERO_SHOW_HELP_PANEL,
    data: {},
    from: 'webview',
  });
};
/* Ended by AICoder, pid:o392bz448faa9f214b960896f0c70a00f248ed8a */

const handleTddHistoryChat = async (message: BroadcastMessage) => {
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_HISTORY_CHAT,
    data: {},
    from: 'webview'
  });
};

const handleTddSettings = async (message: BroadcastMessage) => {
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.TDD_SETTINGS,
    data: {},
    from: 'webview'
  });
};

/* Started by AICoder, pid:1482ahb452vdc4b140f00babd08b0a73c6a40403 */
// 处理运行指定路径下的脚本
// 若filePath为空，则使用内置的路径执行脚本；
// 若filePath不为空，则使用传递的路径执行脚本；
const handleRunShellScript = async (message: BroadcastMessage) => {
  const filePath = message.data?.filePath;
  let scriptPath = '';
  // (1)使用传递的路径执行脚本
  if (filePath) {
    if (!filePath.endsWith('.sh') && !filePath.endsWith('.bat')) { // 注意这里是逻辑与
      vscode.window.showErrorMessage('invalid file type');
      return;
    }
    scriptPath = path.join(FileOperation.getVscodePluginInstallPath(), filePath);
  } else {
    // (2)使用内置的路径执行脚本
    scriptPath = getShellExecPathInDiffPlatform();
    if (!scriptPath) {
      vscode.window.showErrorMessage(`unsupported system platform: ${process.platform}`);
      return;
    }
  }
  // (3) 判断路径是否存在
  const fileIsExist = fs.existsSync(scriptPath);
  if (!fileIsExist) {
    vscode.window.showErrorMessage(`platform: ${process.platform}, file path not exist`);
    return;
  }
  // (4)执行命令并获取实时输出
  execScript(scriptPath);
};

function execScript(scriptPath: string) {
  /* Started by AICoder, pid:d4e18t32062fcc71468c0b965049db465861dda5 */
  const outputChannel = LogOperation.getOutputChannel('zero script');
  outputChannel.show(true); // 显示输出通道
  outputChannel.appendLine(LogOperation.appendTimePrefix(`run script start...`));

  const spawnOptions = {
    shell: true,
    // Windows 下需要设置这些选项以确保正确执行 .bat 文件
    windowsVerbatimArguments: process.platform === 'win32',
    windowsHide: false
  };
  const cdStr = `cd ${path.join(FileOperation.getVscodePluginInstallPath(), 'zero-agents')}`;
  const command = `${cdStr} && ${scriptPath}`;
  const childProcess = spawn(command, spawnOptions);
  let execResult = '';

  // 实时获取标准输出
  childProcess.stdout.on('data', (data) => {
    const output = data.toString();
    outputChannel.appendLine(output);
  });

  // 实时获取标准错误
  childProcess.stderr.on('data', (data) => {
    const errOutput = data.toString();
    outputChannel.appendLine(errOutput);
  });

  // 进程结束时的处理
  childProcess.on('close', (code) => {
    if (code !== 0) {
      execResult = 'fail';
      vscode.window.showErrorMessage(`platform: ${process.platform}, exec error [${scriptPath}]`);
      outputChannel.appendLine(LogOperation.appendTimePrefix(`run script fail`));
    } else {
      execResult = 'success';
      vscode.window.showInformationMessage(`platform: ${process.platform}, exec success [${scriptPath}]`);
      outputChannel.appendLine(LogOperation.appendTimePrefix(`run script success`));
    }
    // 发送给iframe结果
    afterExecScriptThenSendMsg(execResult);
  });
  /* Ended by AICoder, pid:d4e18t32062fcc71468c0b965049db465861dda5 */
}

function getShellExecPathInDiffPlatform(): string {
  const vscodePluginInstallPath = FileOperation.getVscodePluginInstallPath();
  const scriptPaths: Record<string, string> = {
    [SYSTEM_PLATFORM.LINUX]: path.join(vscodePluginInstallPath, 'zero-agents/run.sh'),
    [SYSTEM_PLATFORM.WINDOWS]: path.join(vscodePluginInstallPath, 'zero-agents/run.bat'),
  };
  return scriptPaths[process.platform] || '';
}

function afterExecScriptThenSendMsg(execResult: string) {
  settingPanelObject.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.ZERO_RUN_SHELL_SCRIPT,
    data: {execResult: execResult},
    from: 'webview'
  });
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.ZERO_RUN_SHELL_SCRIPT,
    data: {execResult: execResult},
    from: 'webview'
  });
}
/* Ended by AICoder, pid:1482ahb452vdc4b140f00babd08b0a73c6a40403 */

const handleUacAuth = async (message: BroadcastMessage) => {
  const userInfo = await getUserInfo();
  zeroViewProvider.sendMessage({
    type: PROVIDER_RETURN_COMMANDS.COMMON_UAC_AUTH,
    data: {
      userInfo: userInfo
    },
    from: 'webview'
  });
};

/* Started by AICoder, pid:o44d9da805f78131496f099df08ca61c9a97ffc8 */
// 将处理函数添加到消息监听器对象
export const messageListenersMap: Record<string, (message: BroadcastMessage) => Promise<void>> = {
  [PROVIDER_COMMANDS.TDD_CREATE_FILE]: handleCreateFile,
  [PROVIDER_COMMANDS.TDD_OPEN_FILE]: handleOpenFile,
  [PROVIDER_COMMANDS.TDD_READ_FILE]: handleReadFile,
  [PROVIDER_COMMANDS.TDD_READ_FILES_CONTENT]: handleReadFilesContent,
  [PROVIDER_COMMANDS.TDD_GET_ALL_OPENED_FILES_CONTENT]: handleGetAllOpenedFilesContent,
  [PROVIDER_COMMANDS.TDD_DELETE_FILE]: handleDeleteFile,
  [PROVIDER_COMMANDS.TDD_GET_FILES_LIST]: handleGetFileNameList,
  [PROVIDER_COMMANDS.TDD_NEW_CHAT]: handleTddNewChat,
  [PROVIDER_COMMANDS.TDD_HISTORY_CHAT]: handleTddHistoryChat,
  [PROVIDER_COMMANDS.TDD_SETTINGS]: handleTddSettings,
  [PROVIDER_COMMANDS.ZERO_SHOW_DATA_IN_OUTPUT_CHANNEL]: handleShowDataInOutputChannel,
  [PROVIDER_COMMANDS.ZERO_CLOSE_OUTPUT_CHANNEL]: handleCloseOutputChannel,
  [PROVIDER_COMMANDS.ZERO_WRITE_FULL_CONTENT_TO_FILE]: handleWriteFullContentToFile,
  [PROVIDER_COMMANDS.ZERO_SAVE_FILE_AT_PATH]: handleSaveFileAtPath,
  [PROVIDER_COMMANDS.ZERO_UNDO_FILE_AT_PATH]: handleUndoFileAtPath,
  [PROVIDER_COMMANDS.ZERO_GET_WORKSPACE_ROOT_FOLDER]: handleGetWorkspaceRootFolder,
  [PROVIDER_COMMANDS.ZERO_GLOBAL_SEARCH_FILES]: handleGlobalSearchFiles,
  [PROVIDER_COMMANDS.ZERO_GLOBAL_SEARCH_DIRS]: handleGlobalSearchDirs,
  [PROVIDER_COMMANDS.ZERO_SHOW_SETTING_PANEL]: handleShowSettingPanel,
  [PROVIDER_COMMANDS.ZERO_GET_EXTENSION_PATH]: handleGetExtensionPath,
  [PROVIDER_COMMANDS.ZERO_NEW_CHAT]: handleZeroNewChat,
  [PROVIDER_COMMANDS.ZERO_SHOW_HELP_PANEL]: handleZeroShowHelp,
  [PROVIDER_COMMANDS.ZERO_RUN_SHELL_SCRIPT]: handleRunShellScript,
  [PROVIDER_COMMANDS.COMMON_UAC_AUTH]: handleUacAuth
};
/* Ended by AICoder, pid:o44d9da805f78131496f099df08ca61c9a97ffc8 */
