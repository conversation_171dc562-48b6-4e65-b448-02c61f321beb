import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { BroadcastMessage, ReceiveMessageListener, ViewProviderAfterInit } from '../common/interface';
import { PROVIDER_COMMANDS } from '../common/constant.global';
import { FileOperation } from '../utils-class/file-operation.class';
import { messageListenersMap } from './handleProviderMessage';

export class EmployeeZeroViewProvider implements vscode.WebviewViewProvider {
  private _view?: vscode.WebviewView;
  private listeners: ReceiveMessageListener[] = [];
  private afterInitCallbacks: ViewProviderAfterInit[] = [];

  // 实例化回调
  public afterInit(afterInitCallback: ViewProviderAfterInit) {
    this.afterInitCallbacks.push(afterInitCallback);
  }

  // 消息发送
  public sendMessage(message: BroadcastMessage) {
    this._view?.webview.postMessage(message);
  }

  // 新增消息监听
  public addReceiveMessageListener(callFn: ReceiveMessageListener) {
    if (typeof callFn === 'function' && !this.listeners.some(listener => callFn === listener)) {
      this.listeners.push(callFn);
    }
  }

  // 移除消息监听
  public removeReceiveMessageListener(callFn: ReceiveMessageListener) {
    if (Array.isArray(this.listeners) && this.listeners.length) {
      const index = this.listeners.findIndex(listener => callFn === listener);
      this.listeners.splice(index, 1);
    }
  }

  // 新增销毁资源的方法
  public dispose() {
    this._view = undefined;
    this.listeners = [];
    this.afterInitCallbacks = [];
  }

  constructor(
      private readonly _extensionUri: vscode.Uri
  ) {}

  resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri],
    };

    if (!webviewView.webview.html) {
      // 替换html中的资源路径
      webviewView.webview.html = this.getHtmlReplacedContent(webviewView);
      // 接收传递的数据
      webviewView.webview.onDidReceiveMessage(message => {
        if (Array.isArray(this.listeners) && this.listeners.length) {
          this.listeners.forEach(listener => {
            listener(message);
          });
        }
      });
      // 初始化回调
      this.afterInitCallbacks.forEach(afterInitCallback => {
        afterInitCallback(this._view as vscode.WebviewView);
      });

      // 监听消息，处理逻辑
      this.addReceiveMessageListener( async (message) => {
        console.log(`===> zero provider watch [${message?.type}] message: [${message}]`);
        // 处理各种监听消息
        const handler = messageListenersMap[message.type];
        if (handler) {
          await handler(message);
        } else {
          FileOperation.showErrorMessage(`provider unknown command: ${message.type}`);
        }
      });
      // 监听vscode主题变化
      vscode.window.onDidChangeActiveColorTheme((theme) => {
        this.sendMessage({ type: PROVIDER_COMMANDS.VSCODE_THEME_CHANGE, data: {theme: theme}});
      });

      // 处理视图被销毁
      webviewView.onDidDispose(() => {
        this.dispose();
        console.log('===> zero provider onDidDispose:', this._view);
      });
    }
  }

  private getHtmlReplacedContent(webviewView: vscode.WebviewView): string {
    const htmlPath = path.join(this._extensionUri.fsPath, 'media', 'view.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf-8');

    const replacedContent = htmlContent.replace(/(<img\s+src="|<link\s+href="|<script\s+src=")(.+?)"/g, (match, prefix, src) => {
      const resolvedSrc = path.resolve(path.dirname(htmlPath), src);
      const srcUri = webviewView.webview.asWebviewUri(vscode.Uri.file(resolvedSrc));
      return `${prefix}${srcUri}"`;
    });

    return replacedContent;
  }
}
