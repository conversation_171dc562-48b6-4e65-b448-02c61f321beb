/* Started by AICoder, pid:69a6d43c6e26c1314a2f09fd61caf62aee21a8ad */
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import {BroadcastMessage, CreatePanelOption, ReceiveMessageListener, ViewPanelAfterInit} from '../common/interface';
import {panelMessageListenersMap} from './handlePanelMessage';
import {FileOperation} from '../utils-class/file-operation.class';
import {PROVIDER_COMMANDS} from '../common/constant.global';

export class EmployeeZeroWebviewPanel {
  private panel: vscode.WebviewPanel | undefined;
  private readonly disposables: vscode.Disposable[] = [];
  private listeners: ReceiveMessageListener[] = [];
  private afterInitCallbacks: ViewPanelAfterInit[] = [];

  constructor(
      private readonly panelOption: CreatePanelOption,
  ) {
    this.panelOption = panelOption;
  }

  public showPanel() {
    if (this?.panel) {
      this.panel?.reveal();
    } else {
      this.createNewPanel();
    }
  }

  // 创建 WebviewPanel
  private createNewPanel() {
    this.panel = vscode.window.createWebviewPanel(
      this.panelOption.viewType,
      this.panelOption.title,
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true, // 保持上下文，避免频繁重建
        localResourceRoots: [vscode.Uri.joinPath(this.panelOption.extensionUri, 'media')],
      }
    );

    // 设置 Webview HTML 内容
    this.panel.webview.html = this.getHtmlReplacedContent();

    // 监听 Webview 的关闭事件
    this.panel.onDidDispose(() => this.dispose(), null, this.disposables);

    // 监听 Webview 中的消息
    this.panel.webview.onDidReceiveMessage(message => {
      if (Array.isArray(this.listeners) && this.listeners.length) {
        this.listeners.forEach(listener => {
          listener(message);
        });
      }
    });
    // 初始化回调
    this.afterInitCallbacks.forEach(afterInitCallback => {
      afterInitCallback(this.panel as vscode.WebviewPanel);
    });

    // 监听消息，处理逻辑
    this.addReceiveMessageListener( async (message) => {
      console.log(`===> zero panel watch [${message?.type}] message: [${message}]`);
      const handler = panelMessageListenersMap[message.type];
      if (handler) {
        await handler(message);
      } else {
        FileOperation.showErrorMessage(`panel unknown command: ${message.type}`);
      }
    });

    // 监听vscode主题变化
    vscode.window.onDidChangeActiveColorTheme((theme) => {
      this.sendMessage({ type: PROVIDER_COMMANDS.VSCODE_THEME_CHANGE, data: {theme: theme}});
    });
  }

  // 实例化回调
  public afterInit(afterInitCallback: ViewPanelAfterInit) {
    this.afterInitCallbacks.push(afterInitCallback);
  }

  // 消息发送
  public sendMessage(message: BroadcastMessage) {
    this.panel?.webview.postMessage(message);
  }

  // 新增消息监听
  public addReceiveMessageListener(callFn: ReceiveMessageListener) {
    if (typeof callFn === 'function' && !this.listeners.some(listener => callFn === listener)) {
      this.listeners.push(callFn);
    }
  }

  // 移除消息监听
  public removeReceiveMessageListener(callFn: ReceiveMessageListener) {
    if (Array.isArray(this.listeners) && this.listeners.length) {
      const index = this.listeners.findIndex(listener => callFn === listener);
      this.listeners.splice(index, 1);
    }
  }

  private getHtmlReplacedContent(): string {
    const htmlPath = path.join(this.panelOption.extensionUri.fsPath, 'media', this.panelOption.htmlName);
    const htmlContent = fs.readFileSync(htmlPath, 'utf-8');

    const replacedContent = htmlContent.replace(/(<img\s+src="|<link\s+href="|<script\s+src=")(.+?)"/g, (match, prefix, src) => {
      const resolvedSrc = path.resolve(path.dirname(htmlPath), src);
      const srcUri = this.panel?.webview.asWebviewUri(vscode.Uri.file(resolvedSrc));
      return `${prefix}${srcUri}"`;
    });

    return replacedContent;
  }

  // 关闭 WebviewPanel 时释放资源
  public dispose(): void {
    this.panel = undefined;
    this.listeners = [];
    this.afterInitCallbacks = [];

    // 清理所有事件监听和资源
    while (this.disposables.length) {
      const disposable = this.disposables.pop();
      disposable?.dispose();
    }
  }
}
/* Ended by AICoder, pid:69a6d43c6e26c1314a2f09fd61caf62aee21a8ad */