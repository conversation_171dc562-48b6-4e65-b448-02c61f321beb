<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAT</title>
  </head>
  <body>
    <button id="fullScreen">fullScreen</button>
    <script>
      window.onload = () => {
        const btn = document.getElementById('fullScreen');
        btn.onclick = () => {
          window.parent.postMessage({type: 'fullScreen', data: null}, '*');
        }

        window.addEventListener('message', (e) => {
          console.log(e);
        })
      }
    </script>
  </body>