import { Anthropic } from "@anthropic-ai/sdk"
import OpenAI, { AzureOpenAI } from "openai"
import { ApiHandlerOptions, ModelInfo, openAiModelInfoSaneDefaults, qwenCoderModelInfo } from "../../shared/api"
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../index"
import { convertToOpenAiMessages } from "../transform/openai-format"
import { ApiStream } from "../transform/stream"

export class <PERSON>lamaHandler implements ApiHandler {
	private options: ApiHandlerOptions
	private client: OpenAI

	constructor(options: ApiHandlerOptions) {
		this.options = options
		this.client = new OpenAI({
			baseURL: "http://10.55.19.154:30800/api/msb-423a34b174e447f2b6ad8943688d4e0c/v0/zte-gpt-coder/v0/v1",
			apiKey: "XXXX",
		})
	}

	async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
		const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
			{ role: "system", content: systemPrompt },
			...convertToOpenAiMessages(messages),
		]
		const stream = await this.client.chat.completions.create({
			model: "/model",
			messages: openAiMessages,
			temperature: 0,
			stream: true,
			stream_options: { include_usage: true },
		})
		for await (const chunk of stream) {
			const delta = chunk.choices[0]?.delta
			if (delta?.content) {
				yield {
					type: "text",
					text: delta.content,
				}
			}
			if (chunk.usage) {
				if (chunk.choices[0]?.finish_reason === 'stop') {
					yield {
						type: "usage",
						inputTokens: chunk.usage.prompt_tokens || 0,
						outputTokens: chunk.usage.completion_tokens || 0,
					}
					console.log('Stream ended. Input tokens:', chunk.usage.prompt_tokens || 0, 'Output tokens:', chunk.usage.completion_tokens || 0);
				} else {
					yield {
						type: "usage",
						inputTokens: 0,
						outputTokens: 0,
					}
				}
			}
		}
	}

	getModel(): { id: string; info: ModelInfo } {
		return {
			id: this.options.openAiModelId ?? "",
			info: qwenCoderModelInfo,
		}
	}
}
