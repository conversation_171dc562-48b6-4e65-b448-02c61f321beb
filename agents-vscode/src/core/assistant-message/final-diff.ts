import { ZeroLogger } from "../../utils/logger";
import { blockAnchorFallbackMatch, lineTrimmedFallbackMatch } from "./diff";
import { validateMarkerSequencing } from "./validate-marker-sequencing";

const logger = new ZeroLogger('final-diff');

interface DiffBlock {
	search: string;
	replace: string;
	searchMatchIndex: number;
	searchEndIndex: number;
}

export async function constructFinalContent(lines: string[], originalContent: string): Promise<string> {
	// Step 1: Collect all diff blocks into an array
	const diffBlocks: DiffBlock[] = []
	let currentBlock: Partial<DiffBlock> = {
		searchMatchIndex: -1,
		searchEndIndex: -1
	}
	let inSearch = false
	let inReplace = false

	const validation = validateMarkerSequencing(lines);
    if (!validation.success) {
        throw new Error(validation.error);
    }

	for (const line of validation.result || []) {
		if (line === "<<<<<<< SEARCH") {
			
			inSearch = true
			currentBlock = { search: "", replace: "" }
			continue
		}

		if (line === "=======") {

			inSearch = false
			inReplace = true

			calculateMatchIndices(currentBlock, originalContent)

			continue
		}

		if (line === ">>>>>>> REPLACE") {

			inReplace = false

			diffBlocks.push(currentBlock as DiffBlock)

			continue
		}

		if (inSearch) {
			currentBlock.search += line + "\n"
		} else if (inReplace) {
			currentBlock.replace += line + "\n"
		}
	}

	// Validate all search/replace blocks have actual changes
	if (diffBlocks.length > 0 && diffBlocks.every(b => b.search.trim() === b.replace.trim())) {
		logger.error(`Search and replace content are identical `)
		throw new Error(
			`Search and replace content are identical - no changes would be made\n\n` +
			`Debug Info:\n` +
			`- Search and replace must be different to make changes\n` +
			`- Use read_file to verify the content you want to change`
		);
	}

	// Step 2: Process each diff block
	let result = ""
	let lastProcessedIndex = 0

	diffBlocks.sort((a, b) => a.searchMatchIndex - b.searchMatchIndex)

	for (const block of diffBlocks) {
		// Output everything up to the match location
		result += originalContent.slice(lastProcessedIndex, block.searchMatchIndex)

		// Output replacement content
		result += block.replace

		// Advance lastProcessedIndex to after the matched section
		lastProcessedIndex = block.searchEndIndex
	}

	result += originalContent.slice(lastProcessedIndex)

	return result
}


/**
 * Calculates the match indices for a diff block using multiple matching strategies
 */
function calculateMatchIndices(block: Partial<DiffBlock>, originalContent: string) {
	if (!block.search) {
		// Empty search block case
		if (originalContent.length === 0) {
			block.searchMatchIndex = 0
			block.searchEndIndex = 0
		} else {
			block.searchMatchIndex = 0
			block.searchEndIndex = originalContent.length
		}
		return
	}

	// Exact search match scenario
	const exactIndex = originalContent.indexOf(block.search, 0)
	if (exactIndex !== -1) {
		block.searchMatchIndex = exactIndex
		block.searchEndIndex = exactIndex + block.search.length
		return
	}

	// Attempt fallback line-trimmed matching
	const lineMatch = lineTrimmedFallbackMatch(originalContent, block.search, 0)
	if (lineMatch) {
		[block.searchMatchIndex, block.searchEndIndex] = lineMatch
		return
	}

	// Try block anchor fallback for larger blocks
	const blockMatch = blockAnchorFallbackMatch(originalContent, block.search, 0)
	if (blockMatch) {
		[block.searchMatchIndex, block.searchEndIndex] = blockMatch
    } else {
        throw new Error(
            `The SEARCH block:\n${block.search.trimEnd()}\n...does not match anything in the file.- Tip: Use the read_file tool to get the latest content of the file before attempting to use the replace_in_file tool again, as the file content may have changed\n\n`
        )
    }
}
