import { getShell } from "../../utils/shell"
import os from "os"
import osName from "os-name"
import { McpHub } from "../../services/mcp/McpHub"
import i18nService from "../../i18n/i18n"
import { SYSTEM_PROMPT_ACT_PLAN_DIFF_8, SYSTEM_PROMPT_ACT_TOOL_DESC_2, SYSTEM_PROMPT_ACT_TOOL_EXAMPLE_5, SYSTEM_PROMPT_ACT_TOOL_GUIDELINES_7, SYSTEM_PROMPT_BEG_AND_TOOL_DESC_1, SYSTEM_PROMPT_TOOL_DESC_3, SYSTEM_PROMPT_TOOL_GUIDELINES_6, SYSTEM_PROMPT_CAPABILITIES_9, SYSTEM_PROMPT_PLAN_TOOL_DESC_4, SYSTEM_PROMPT_TOOL_EXAMPLE_42, SYSTEM_PROMPT_WHAT_PLAN_81 } from "./prompt-temp"

export const SYSTEM_PROMPT = async (
	cwd: string,
	supportsComputerUse: boolean,
	isPlanMode: boolean
) => `${isPlanMode ? await SYSTEM_PROMPT_PLAN(cwd, supportsComputerUse, isPlanMode) : await SYSTEM_PROMPT_ACT(cwd, supportsComputerUse, isPlanMode)}`

const SYSTEM_PROMPT_PLAN = async (
	cwd: string,
	supportsComputerUse: boolean,
	isPlanMode: boolean) =>
	`${await SYSTEM_PROMPT_BEG_AND_TOOL_DESC_1(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_TOOL_DESC_3(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_PLAN_TOOL_DESC_4(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_TOOL_EXAMPLE_42(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_TOOL_GUIDELINES_6(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_ACT_PLAN_DIFF_8(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_WHAT_PLAN_81(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_CAPABILITIES_9(cwd, supportsComputerUse, !isPlanMode)}
`

const SYSTEM_PROMPT_ACT = async (
	cwd: string,
	supportsComputerUse: boolean,
	isPlanMode: boolean) =>
	`${await SYSTEM_PROMPT_BEG_AND_TOOL_DESC_1(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_ACT_TOOL_DESC_2(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_TOOL_DESC_3(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_TOOL_EXAMPLE_42(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_ACT_TOOL_EXAMPLE_5(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_TOOL_GUIDELINES_6(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_ACT_TOOL_GUIDELINES_7(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_ACT_PLAN_DIFF_8(cwd, supportsComputerUse)}
 ${await SYSTEM_PROMPT_CAPABILITIES_9(cwd, supportsComputerUse, !isPlanMode)}
`

export function addUserInstructions(settingsCustomInstructions?: string, clineRulesFileInstructions?: string) {
	let customInstructions = ""
	if (settingsCustomInstructions) {
		customInstructions += settingsCustomInstructions + "\n\n"
	}
	if (clineRulesFileInstructions) {
		customInstructions += clineRulesFileInstructions
	}

	return `
====

USER'S CUSTOM INSTRUCTIONS

The following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.

${customInstructions.trim()}`
}
