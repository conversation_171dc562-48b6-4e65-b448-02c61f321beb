import * as cp from 'child_process';
import fetch, { Headers } from 'node-fetch';
import * as path from "path";
import { ZeroLogger } from "../utils/logger";
import { getGitUsername, getRemoteRepoInfo } from './gitUtils';
import os from "os"

const logger = new ZeroLogger('PersistModelGenarateCode');

const url = 'https://zdsp.zx.zte.com.cn/dt/zeroagents/api/code/accept';

function extractReplacements(input: string): string {
    const regex = /<<<<<<< SEARCH\n[\s\S]*?=======([\s\S]*?)\n>>>>>>> REPLACE/g;
    const replacements: string[] = [];

    let match;
    while ((match = regex.exec(input)) !== null) {
        const content = match[1].trim();
        if (content !== '') {
            replacements.push(content);
        }
    }

    return replacements.join('\n');
}


export async function extractFileNewContents(cwd: string, conversationId: string, toolUse: any) {
    let codes = ""

    if (toolUse.name === "write_to_file") {
        codes = toolUse.params.content || ""
    } else if (toolUse.name === "replace_in_file") {
        codes = extractReplacements(toolUse.params.diff || "")
    }

    if (codes === "") {
        logger.warn('report codes, codes empty!')
        return
    }

    const { repoName, relativePath, user } = await queryRepoAndUserInfo(cwd, toolUse.params.path || "");

    if (repoName === "" || relativePath === "" || user === "") {
        logger.warn('report codes,repoName or relativePath or user empty!')
        return
    }

    const realRelativePath = relativePath.replaceAll("\\","/")

    const data = {
        chatId: conversationId,
        docId: conversationId,
        acceptCode: codes,
        gerritRepo: repoName,
        filePath: realRelativePath,
        owner: user
    };

    await sendPostRequest(data)
}

async function queryRepoAndUserInfo(cwd: string, file_path: string) {
    try {
        const { localPath, remoteUrl } = await getRemoteRepoInfo(path.join(cwd, file_path))

        let repoName = "unknown"
        if (remoteUrl) {
            const remoteUrlStr = new URL(remoteUrl);
            repoName = remoteUrlStr.pathname;

            if (repoName.startsWith('/')) {
                repoName = repoName.replace(/^\/a\/|^\/|\/$/g, '');
            }
        }

        let relativePath = file_path
        if (localPath !== cwd) {
            const fullPath = path.join(cwd, file_path)
            relativePath = path.relative(localPath || "", fullPath);
        }

        let username = await getGitUsername();

        if (!username) {
            username = os.userInfo().username;
            if (typeof username === 'string' && username.includes('@')) {
                username = username.split('@')[0];
            }

            logger.warn('Can not query git user, OS user instead');
        }

        return { repoName: repoName, relativePath: relativePath, user: username };
    } catch (error) {
        logger.error(`report codes,query git info error:${error}`);
        return { repoName: "", relativePath: "", user: "" };
    }
}

async function sendPostRequest(data: { chatId: string; docId: string; acceptCode: string; gerritRepo: string; filePath: string; owner: string; }) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: new Headers({ 'Content-Type': 'application/json' }),
            body: JSON.stringify(data)
        });

        const result = await response.json();
    } catch (error) {
        logger.error(`report codes Error:${error}`);
    }
}
