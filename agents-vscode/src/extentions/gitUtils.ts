import * as vscode from 'vscode';
import { ZeroLogger } from '../utils/logger';
import * as childProcess from 'child_process';
import * as os from 'os';
import * as path from 'path';
import * as fs from 'fs';
import * as ini from 'ini';

// 新增缓存相关变量
let cachedUsername = "";
const logger = new ZeroLogger('gitUtils');

/** 
 * 获取Git用户名的入口函数（带缓存）
 * @returns Promise<string | undefined> 用户名或undefined
 * @caches 永久缓存，直到获取到新值时更新
 */
export async function getGitUsername(): Promise<string> {
    if (cachedUsername) {
        return cachedUsername;
    }

    try {
        logger.info('Attempting to get username via config file or Git CLI');
        const username = await getUsernameViaConfigFile() || await getUsernameViaGitCli();
        if (username) {
            logger.info(`Username found: ${username}`);
            cachedUsername = username.trim();
        }
    } catch (error) {
        logger.error('Error getting Git username', error);
    }

    return cachedUsername;
}

/**
 * 获取仓库信息
 */
async function getUsernameViaGitCli(): Promise<string> {
    try {
        logger.info('Attempting to get username via Git CLI');
        // 尝试获取本地配置
        const workspaceUsername = await executeGitCommand(['config', 'user.name']);
        if (workspaceUsername) {
            logger.info(`Workspace username found: ${workspaceUsername}`);
            return workspaceUsername;
        }

        // 尝试获取全局配置
        const globalUsername = await executeGitCommand(['config', '--global', 'user.name']);
        if (globalUsername) {
            logger.info(`Global username found: ${globalUsername}`);
        }
        return globalUsername || "";
    } catch (error) {
        logger.error('Error getting username via Git CLI', error);
        return "";
    }
}

/**
 * 执行Git命令
 */
async function executeGitCommand(args: string[], cwd?: string): Promise<string> {
    return new Promise((resolve, reject) => {
        childProcess.execFile(
            'git',
            args,
            { cwd },
            (error, stdout) => {
                if (error) {
                    logger.error('execute command error:', error);
                    resolve('');
                }
                resolve(stdout.toString().trim() || '');
            }
        );
    });
}

/**
 * 通过解析配置文件获取用户名
 */
/**
 * 增强版：向上查找最多5层目录寻找.git/config
 */
function findGitRootDir(startPath: string, maxUpDepth = 5, maxDownDepth = 3): string {
    // 向上查找
    let currentPath = startPath;
    for (let i = 0; i <= maxUpDepth; i++) {
        const gitConfigPath = path.join(currentPath, '.git/config');
        if (fs.existsSync(gitConfigPath)) { return currentPath; }
        const parentPath = path.dirname(currentPath);
        if (parentPath === currentPath) { break; }
        currentPath = parentPath;
    }

    // 向下查找
    const downSearch = (dir: string, depth: number): string => {
        if (depth > maxDownDepth) { return ""; }

        const files = fs.readdirSync(dir);
        for (const file of files) {
            const fullPath = path.join(dir, file);
            if (fs.statSync(fullPath).isDirectory()) {
                const gitConfigPath = path.join(fullPath, '.git/config');
                if (fs.existsSync(gitConfigPath)) { return fullPath; }

                const result = downSearch(fullPath, depth + 1);
                if (result) { return result; }
            }
        }
        return "";
    };

    return downSearch(startPath, 0);
}

async function getUsernameViaConfigFile(): Promise<string> {
    logger.info('Attempting to get username via config file');
    const workspacePath = getWorkspacePath();
    if (workspacePath) {
        const gitRoot = findGitRootDir(workspacePath);
        if (gitRoot) {
            const localConfigPath = path.join(gitRoot, '.git/config');
            const localUsername = parseConfigFile(localConfigPath);
            if (localUsername) {
                logger.info(`Local username found: ${localUsername}`);
                return localUsername;
            }
        }
    }

    // 尝试全局配置
    const globalConfigPath = path.join(os.homedir(), '.gitconfig');
    const globalUsername = parseConfigFile(globalConfigPath);
    if (globalUsername) {
        logger.info(`Global username found: ${globalUsername}`);
    }
    return globalUsername;
}

/**
 * 解析Git配置文件
 */
function parseConfigFile(configPath: string): string {
    try {
        if (!fs.existsSync(configPath)) { return ""; }
        const content = fs.readFileSync(configPath, 'utf-8');
        return ini.parse(content).user?.name;
    } catch (error) {
        logger.error(`get remote repo info via config file error: ${error}`);
    }
    return "";
}

function getWorkspacePath(): string | undefined {
    return vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
}

// 新增获取远程仓库信息的方法
export async function getRemoteRepoInfo(filePath: string): Promise<{ localPath: string, remoteUrl: string }> {
    let configInfo = await getRemoteRepoInfoViaConfigFile(filePath);

    return configInfo.localPath && configInfo.remoteUrl ? configInfo : await getRemoteRepoInfoViaGitCli(filePath);
}

async function getRemoteRepoInfoViaConfigFile(filePath: string): Promise<{ localPath: string, remoteUrl: string }> {
    logger.info('Attempting to get remote repo info via config file.');

    try {
        const startPath = path.dirname(filePath);
        const gitRoot = findGitRootDir(startPath, 30, 0);
        if (!gitRoot) {
            return { localPath: "", remoteUrl: "" };
        }

        const content = fs.readFileSync(path.join(gitRoot, '.git/config'), 'utf-8');
        const config = ini.parse(content);
        const remoteUrl = config['remote "origin"']?.url || '';


        logger.info(`Remote repo info found: repoPath=${gitRoot}, remoteUrl=${remoteUrl}`);
        return { localPath: gitRoot, remoteUrl: remoteUrl };
    } catch (error) {
        logger.error(`get remote repo info via config file error: ${error}`);
    }
    return { localPath: "", remoteUrl: "" };
}

async function getRemoteRepoInfoViaGitCli(filePath: string): Promise<{ localPath: string, remoteUrl: string }> {
    // 新增git命令备选方案
    logger.info('Attempting to get remote repo info via Git CLI');

    try {
        const fileDir = path.dirname(filePath);

        const rootDir = await executeGitCommand(['rev-parse', '--show-toplevel'], fileDir);
        const remoteUrlViaGit = await executeGitCommand(['remote', 'get-url', 'origin'], fileDir);
        if (rootDir && remoteUrlViaGit) {
            logger.info(`Remote repo info found: localPath=${rootDir}, remoteUrl=${remoteUrlViaGit}`);
            return { localPath: rootDir, remoteUrl: remoteUrlViaGit };
        }
    } catch (error) {
        logger.error(`get remote repo info via Git CLI error: ${error}`);
    }
    return { localPath: "", remoteUrl: "" };
}
