import { Anthropic } from "@anthropic-ai/sdk"
import * as fs from "fs"
import OpenAI from "openai"
import { convertToOpenAiMessages } from "../api/transform/openai-format";

export async function printPromptZte(systemPrompt: string, messages: Anthropic.Messages.MessageParam[], oldMessages: Anthropic.Messages.MessageParam[], workDir: string, taskId: string) {
	if (!fs.existsSync(`${workDir}/settings/needPrintPrompt`)) {
		return;
	}

	const finalPrompt = fs.existsSync(`${workDir}/settings/needFullSystemPrompt`) ? systemPrompt : '###systemPrompt###'

	const mList = [messages, oldMessages]

	for (const [index, m] of mList.entries()) {
		const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
			{ role: "system", content: finalPrompt },
			...convertToOpenAiMessages(m),
		]

		const fileName = formatTime(workDir, taskId, index);

		const formattedMessages = JSON.stringify(openAiMessages, null, 2);
		fs.writeFileSync(fileName, formattedMessages);
	}
}

function formatTime(workDir: string, taskId: string, index: number): string {
	const date = new Date()

	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');

	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');

	const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

	fs.mkdirSync(`${workDir}/prompts/${year}${month}${day}/${taskId}`, { recursive: true })

	return `${workDir}/prompts/${year}${month}${day}/${taskId}/${hours}${minutes}${seconds}${milliseconds}-${index}.json`;
}