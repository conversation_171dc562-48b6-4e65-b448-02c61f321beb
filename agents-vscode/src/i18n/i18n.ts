import * as vscode from 'vscode';
import en from "./en";
import zh from "./zh";
import {deepClone} from "../utils/tools";

export const LANG_TYPE = {
  ZH: 'zh-CN',
  EN: 'en-US',
}

/**
 * 国际化(i18n)管理类，使用单例模式实现全局语言管理
 */
class I18nService {
  private static instance: I18nService;
  private currentLanguage: string = LANG_TYPE.ZH;
  private translations: Record<string, Record<string, any>> = {
    [LANG_TYPE.ZH]: zh,
    [LANG_TYPE.EN]: en,
  };

  /**
   * 私有构造函数，防止直接实例化
   * @private
   */
  private constructor() {}

  /**
   * 获取 I18n 的单例实例
   * @returns {I18n} I18n 实例
   * @static
   */
  public static getInstance(): I18nService {
    if (!I18nService.instance) {
      I18nService.instance = new I18nService();
    }
    return I18nService.instance;
  }

  /**
   * 设置当前使用的语言
   * @param {Language} lang - 要设置的目标语言
   */
  public setLanguage(lang: string, provider: any) {
    this.currentLanguage = lang;
    setTimeout(() => {
      if (provider) {
        provider.postMessageToWebview({
          type: 'lang',
          text: lang,
        })
      }
    }, 1000);
    
  }

  /**
   * 获取指定键值的翻译文本
   * @param {string} key - 翻译键值
   * @param {Record<string, any>} [params] - 用于替换翻译文本中占位符的参数对象
   * @returns {string} 翻译后的文本，如果找不到对应翻译则返回键值本身
   * @example
   * // 不带参数的翻译
   * i18n.get('submit') // 返回: '提交'
   *
   * // 多层参数
   * i18n.get('first.second') // 返回: '第二个'
   * 
   * // 带参数的翻译
   * i18n.get('hello', { name: 'World' }) // 模板: '你好 {{name}}' -> 返回: '你好 World'
   */
  public get(key: string, params?: Record<string, any>): string {
    let translation = key;
    if (key.indexOf('.')) {
      const keys = key.split('.');
      let _translation = deepClone(this.translations[this.currentLanguage])
      keys.forEach(k => {
        _translation = _translation[k];
      });
      translation = _translation || key;
    } else {
      translation = this.translations[this.currentLanguage][key] || key;
    }
    if (params) {
      return this.interpolate(translation, params);
    }
    return translation;
  }

  /**
   * 替换文本中的参数占位符
   * @param {string} text - 包含占位符的文本
   * @param {Record<string, any>} params - 用于替换的参数对象
   * @returns {string} 替换后的文本
   * @private
   * @example
   * // text: '你好 {{name}}'
   * // params: { name: 'World' }
   * // 返回: '你好 World'
   */
  private interpolate(text: string, params: Record<string, any>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (_, key) => params[key] || '');
  }

  /**
   * 加载指定语言的翻译数据
   * @param {Language} lang - 要加载翻译的目标语言
   * @param {Record<string, any>} translations - 翻译键值对对象
   * @example
   * i18n.loadTranslations('zh-cn', {
   *   'submit': '提交',
   *   'cancel': '取消'
   * });
   */
  private loadTranslations(lang: string, translations: Record<string, any>) {
    this.translations[lang] = translations;
  }
}

let i18nService!: I18nService;
if (!i18nService) {
  i18nService = I18nService.getInstance();
}

export default i18nService;

export function onChangeLanguage(provider: any) {
  try {
    // 监听语言设置的变化
    vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration('employeeZero.language')) {
        const newLanguage = getCurrentLanguage();
        i18nService.setLanguage(newLanguage, provider);
        reloadWindow(newLanguage);
      }
    });
  } catch (error) {
    vscode.window.showErrorMessage(`Failed to set language: ${error}`);
  }
}

export function getCurrentLanguage() {
  const currentLanguage = vscode.workspace.getConfiguration('employeeZero')?.get<string>('language');
  if (!currentLanguage) {
    vscode.window.showErrorMessage(`Current language is: ${currentLanguage}`);
  }
  return currentLanguage || LANG_TYPE.ZH;
}

export function reloadWindow(language: string) {
  vscode.window.showInformationMessage(
    `Language switched to ${language}. Please restart VSCode to apply changes.`,
    '立即重启'
  ).then(selection => {
    if (selection === '立即重启') {
      vscode.commands.executeCommand('workbench.action.reloadWindow');
    }
  });
}