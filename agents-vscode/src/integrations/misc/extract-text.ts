import * as path from "path"
// @ts-ignore-next-line
import pdf from "pdf-parse/lib/pdf-parse"
import mammoth from "mammoth"
import fs from "fs/promises"
import { isBinaryFile } from "isbinaryfile"
import { parseFile } from "../../services/tree-sitter"
import { loadRequiredLanguageParsers } from "../../services/tree-sitter/languageParser"
import { ApiHandler } from '../../api';
import { OpenAiHandler } from '../../api/providers/openai';

export async function extractTextFromFile(filePath: string, api: ApiHandler): Promise<string> {
	try {
		await fs.access(filePath)
	} catch (error) {
		throw new Error(`File not found: ${filePath}`)
	}
	const fileExtension = path.extname(filePath).toLowerCase()
	let content:string = ""
	switch (fileExtension) {
		case ".pdf":
			content = await extractTextFromPDF(filePath)
			break
		case ".docx":
			content = await extractTextFromDOCX(filePath)
			break
		case ".ipynb":
			content = await extractTextFromIPYNB(filePath)
			break
		default:
			const isBinary = await isBinaryFile(filePath).catch(() => false)
			if (!isBinary) {
				content = await fs.readFile(filePath, "utf8")
			} else {
				throw new Error(`Cannot read text for file type: ${fileExtension}`)
			}
	}
	return maxTokenProcess(content, filePath, api)
}

async function extractTextFromPDF(filePath: string): Promise<string> {
	const dataBuffer = await fs.readFile(filePath)
	const data = await pdf(dataBuffer)
	return data.text
}

async function extractTextFromDOCX(filePath: string): Promise<string> {
	const result = await mammoth.extractRawText({ path: filePath })
	return result.value
}

async function extractTextFromIPYNB(filePath: string): Promise<string> {
	const data = await fs.readFile(filePath, "utf8")
	const notebook = JSON.parse(data)
	let extractedText = ""

	for (const cell of notebook.cells) {
		if ((cell.cell_type === "markdown" || cell.cell_type === "code") && cell.source) {
			extractedText += cell.source.join("\n") + "\n"
		}
	}

	return extractedText
}

async function maxTokenProcess(content: string, filePath: string, api: ApiHandler): Promise<string> {
    const newlineRegex = /\r\n|\r|\n/g;
    const newlineCount = (content.match(newlineRegex) || []).length;
    let maxLines = await getMaxLines(api);
    if (newlineCount < maxLines){
        return content;
    }
    const languageParsers = await loadRequiredLanguageParsers([filePath]);
    let definition = await parseFile(filePath, languageParsers);
    return definition ? definition : "";
}

export async function getMaxLines(api: ApiHandler): Promise<number> {
    let contextWindow = api.getModel().info.contextWindow || 128_000;
    if (api instanceof OpenAiHandler && api.getModel().id.toLowerCase().includes("deepseek")) {
        contextWindow = 64_000
    }
    let maxtokens = contextWindow - 10_000;
    let maxLines = maxtokens / 20;
    return maxLines;
}

export function addLineNumbers(content: string, startLine: number = 1): string {
	const lines = content.split("\n")
	const maxLineNumberWidth = String(startLine + lines.length - 1).length
	return lines
		.map((line, index) => {
			const lineNumber = String(startLine + index).padStart(maxLineNumberWidth, " ")
			return `${lineNumber} | ${line}`
		})
		.join("\n")
}