import * as vscode from 'vscode';
import { <PERSON><PERSON><PERSON>and<PERSON> } from '../../api';
import { OpenAiHandler } from '../../api/providers/openai';
import { parseFile } from "../tree-sitter"
import { loadRequiredLanguageParsers } from "../tree-sitter/languageParser"
import { getMaxLines } from "../../integrations/misc/extract-text"

export class CodeDefinitionRetriever {
    private api: ApiHandler
    constructor(api: ApiHandler) {
        this.api = api;
    }

    /* Started by AICoder, pid:v6067p5d16f34f314b060afc10784d628508f912 */
    public async getCodeDefinition(
        filePath: string = "",
        className: string = "",
        functionName: string = ""
    ): Promise<string> {
        if (!className && !functionName) {
            return "class_name and function_name cannot both be empty.";
        }
        if (!functionName && className) {
            return await this.getClassDefinition(className, filePath);
        }
        const symbols = await vscode.commands.executeCommand<vscode.SymbolInformation[]>(
            'vscode.executeWorkspaceSymbolProvider',
            functionName
        );
        let results: string = "";
        if (!symbols || symbols.length === 0) {
            results += `未找到函数 "${functionName}" 的符号信息`;
            return results;
        }
        for (const symbol of symbols) {
            if (
                (symbol.kind === vscode.SymbolKind.Function || symbol.kind === vscode.SymbolKind.Method) &&
                (!className || symbol.containerName === className)
            ) {
                const location = symbol.location;
                if (filePath && location.uri.fsPath !== filePath) {
                    continue;
                }
                results = await this.getSymbolItem(functionName, location);
            }
        }
        if (results.length === 0) {
            results += `未找到匹配的符号定义: ${functionName}\n`;
        }
        return results;
    }

    private async getClassDefinition(className: string, filePath: string): Promise<string> {
        let results: string = "";
        const symbols = await vscode.commands.executeCommand<vscode.SymbolInformation[]>(
            'vscode.executeWorkspaceSymbolProvider',
            className
        );

        if (!symbols || symbols.length === 0) {
            results += `未找到类 "${className}" 的符号信息`;
            return results;
        }
        for (const symbol of symbols) {
            if (symbol.kind === vscode.SymbolKind.Class) {
                const location = symbol.location;
                if (filePath && location.uri.fsPath !== filePath) {
                    continue;
                }
                results = await this.getSymbolItem(className, location);
            }
        }
        if (results.length === 0) {
            results += `未找到匹配的类定义: ${className}\n`;
        }
        return results;
    }

    private async getSymbolItem(symbol: string, location: vscode.Location): Promise<string> {
        let results: string = "";
        results += `filePath: ${location.uri.fsPath}\n`;
        let maxLines = await getMaxLines(this.api);
        try {
            const document = await vscode.workspace.openTextDocument(location.uri);
            const startLine = location.range.start.line + 1;
            const endLine = location.range.end.line + 1;
            const symbolLines = endLine - startLine;
            if (symbolLines > maxLines) {
                const languageParsers = await loadRequiredLanguageParsers([location.uri.fsPath]);
                let definition = await parseFile(location.uri.fsPath, languageParsers);
                results += `code:\n${definition}\n\n`;
                results += `notification: The definition of ${symbol} exceeds ${maxLines} lines. We are unable to provide its specific content and can only return its signature. Please do not attempt to retrieve its content again.`;
                return results;
            }
            else {
                const codeSnippet = document.getText(location.range);
                results += `start_line: ${startLine}\n`;
                results += `end_line: ${endLine}\n`;
                results += `code:\n${codeSnippet}\n\n`;
                results += `notification: In this task, do not use the read_code_item tool to obtain the symbol (${symbol}) anymore.`;
            }
        } catch (error) {
            console.error(`打开文件失败: ${location.uri.fsPath}`, error);
            results += `打开文件失败: ${location.uri.fsPath}\n`;
        }
        return results
    }
    /* Ended by AICoder, pid:v6067p5d16f34f314b060afc10784d628508f912 */
}