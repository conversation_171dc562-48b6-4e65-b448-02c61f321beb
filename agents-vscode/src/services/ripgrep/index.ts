import * as vscode from "vscode"
import * as childProcess from "child_process"
import * as path from "path"
import * as readline from "readline"
import { fileExistsAtPath } from "../../utils/fs"
import { loadRequiredLanguageParsers, LanguageParser } from "../tree-sitter/languageParser"
/*
This file provides functionality to perform regex searches on files using ripgrep.
Inspired by: https://github.com/DiscreteTom/vscode-ripgrep-utils

Key components:
1. getBinPath: Locates the ripgrep binary within the VSCode installation.
2. execRipgrep: Executes the ripgrep command and returns the output.
3. regexSearchFiles: The main function that performs regex searches on files.
   - Parameters:
     * cwd: The current working directory (for relative path calculation)
     * directoryPath: The directory to search in
     * regex: The regular expression to search for (Rust regex syntax)
     * filePattern: Optional glob pattern to filter files (default: '*')
   - Returns: A formatted string containing search results with context

The search results include:
- Relative file paths
- 2 lines of context before and after each match
- Matches formatted with pipe characters for easy reading

Usage example:
const results = await regexSearchFiles('/path/to/cwd', '/path/to/search', 'TODO:', '*.ts');

rel/path/to/app.ts
│----
│function processData(data: any) {
│  // Some processing logic here
│  // TODO: Implement error handling
│  return processedData;
│}
│----

rel/path/to/helper.ts
│----
│  let result = 0;
│  for (let i = 0; i < input; i++) {
│    // TODO: Optimize this function for performance
│    result += Math.pow(i, 2);
│  }
│----
*/

const isWindows = /^win/.test(process.platform)
const binName = isWindows ? "rg.exe" : "rg"

interface SearchFileResult {
    file: string
    searchResults: SearchResult[]
}

interface SearchResult {
    lines: SearchLineResult[]
}

interface SearchLineResult {
    line: number
    text: string
    isMatch: boolean
    column?: number
}
// Constants
const MAX_RESULTS = 20
const MAX_LINE_LENGTH = 500

/**
 * Truncates a line if it exceeds the maximum length
 * @param line The line to truncate
 * @param maxLength The maximum allowed length (defaults to MAX_LINE_LENGTH)
 * @returns The truncated line, or the original line if it's shorter than maxLength
 */
export function truncateLine(line: string, maxLength: number = MAX_LINE_LENGTH): string {
    return line.length > maxLength ? line.substring(0, maxLength) + " [truncated...]" : line
}
/**
 * Get the path to the ripgrep binary within the VSCode installation
 */
export async function getBinPath(vscodeAppRoot: string): Promise<string | undefined> {
    const checkPath = async (pkgFolder: string) => {
        const fullPath = path.join(vscodeAppRoot, pkgFolder, binName)
        return (await fileExistsAtPath(fullPath)) ? fullPath : undefined
    }

    return (
        (await checkPath("node_modules/@vscode/ripgrep/bin/")) ||
        (await checkPath("node_modules/vscode-ripgrep/bin")) ||
        (await checkPath("node_modules.asar.unpacked/vscode-ripgrep/bin/")) ||
        (await checkPath("node_modules.asar.unpacked/@vscode/ripgrep/bin/"))
    )
}

async function execRipgrep(bin: string, args: string[]): Promise<string> {
    return new Promise((resolve, reject) => {
        const rgProcess = childProcess.spawn(bin, args)
        // cross-platform alternative to head, which is ripgrep author's recommendation for limiting output.
        const rl = readline.createInterface({
            input: rgProcess.stdout,
            crlfDelay: Infinity, // treat \r\n as a single line break even if it's split across chunks. This ensures consistent behavior across different operating systems.
        })

        let output = ""
        let lineCount = 0
        const maxLines = MAX_RESULTS * 5 // limiting ripgrep output with max lines since there's no other way to limit results. it's okay that we're outputting as json, since we're parsing it line by line and ignore anything that's not part of a match. This assumes each result is at most 5 lines.

        rl.on("line", (line) => {
            if (lineCount < maxLines) {
                output += line + "\n"
                lineCount++
            } else {
                rl.close()
                rgProcess.kill()
            }
        })

        let errorOutput = ""
        rgProcess.stderr.on("data", (data) => {
            errorOutput += data.toString()
        })
        rl.on("close", () => {
            if (errorOutput) {
                reject(new Error(`ripgrep process error: ${errorOutput}`))
            } else {
                resolve(output)
            }
        })
        rgProcess.on("error", (error) => {
            reject(new Error(`ripgrep process error: ${error.message}`))
        })
    })
}

export async function regexSearchFiles(
    cwd: string,
    directoryPath: string,
    regex: string,
    filePattern?: string,
): Promise<string> {
    // Detect and extract file extensions - only match common code file extensions
    // List of common code file extensions to detect
    const codeExtensions = [
        'js', 'ts', 'jsx', 'tsx', 'py', 'java', 'c', 'cpp', 'h', 'hpp', 'cs', 'go', 'rb', 'php',
        'swift'
    ];
    // Create a pattern that only matches these specific extensions
    const extensionPattern = new RegExp(`\\.(${codeExtensions.join('|')})\\b`, 'g');
    const matches = regex.match(extensionPattern)


    if (matches) {
        let cleanRegex = regex;
        for (const match of matches) {
            const escapeIndex = cleanRegex.lastIndexOf('\\', cleanRegex.lastIndexOf(match));
            const isEscaped = escapeIndex === cleanRegex.lastIndexOf(match) - 1;

            if (isEscaped) {
                cleanRegex = cleanRegex.replace('\\' + match, '');
            } else {
                cleanRegex = cleanRegex.replace(match, '');
            }
        }

        // Process filePattern
        const extensions = matches.map(ext => `*${ext}`)
        const newFilePattern = filePattern
            ? `{${filePattern},${extensions.join(',')}}`
            : extensions.length === 1
                ? extensions[0]
                : `{${extensions.join(',')}}`

        // Log the values for debugging
        console.log('[RIPGREP-DEBUG] File extension extraction:', {
            originalRegex: regex,
            cleanRegex: cleanRegex,
            matches: matches,
            newFilePattern: newFilePattern
        });

        // Continue search with processed parameters
        return regexSearchFiles(cwd, directoryPath, cleanRegex, newFilePattern)
    }

    const vscodeAppRoot = vscode.env.appRoot
    const rgPath = await getBinPath(vscodeAppRoot)

    if (!rgPath) {
        throw new Error("Could not find ripgrep binary")
    }

    const args = ["--json", "-e", regex, "--glob", filePattern || "*", "--context", "1", directoryPath]

    let output: string
    try {
        output = await execRipgrep(rgPath, args)
    } catch (error) {
        console.error("Error executing ripgrep:", error)
        return "No results found"
    }

    const results: SearchFileResult[] = []
    let currentFile: SearchFileResult | null = null

    output.split("\n").forEach((line) => {
        if (line) {
            try {
                const parsed = JSON.parse(line)
                if (parsed.type === "begin") {
                    currentFile = {
                        file: parsed.data.path.text.toString(),
                        searchResults: [],
                    }
                } else if (parsed.type === "end") {
                    // Reset the current result when a new file is encountered
                    results.push(currentFile as SearchFileResult)
                    currentFile = null
                } else if ((parsed.type === "match" || parsed.type === "context") && currentFile) {
                    const line = {
                        line: parsed.data.line_number,
                        text: truncateLine(parsed.data.lines.text),
                        isMatch: parsed.type === "match",
                        ...(parsed.type === "match" && { column: parsed.data.absolute_offset }),
                    }

                    const lastResult = currentFile.searchResults[currentFile.searchResults.length - 1]
                    if (lastResult?.lines.length > 0) {
                        const lastLine = lastResult.lines[lastResult.lines.length - 1]

                        // If this line is contiguous with the last result, add to it
                        if (parsed.data.line_number <= lastLine.line + 1) {
                            lastResult.lines.push(line)
                        } else {
                            // Otherwise create a new result
                            currentFile.searchResults.push({
                                lines: [line],
                            })
                        }
                    } else {
                        // First line in file
                        currentFile.searchResults.push({
                            lines: [line],
                        })
                    }
                }
            } catch (error) {
                console.error("Error parsing ripgrep output:", error)
            }
        }
    })

    console.log('[RIPGREP-DEBUG] Search results:', {
        totalFiles: results.length,
        totalSnippets: results.reduce((sum, file) => sum + file.searchResults.length, 0)
    });

    await sortResultsByDefinitions(results);
    await sortFilesByDefinitions(results);

    return formatResults(results, cwd)
}

// Definition type enum
enum DefinitionType {
    NONE = 0,       // Non-definition (lowest priority)
    OTHER = 1,      // Other definition
    FUNCTION = 2,   // Function definition
    CLASS = 3       // Class definition (highest priority)
}

// Cache for parsed results and parsers
const parsedResults = new Map<string, Map<string, DefinitionType>>();
let languageParsers: LanguageParser | null = null;

/**
 * Sort search results by definition type with priority: class definition > function definition > other definition > non-definition
 * @param fileResults Array of search results to be sorted
 */
async function sortResultsByDefinitions(fileResults: SearchFileResult[]): Promise<void> {
    for (const fileResult of fileResults) {
        const definitionMap = new Map<SearchResult, DefinitionType>();

        for (const result of fileResult.searchResults) {
            try {
                const defType = await isDefinition(result, fileResult.file);
                definitionMap.set(result, defType);
            } catch (error) {
                console.error(`Error checking definition: ${error}`);
                definitionMap.set(result, DefinitionType.NONE);
            }
        }

        fileResult.searchResults.sort((a, b) => {
            const aDefType = definitionMap.get(a) ?? DefinitionType.NONE;
            const bDefType = definitionMap.get(b) ?? DefinitionType.NONE;
            return bDefType - aDefType;
        });
    }
}

/**
 * Check if a search result contains a class or function definition using tree-sitter
 * @param result The search result to check
 * @param filePath The file path
 * @returns DefinitionType indicating the type of definition found
 */
async function isDefinition(result: SearchResult, filePath: string): Promise<DefinitionType> {
    try {
        const resultKey = result.lines.length > 0 ?
            `${result.lines[0].text}-${result.lines[result.lines.length - 1].text}` : '';

        if (parsedResults.has(filePath)) {
            const fileResults = parsedResults.get(filePath);
            if (fileResults && fileResults.has(resultKey)) {
                const cachedType = fileResults.get(resultKey) ?? DefinitionType.NONE;
                return cachedType;
            }
        }

        if (!languageParsers) {
            languageParsers = await loadRequiredLanguageParsers([filePath]);
        }

        const ext = path.extname(filePath).toLowerCase().slice(1);
        const { parser, query } = languageParsers[ext] || {};

        if (!parser || !query) {
            return DefinitionType.NONE;
        }

        // Extract code snippet from search result
        const codeSnippet = result.lines.map(line => line.text).join('\n');

        // Parse the code snippet
        const tree = parser.parse(codeSnippet);

        // Apply query and get captures
        const captures = query.captures(tree.rootNode);

        // Filter matching captures
        const matchingCaptures = captures.filter(capture =>
            capture.name.includes('definition') ||
            (capture.name.includes('name') && !capture.name.includes('reference'))
        );

        // Determine the definition type
        let definitionType = DefinitionType.NONE;
        if (matchingCaptures.length > 0) {
            // Check if there's a class definition
            const hasClassDefinition = matchingCaptures.some(capture =>
                capture.name.includes('class') ||
                capture.name.includes('interface') ||
                capture.name.includes('type')
            );

            // Check if there's a function definition
            const hasFunctionDefinition = matchingCaptures.some(capture =>
                capture.name.includes('function') ||
                capture.name.includes('method')
            );

            // Set the definition type based on priority
            if (hasClassDefinition) {
                definitionType = DefinitionType.CLASS;
            } else if (hasFunctionDefinition) {
                definitionType = DefinitionType.FUNCTION;
            } else {
                definitionType = DefinitionType.OTHER;
            }
        }

        // Cache the result
        if (!parsedResults.has(filePath)) {
            parsedResults.set(filePath, new Map());
        }
        parsedResults.get(filePath)?.set(resultKey, definitionType);

        return definitionType;
    } catch (error) {
        console.error(`Error using tree-sitter to check definition: ${error}`);
        return DefinitionType.NONE;
    }
}

/**
 * Sort file results based on whether they contain definitions, with priority:
 * files with class definitions > files with function definitions > files with other definitions > files without definitions
 * @param fileResults Array of file results to be sorted
 */
async function sortFilesByDefinitions(fileResults: SearchFileResult[]): Promise<void> {
    const fileDefinitionMap = new Map<SearchFileResult, DefinitionType>();

    for (const fileResult of fileResults) {
        const definitionTypes = fileResult.searchResults.map(result => {
            const resultKey = result.lines.length > 0 ?
                `${result.lines[0].text}-${result.lines[result.lines.length - 1].text}` : '';

            // Check if the definition type for this result is already in the cache
            if (parsedResults.has(fileResult.file)) {
                const fileResults = parsedResults.get(fileResult.file);
                if (fileResults && fileResults.has(resultKey)) {
                    return fileResults.get(resultKey) ?? DefinitionType.NONE;
                }
            }

            return DefinitionType.NONE;
        });

        const highestPriorityType = definitionTypes.reduce(
            (highest, current) => current > highest ? current : highest,
            DefinitionType.NONE
        );

        fileDefinitionMap.set(fileResult, highestPriorityType);
    }

    fileResults.sort((a, b) => {
        const aDefType = fileDefinitionMap.get(a) ?? DefinitionType.NONE;
        const bDefType = fileDefinitionMap.get(b) ?? DefinitionType.NONE;
        return bDefType - aDefType;
    });
}

function formatResults(fileResults: SearchFileResult[], cwd: string): string {
    let totalResults = fileResults.reduce((sum, file) => sum + file.searchResults.length, 0)
    let output = ""

    if (totalResults >= MAX_RESULTS) {
        output += `Showing first ${MAX_RESULTS} of ${MAX_RESULTS}+ results. Use a more specific search if necessary.\n\n`
    } else {
        output += `Found ${totalResults === 1 ? "1 result" : `${totalResults.toLocaleString()} results`}.\n\n`
    }

    const limitedResults = fileResults.slice(0, MAX_RESULTS);

    for (const file of limitedResults) {
        const relativeFilePath = path.relative(cwd, file.file);
        output += `# ${relativeFilePath}\n`;

        file.searchResults.forEach((result) => {
            if (result.lines.length > 0) {
                result.lines.forEach((line) => {
                    output += `${line.text.trimEnd()}\n`;
                });
                output += "----\n";
            }
        });

        output += "\n";
    }

    return output.trim();
}
