export { default as phpQuery } from "./php"
export { default as typescriptQuery } from "./typescript"
export { default as pythonQuery } from "./python"
export { default as javascriptQuery } from "./javascript"
export { default as javaQuery } from "./java"
export { default as rustQuery } from "./rust"
export { default as rubyQuery } from "./ruby"
export { default as cppQuery } from "./cpp"
export { default as cQuery } from "./c"
export { default as csharpQuery } from "./c-sharp"
export { default as goQuery } from "./go"
export { default as swiftQuery } from "./swift"
