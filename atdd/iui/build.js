const fs = require('fs');
const tar = require('tar');
const path = require('path');

const sourceDir = 'dist'; // 源目录
const targetFile = 'atdd-iui.tar.gz'; // 打包后的目标文件名

// 检查源目录是否存在
if (!fs.existsSync(sourceDir)) {
  console.error(`源目录 ${sourceDir} 不存在`);
  process.exit(1);
}

// 打包目录为 tar.gz 文件，但不包含 dist 这一层级
tar.create(
  {
    gzip: true, // 启用 gzip 压缩
    cwd: sourceDir, // 设置工作目录为 dist 目录
    portable: true, // 生成可移植的 tar 文件
  },
  fs.readdirSync(sourceDir) // 读取 dist 目录下的所有文件和文件夹
)
  .pipe(fs.createWriteStream(targetFile))
  .on('finish', () => {
    console.log(`打包完成，文件已保存为 ${targetFile}`);
  })
  .on('error', (err) => {
    console.error('打包失败:', err);
  });