.jenkins-wrapper {
  height: 100%;
  width: 100%;
  font-family: system-ui,"Segoe UI",roboto,"Noto Sans",oxygen,ubuntu,cantarell,"Fira Sans","Droid Sans","Helvetica Neue",arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
}

.jenkins-wrapper .jenkins-header {
  height: 56px;
  display: flex;
  align-items: center;
  padding: 8px 24px;
  background: #000;
}
.jenkins-wrapper .jenkins-header .jenkins-logo {
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  align-items: center;
  height: 100%;
}
.jenkins-wrapper .jenkins-header .jenkins-logo img {
  height: 100%;
}
.jenkins-wrapper .jenkins-header .jenkins-link {
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  flex-grow: 0;
  flex-shrink: 0;
}
.jenkins-wrapper .jenkins-header .jenkins-link > span {
  font-size: 14px;
  padding-left: 8px;
}

.jenkins-wrapper .jenkins-content {
  padding: 24px;
  height: calc(100% - 56px);
  overflow: auto;
}

.jenkins-wrapper .jenkins-content .jenkins-title {
  color: #000;
  font-size: 24px;
  font-weight: 650;
  padding: 0 0 24px;
  display: flex;
  align-items: center;
}

.jenkins-wrapper .jenkins-content .jenkins-title img {
  width: 30px;
  height: 30px;
  margin-right: 16px;
}

.jenkins-wrapper .jenkins-content .jenkins-title .executing {
  position: relative;
  width: 28px;
  height: 28px;
  margin-right: 16px;
}
.jenkins-wrapper .jenkins-content .jenkins-title .executing > div:first-child {
  position: relative;
  height: 100%;
  width: 100%;
  border-radius: 50%;
  border: 2px solid rgb(115, 115, 140, 0.4);
  animation: executing 2s infinite linear;
}
.jenkins-wrapper .jenkins-content .jenkins-title .executing > div:first-child::before {
  position: absolute;
  content: '';
  display: block;
  height: 14px;
  width: 14px;
  top: -2px;
  left: -2px;
  margin-right: 16px;
  border-top: 2px solid #73738c;
  border-left: 2px solid #73738c;
  border-top-left-radius: 100%;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.jenkins-wrapper .jenkins-content .jenkins-title .executing > div:last-child {
  width: 13px;
  transform: rotate(-45deg);
  border-top: 2px solid #73738c;
  position: absolute;
  top: 13px;
  left: 7px;
}

.jenkins-wrapper .jenkins-content .jenkins-output {
  white-space: pre-wrap;
  background: rgba(0, 0, 0, .05);
  padding: 16px;
  font-family: ui-monospace,sfmono-regular,sf mono,jetbrainsmono,consolas,monospace;
  font-size: 12px;
  line-height: 1.5;
}

.lds-ellipsis {
  display: inline-block;
  height: 2.5rem;
  position: relative;
  width: 12rem;
}

.lds-ellipsis div:first-child {
  animation: lds-ellipsis1 .6s infinite;
  left: 1rem;
}

.lds-ellipsis div {
  animation-timing-function: cubic-bezier(0,1,1,0);
  background: #73738c;
  border-radius: 50%;
  height: .5rem;
  position: absolute;
  top: 1rem;
  width: .5rem;
}

.lds-ellipsis div:nth-child(2) {
    animation: lds-ellipsis2 .6s infinite;
    left: 1rem;
}

.lds-ellipsis div:nth-child(3) {
    animation: lds-ellipsis2 .6s infinite;
    left: 4rem;
}

@keyframes lds-ellipsis1 {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes lds-ellipsis2 {
  0% {
    transform: translate(0);
  }

  100% {
    transform: translate(3rem);
  }
}

@keyframes executing {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}