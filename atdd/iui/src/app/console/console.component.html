<div class="jenkins-wrapper">
  <div class="jenkins-header">
    <div class="jenkins-logo">
      <img src="jenkinsLogo.svg" />
      <img src="jenkinsTitle.svg" />
    </div>
    <a class="plx-ico-multi-window-16 jenkins-link" *ngIf="link" [href]="link" target="_blank"><span>从 Jenkins 打开</span></a>
  </div>
  <div class="jenkins-content">
    <h1 class="jenkins-title">
      <ng-container *ngIf="showLoading" [ngTemplateOutlet]="executingTmpl"></ng-container>
      <ng-container *ngIf="!showLoading">
        <img *ngIf="!success" src="jenkinsError.svg" />
        <img *ngIf="success" src="jenkinsSuccess.svg" />
      </ng-container>
      Console Output
    </h1>
    <div #jenkinsOutput class="jenkins-output"></div>
    <div *ngIf="showLoading" class="lds-ellipsis"><div></div><div></div><div></div></div>
  </div>
</div>

<!-- 执行中 -->
<ng-template #executingTmpl>
  <div class="executing">
    <div></div>
    <div></div>
  </div>
</ng-template>