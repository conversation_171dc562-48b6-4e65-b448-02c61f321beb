import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';

@Component({
  selector: 'jenkins-console',
  templateUrl: './console.component.html',
  styleUrls: ['./console.component.css']
})
export class JenkinsConsoleComponent implements OnChanges, OnInit {
  @ViewChild('jenkinsOutput', { static: true }) jenkinsOutput: ElementRef<HTMLDivElement>;

  @Input() data: string;
  @Input() link: string;

  showLoading: boolean = false;
  success: boolean = false;

  ngOnChanges(changes: SimpleChanges) {
    const { data } = changes;
    if (data && !data.isFirstChange()) {
      this.updateOutput();
    }
  }

  ngOnInit() {
    this.updateOutput();
  }

  private updateOutput() {
    const outputElement = this.jenkinsOutput?.nativeElement;
    if (outputElement) {
      outputElement.innerHTML = this.getPrettyData() ?? '';
    }
  }

  private getPrettyData(): string {
    if (typeof this.data !== 'string' || !this.data) {
      return '';
    }
    this.data = this.data.replace(/\r/g, '');

    let dataList: string[] = this.data.split('\n');
    dataList = dataList.map(data => {
      if (data.trim().startsWith('[Pipeline]')) {
        return `<span class="pipeline" style="color: #9A9999;">${data}</span>`;
      }

      if (data.trim().startsWith('Started by user')) {
        return data.replace(/Started by user/g, 'Started by user<a class="link" style="color: #006fe6;">') + '</a>';
      }

      const ipReg = /\b\S*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b /g;
      data = data.replace(ipReg, ip => `<a class="link" style="color: #006fe6;">${ip}</a>`);

      return data;
    })

    this.showLoading = !dataList.some(data => data.trim().startsWith('Finished:'));
    if (!this.showLoading) {
      this.success = dataList.some(data => data.trim().startsWith('Finished: SUCCESS'));
    }

    return dataList.join('\n');
  }

}