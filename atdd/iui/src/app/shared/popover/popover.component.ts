import { ApplicationRef, Component, ComponentFactoryResolver, ElementRef, Injector, NgZone, Renderer2, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { PlxPopoverManual, PlxPopoverManualConfig } from 'paletx/plx-popover';

@Component({
  selector: 'popover-slot',
  template: `<ng-template #popoverCont><ng-content></ng-content></ng-template>`
})
export class PopoverSlotComponent {
  @ViewChild('popoverCont', { static: true }) popoverCont: TemplateRef<any>;

  private popover: PlxPopoverManual;

  constructor(
    private injector: Injector,
    private viewContainerRef: ViewContainerRef,
    private renderer: Renderer2,
    private componentFactoryResolver: ComponentFactoryResolver,
    private ngZone: NgZone,
    private appRef: ApplicationRef,
    private elementRef: ElementRef
  ) {}

  open(host: HTMLElement) {
    const popover = new PlxPopoverManual(
      this.injector,
      this.viewContainerRef,
      this.renderer,
      this.componentFactoryResolver,
      this.ngZone,
      host
    );

    if (this.popover) {
      this.popover?.close(true);
    }
    this.popover = popover;
    this.popover.setApplicationRef(this.appRef);
    this.popover.open(this.popoverCont, this.getConfig());
  }

  close() {
    this.popover?.close(true);
  }

  private getConfig(): PlxPopoverManualConfig {
    return {
      popoverMaxHeight: '200px',
      showActiveWhiteSpace: true,
      placement: 'top',
      delay: 0,
      autoClose: true,
      container: this.elementRef.nativeElement
    };
  }
}