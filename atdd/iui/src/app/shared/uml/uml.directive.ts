import { Directive, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[uml-wrapper]',
  exportAs: 'uml-wrapper'
})
export class UMLWrapperDirective implements OnChanges, OnInit {
  @Input() data: string = '';

  @Output() render: EventEmitter<void> = new EventEmitter();

  ngOnChanges(changes: SimpleChanges) {
    const { data } = changes;

    if (data && !data.isFirstChange()) {
      this.renderPlantUml();
    }
  }

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    if (this.data) {
      this.renderPlantUml();
    }
  }

  renderPlantUml() {
    const container = this.elementRef.nativeElement;

    try {
      // 使用PlantUML在线服务渲染，使用HEX编码
      const encoded = this.encodePlantUml(this.data);
      // 使用~h前缀表示HEX编码
      const imageUrl = `https://zdsp.zx.zte.com.cn/ai/atdd_plantuml/svg/~h${encoded}`;
      
      // 创建图片元素
      const img = document.createElement('img');
      
      img.onload = () => {
        container.innerHTML = '';
        container.appendChild(img);
        this.render.emit();
      };
        
      img.onerror = () => {
        // 渲染失败，显示错误信息和源码
        container.innerHTML = `
          <div class="plantuml-error">
            图表渲染失败，可能是PlantUML语法错误或网络问题<br />
            <pre>${this.data}</pre>
          </div>
        `;
        this.render.emit();
      };
      
      img.style.maxWidth = '100%';
      img.style.maxHeight = '100%';
      img.style.display = 'block';
      img.style.margin = '0 auto';
      img.src = imageUrl;
      img.alt = 'PlantUML Diagram';
        
    } catch (error) {
      console.error('PlantUML渲染错误:', error);
      container.innerHTML = `
        <div class="plantuml-error">
            图表渲染失败: ${error.message}<br />
            <pre>${this.data}</pre>
        </div>
      `;
      this.render.emit();
    }
  }

  private encodePlantUml(plantUmlCode: string) {
    // 移除@startuml和@enduml标记
    let code = plantUmlCode.replace(/@startuml[\s\S]*?\n/, '').replace(/@enduml[\s\S]*$/, '');
    
    // 使用HEX编码（简单且可靠的方式）
    try {
      // 将字符串转换为UTF-8字节，然后转换为十六进制
      const utf8Bytes = new TextEncoder().encode(code);
      let hexString = '';
      for (let i = 0; i < utf8Bytes.length; i++) {
        hexString += utf8Bytes[i].toString(16).padStart(2, '0');
      }
      return hexString;
    } catch (e) {
      console.error('PlantUML编码失败:', e);
      // 如果编码失败，返回一个默认的简单图表
      return '407374617274756d6c0a6e6f7465202245e7bc96e7a081e5a4b1e8b4a522206173204e310a40656e64756d6c';
    }
  }
}