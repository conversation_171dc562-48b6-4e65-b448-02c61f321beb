.uml-editor-wrapper {
  display: flex;
  width: 100%;
  height: 240px;
  border: 1px solid #d9d9d9;
  padding: 0;
  overflow: hidden;
}
.uml-editor-wrapper .uml-preview {
  flex-basis: 70%;
  flex-grow: 0;
  flex-shrink: 0;
  height: 100%;
  position: relative;
  transition: flex-basis 0.8s ease;
  display: flex;
  padding: 8px 16px;
  flex-direction: column;
}
.uml-editor-wrapper.uml-preview-fullscreen .uml-preview {
  flex-basis: 100%;
}
.uml-editor-wrapper.uml-preview-fullscreen .uml-editor-split {
  display: none;
}
.uml-editor-wrapper.uml-preview-fullscreen .uml-editor {
  flex-basis: 0;
  overflow: hidden;
}
.uml-editor-wrapper .uml-preview .uml-preview-container {
  width: 100%;
  height: calc(100% - 32px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.uml-editor-wrapper .uml-editor {
  flex-shrink: 1;
  flex-grow: 1;
  height: 100%;
  padding: 8px 0;
}
.uml-editor-wrapper .uml-editor textarea#uml-editor-textarea {
  height: calc(100% + 16px);
  width: 100%;
  border: none;
  resize: none;
  overflow: auto;
  white-space: nowrap;
  color: #4d4d4d;
  padding: 8px 16px;
  margin: -8px 0;
}
.uml-editor-wrapper .uml-editor-split {
  flex-basis: 1px;
  flex-grow: 0;
  flex-shrink: 0;
  height: 100%;
  border-left: 1px solid #d9d9d9;
}
.uml-editor-wrapper .uml-preview plx-loading {
  position: absolute;
  display: block;
  width: 100%;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
}
.uml-editor-wrapper .uml-preview .uml-preview-operation {
  display: flex;
  justify-content: flex-end;
}
.uml-editor-wrapper .uml-preview .uml-preview-operation .plx-icon-btn {
  animation: none;
}
.uml-editor-wrapper .uml-preview .uml-preview-operation .plx-icon-btn:not(:last-child) {
  margin-right: 8px;
}