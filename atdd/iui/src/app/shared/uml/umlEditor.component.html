<div class="uml-editor-wrapper" [class.uml-preview-fullscreen]="!showSourceCode" [style.height]="height + 'px'">
  <div class="uml-preview">
    <div class="uml-preview-operation">
      <button class="plx-icon-btn plx-ico-refresh-16" [plxTooltip]="'刷新'" container="body" (click)="refresh()"></button>
      <button class="plx-icon-btn plx-ico-code-16" [plxTooltip]="'代码'" container="body" [class.selected]="showSourceCode" (click)="toggleShowSourceCode($event)"></button>
    </div>
    <plx-loading *ngIf="showLoading" [type]="'max'"></plx-loading>
    <div class="uml-preview-container" uml-wrapper #umlWrapper="uml-wrapper" [data]="umlText" (render)="showLoading = false"></div>
  </div>
  <div class="uml-editor-split"></div>
  <div class="uml-editor">
    <textarea id="uml-editor-textarea" [ngModel]="text" (ngModelChange)="onInputChange($event)"></textarea>
  </div>
</div>