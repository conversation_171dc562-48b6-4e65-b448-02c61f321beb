import { ChangeDetectorRef, Component, EventEmitter, forwardRef, Output, ViewChild } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { UMLWrapperDirective } from './uml.directive';

@Component({
  selector: 'uml-editor',
  templateUrl: './umlEditor.component.html',
  styleUrls: ['./umlEditor.component.css'],
  providers: [{
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => UMLEditorComponent),
      multi: true
    }]
})
export class UMLEditorComponent {
  @ViewChild('umlWrapper', { static: true }) umlWrapper: UMLWrapperDirective;
  @Output() editorHeightChange: EventEmitter<number> = new EventEmitter();

  text: string;
  umlText: string;
  height: number = 240;
  showLoading: boolean = true;
  showSourceCode: boolean = true;

  private renderTimer: number;
  private onChange: (_: string) => void = () => {};
  private onTouched: () => void = () => {};
  
  constructor(private cdr: ChangeDetectorRef) {}

  writeValue(value: string) {
    this.text = value;
    this.setEditorHeight(this.text);
    if (this.text) {
      this.umlText = this.text;
      this.showSourceCode = true;
    } else {
      this.showLoading = true;
      this.showSourceCode = false;
    }
  }

  registerOnChange(fn: (_: string) => void) {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void) {
    this.onTouched = fn;
  }

  onInputChange(value: string) {
    this.text = value;
    this.setEditorHeight(this.text);
    this.onChange(this.text);
    this.onTouched();

    if (this.renderTimer) {
      clearTimeout(this.renderTimer);
      this.renderTimer = null;
    }
    this.renderTimer = window.setTimeout(() => {
      this.umlText = this.text;
      this.showLoading = true;
      this.cdr.detectChanges();
    }, 400)
  }

  setEditorHeight(text: string) {
    const rows = text?.split('\n').length ?? 0;
    const rowsHeight = (rows + 1) * 18;
    this.height = Math.max(240, rowsHeight + 16);
    this.editorHeightChange.emit(this.height);
  }

  toggleShowSourceCode(e: MouseEvent) {
    (e.target as HTMLButtonElement).blur();
    this.showSourceCode = !this.showSourceCode;
  }

  refresh() {
    this.umlWrapper.renderPlantUml();
    this.showLoading = true;
  }
}