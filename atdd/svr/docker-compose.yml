version: '2'
services:
  postgres:
    image: public-docker-virtual.artnj.zte.com.cn/postgres:10.4-alpine  # 使用已加载的镜像
    container_name: cyc_postgres
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}   # 替换为实际密码,或在同目录.env文件中保存POSTGRES_PASSWORD=xxxxx
    volumes:
      - ./postgres_data:/var/lib/postgresql/data  # 挂载数据目录    - "宿主机路径:容器路径:权限"
    ports:
      - "5432:5432" 
    expose:
      - "5432/tcp"
    restart: always
    networks:
      - atdd-network

  elasticsearch:
    image: public-docker-virtual.artnj.zte.com.cn/elasticsearch:7.9.2  # 使用已加载的镜像
    container_name: cyc_elasticsearch
    environment:
       - discovery.type=single-node   # 单节点模式
       - ES_JAVA_OPTS=-Xms1g -Xmx1g  # JVM内存分配
       - network.host=0.0.0.0
    # volumes:
      # - ./es_data:/usr/share/elasticsearch/data  # 挂载数据目录    - "宿主机路径:容器路径:权限"
    ports:
      - "9200:9200"
      - "9300:9300"
    expose:
      - "9200/tcp"
      - "9300/tcp"
    ulimits:  # 解决文件描述符限制问题
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    restart: always
    networks:
      - atdd-network

  code-agent:
    image: code-agent-dev:v1  # 使用已加载的镜像
    container_name: code-agent
    environment: 
      - TZ=Asia/Shanghai 
      - PASSWORD=ps123 
      - PUID=0 
      - PGID=0 
      - DEFAULT_WORKSPACE=${PWD}/project 
    volumes:
      - ./atdd:/code/zero  # 挂载应用代码目录    - "宿主机路径:容器路径:权限"
    # working_dir: /atdd/svr
    # command: tail -f /dev/null  # 保持容器运行（按需修改）
    ports: 
      - "8443:8443" 
      - "9876:9876" 
    expose:
      - "8443/tcp"
      - "9876/tcp"
    restart: always 
    networks:
      - atdd-network
    depends_on:
      - postgres
      - elasticsearch
  
  python-atdd:
    image: public-docker-virtual.artnj.zte.com.cn/python_atdd:3.11.4  # 使用已加载的镜像
    container_name: python-atdd
    volumes:
      - ./atdd:/code/zero  # 挂载应用代码目录    - "宿主机路径:容器路径:权限"
    working_dir: /atdd/svr
    command: uvicorn app:app --host 0.0.0.0 --port 56898 --workers 4
    ports: 
      - "56898:56898" 
    expose:
      - "56898/tcp"
    restart: always 
    networks:
      - atdd-network
    depends_on:
      - postgres
      - elasticsearch
      - code-agent

networks:
  atdd-network:
    driver: bridge