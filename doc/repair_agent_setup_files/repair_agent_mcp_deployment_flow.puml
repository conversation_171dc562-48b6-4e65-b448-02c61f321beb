@startuml repair-agent-mcp-deployment-flow
!theme plain
skinparam backgroundColor white
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 12

' 设置不同组件的颜色
skinparam package {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #0D47A1
}

skinparam rectangle {
    BackgroundColor #F5F5F5
    BorderColor #757575
    FontColor #424242
}

skinparam note {
    BackgroundColor #FFF3E0
    BorderColor #FF9800
    FontColor #E65100
}

title Repair Agent MCP 2.0 部署流程图

' 外部用户层
package "外部用户层" as UserLayer {
    [VSCode用户] as User
}

' VSCode插件层
package "VSCode插件层" as VSCodeLayer {
    [VSCode Extension] as VSCodeExt
    [repair_agent_mcp.json] as MCPConfig
    [installCmd] as InstallCmd
}

' MCP服务层
package "MCP服务层" as MCPLayer {
    [MCP Server] as MCPServer
    [MCP Hub] as MCPHub
}

' 部署脚本层
package "部署脚本层" as DeployLayer {
    [deploy_repair_agent.py] as DeployScript
    [环境检查] as EnvCheck
    [uv安装] as UVInstall
    [依赖安装] as DepInstall
    [配置生成] as ConfigGen
}

' 系统层
package "系统层" as SystemLayer {
    [Python环境] as PythonEnv
    [虚拟环境] as VirtualEnv
    [配置文件] as ConfigFiles
    [MCP服务配置] as MCPServiceConfig
}

' 流程连接
User --> VSCodeExt : 1. 安装插件
VSCodeExt --> MCPConfig : 2. 读取配置
MCPConfig --> InstallCmd : 3. 获取安装命令
InstallCmd --> DeployScript : 4. 执行安装命令

DeployScript --> EnvCheck : 5. 检查环境
EnvCheck --> PythonEnv : 6. 验证Python版本
EnvCheck --> UVInstall : 7. 安装uv工具
UVInstall --> VirtualEnv : 8. 创建虚拟环境
VirtualEnv --> DepInstall : 9. 安装依赖包
DepInstall --> ConfigGen : 10. 生成配置文件
ConfigGen --> ConfigFiles : 11. 写入配置文件
ConfigGen --> MCPServiceConfig : 12. 配置MCP服务

MCPServiceConfig --> MCPHub : 13. 注册MCP服务
MCPHub --> MCPServer : 14. 启动MCP服务
MCPServer --> VSCodeExt : 15. 服务就绪通知

' 状态说明
note right of User : 用户通过VSCode\n安装Repair Agent插件
note right of DeployScript : 自动化部署脚本\n处理环境配置和依赖安装
note right of MCPServer : MCP服务启动后\n可接收代码修复请求

@enduml 