@startuml repair-agent-mcp-sequence
!theme plain
skinparam backgroundColor white
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 11

' 设置不同组件的颜色
skinparam participant {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #0D47A1
}

skinparam actor {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
    FontColor #4A148C
}

skinparam note {
    BackgroundColor #FFF3E0
    BorderColor #FF9800
    FontColor #E65100
}

title Repair Agent MCP 2.0 部署时序图

' 定义参与者
actor "外部用户" as User
participant "VSCode Extension" as VSCode
participant "repair_agent_mcp.json" as MCPConfig
participant "installCmd" as InstallCmd
participant "deploy_repair_agent.py" as DeployScript
participant "系统环境" as System
participant "MCP Hub" as MCPHub
participant "MCP Server" as MCPServer

' 时序流程
User -> VSCode : 1. 安装Repair Agent插件
activate VSCode

VSCode -> MCPConfig : 2. 读取MCP配置
activate MCPConfig
MCPConfig -> VSCode : 返回配置信息
deactivate MCPConfig

VSCode -> InstallCmd : 3. 获取安装命令
activate InstallCmd
note right : installCmd: "tar -zxvf repair-agent-mcp-2.0.tar.gz && cd repair-agent-mcp-2.0 && python repair_agent_setup_files/deploy_repair_agent.py --user-id '工号'"
InstallCmd -> VSCode : 返回安装命令
deactivate InstallCmd

VSCode -> DeployScript : 4. 执行安装命令
activate DeployScript

DeployScript -> System : 5. 检查环境
activate System
note right : 检查Python版本 >= 3.10\n检查pip和tar命令
System -> DeployScript : 环境检查结果
deactivate System

DeployScript -> System : 6. 安装uv工具
activate System
note right : 使用pip安装uv\n或检查uv是否已安装
System -> DeployScript : uv安装结果
deactivate System

DeployScript -> System : 7. 创建虚拟环境
activate System
note right : 使用uv venv创建\nPython虚拟环境
System -> DeployScript : 虚拟环境创建结果
deactivate System

DeployScript -> System : 8. 安装依赖包
activate System
note right : 在虚拟环境中\n安装requirements.txt中的依赖
System -> DeployScript : 依赖安装结果
deactivate System

DeployScript -> System : 9. 生成配置文件
activate System
note right : 创建.env文件\n配置API密钥等
System -> DeployScript : 配置文件生成结果
deactivate System

DeployScript -> System : 10. 配置MCP服务
activate System
note right : 生成MCP服务配置\n设置启动参数
System -> DeployScript : MCP服务配置结果
deactivate System

DeployScript -> VSCode : 11. 部署完成
deactivate DeployScript

VSCode -> MCPHub : 12. 注册MCP服务
activate MCPHub
note right : 注册repair-agent-mcp服务\n配置服务参数
MCPHub -> VSCode : 服务注册成功
deactivate MCPHub

VSCode -> MCPServer : 13. 启动MCP服务
activate MCPServer
note right : 启动repair_agent_cline_mcp_service.py\n监听代码修复请求
MCPServer -> VSCode : 服务启动成功
deactivate MCPServer

VSCode -> User : 14. 安装完成通知
note right : 显示安装成功消息\n提供使用说明
deactivate VSCode

' 错误处理流程
note over DeployScript, System : 如果任何步骤失败，会抛出异常并停止部署

' 服务运行状态
note over MCPServer : MCP服务持续运行\n等待代码修复请求

@enduml 