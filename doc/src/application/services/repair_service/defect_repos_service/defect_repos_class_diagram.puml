@startuml defect_repos_class_diagram
!theme plain
skinparam backgroundColor #FAFAFA
skinparam classBackgroundColor #E8F4FD
skinparam interfaceBackgroundColor #FFF2CC
skinparam packageBackgroundColor #F0F8E8

' 定义颜色
skinparam class {
    BorderColor #2E86AB
    ArrowColor #A23B72
    AttributeIconSize 0
}

package "缺陷代码库映射服务" {
    
    class DefectRepositoryMappingService {
        - ict_sid: str
        - ict_cid: str 
        - workspace_key: str
        - ict_handler: IcenterTool
        - ict_table_analysis: IctTableAnalysis
        - pg_handler: DefectReposPGHandler
        
        + __init__(user_id, user_pwd, ict_sid, ict_cid, pg_config...)
        - __init_ict_handler(user_id, user_pwd)
        - __init_pg_handler(pg_config...)
        - _extract_field_content(field_value): str
        - _check_defect_repos_mapping_datas(data_list)
        - _convert_icenter_table_data(icenter_data): List[Dict]
        + get_defect_repos_mapping_from_icenter_page(): List[Dict]
        + update_defect_repos_mapping_to_pg(mapping_data)
        + update_repos_from_icenter_page_main()
    }
    
    class "数据验证器" as Validator {
        <<utility>>
        + validate_required_fields(data, fields): bool
        + validate_data_format(data): bool
        + validate_uniqueness(data_list): bool
    }
    
    class "字段映射器" as FieldMapper {
        <<utility>>
        + FIELD_MAPPING: Dict[str, str]
        + map_chinese_to_english(chinese_data): Dict
        + extract_field_content(field_value): str
    }
}

package "icenter工具包" {
    class IcenterTool {
        <<external>>
        - user_id: str
        - user_pwd: str
        + page_read(sid, cid): Dict
        + authenticate(): bool
    }
    
    class IctTableAnalysis {
        <<external>>
        + search_table(html, headers): List[Dict]
        + parse_table_row(row_html): Dict
        + extract_table_data(table_html): List
    }
}

package "数据库处理包" {
    class DefectReposPGHandler {
        <<external>>
        - host: str
        - port: int
        - user: str
        - password: str
        - database: str
        + update_defect_repos_mapping_to_pg_main(workspace, data)
        + insert_mapping_record(record): bool
        + update_mapping_record(record): bool
        + check_record_exists(conditions): bool
    }
}

package "工具类" {
    class SimpleAEScryptor {
        <<utility>>
        + encrypt(text): str
        + decrypt(encrypted_text): str
    }
    
    class StringUtils {
        <<utility>>
        + calculate_checksum(input): str
        + value_pg: str
    }
    
    class Log4py {
        <<utility>>
        + __init__(log_path)
        + get_logger(): Logger
    }
}

' 依赖关系
DefectRepositoryMappingService --> IcenterTool : uses
DefectRepositoryMappingService --> IctTableAnalysis : uses
DefectRepositoryMappingService --> DefectReposPGHandler : uses
DefectRepositoryMappingService ..> Validator : validates data
DefectRepositoryMappingService ..> FieldMapper : maps fields

DefectRepositoryMappingService ..> SimpleAEScryptor : decrypts passwords
DefectRepositoryMappingService ..> StringUtils : calculates checksums
DefectRepositoryMappingService ..> Log4py : logs operations

note top of DefectRepositoryMappingService : 主服务类，负责整个业务流程：\n从icenter页面获取数据\n→ 数据验证和转换 \n→ 更新到PostgreSQL数据库

note right of IcenterTool : 负责icenter页面\n数据读取和认证

note right of DefectReposPGHandler : 负责PostgreSQL\n数据库操作

@enduml 