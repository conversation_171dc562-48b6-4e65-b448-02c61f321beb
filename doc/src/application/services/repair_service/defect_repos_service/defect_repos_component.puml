@startuml defect_repos_component
!theme plain
skinparam backgroundColor #FAFAFA

' 组件颜色定义
skinparam component {
    BackgroundColor #E8F4FD
    BorderColor #2E86AB
}

skinparam interface {
    BackgroundColor #FFF2CC
    BorderColor #F39C12
}

skinparam package {
    BackgroundColor #F0F8E8
    BorderColor #28A745
}

skinparam database {
    BackgroundColor #D4EDDA
    BorderColor #28A745
}

title 缺陷代码库映射服务 - 组件关系图

package "应用层 (Application Layer)" #LightBlue {
    
    package "服务入口" {
        component [命令行入口] as CLI #Wheat
        component [参数解析器] as ArgParser #Wheat
        component [配置管理器] as ConfigManager #Wheat
    }
    
    package "主服务" {
        component [DefectRepositoryMappingService] as MainService #LightCyan
    }
}

package "领域层 (Domain Layer)" #LightGreen {
    
    package "icenter工具包" {
        component [IcenterTool] as IcenterTool #MistyRose
        component [IctTableAnalysis] as TableAnalysis #MistyRose
        
        interface "页面读取接口" as IPageReader
        interface "表格解析接口" as ITableParser
    }
    
    package "数据库工具包" {
        component [DefectReposPGHandler] as PGHandler #LightYellow
        
        interface "数据库操作接口" as IDBOperation
    }
    
    package "工具类" {
        component [SimpleAEScryptor] as Encryptor #Lavender
        component [StringUtils] as StringUtil #Lavender
        component [Log4py] as Logger #Lavender
        
        interface "加密解密接口" as ICrypto
        interface "字符串处理接口" as IStringUtil
        interface "日志记录接口" as ILogger
    }
    
    package "数据验证包" {
        component [数据格式验证器] as FormatValidator #Pink
        component [字段内容验证器] as ContentValidator #Pink
        component [业务规则验证器] as BusinessValidator #Pink
        
        interface "数据验证接口" as IValidator
    }
    
    package "数据转换包" {
        component [字段映射器] as FieldMapper #Orange
        component [内容提取器] as ContentExtractor #Orange
        component [格式标准化器] as Normalizer #Orange
        
        interface "数据转换接口" as ITransformer
    }
}

package "基础设施层 (Infrastructure Layer)" #MistyRose {
    
    package "外部系统接口" {
        component [icenter API客户端] as IcenterAPI #Thistle
        component [PostgreSQL驱动] as PostgreDriver #Thistle
        
        interface "HTTP客户端接口" as IHttpClient
        interface "数据库驱动接口" as IDBDriver
    }
}

package "外部系统 (External Systems)" #LightGray {
    database "icenter系统" as IcenterSystem
    database "PostgreSQL数据库" as PostgreDB
}

' 接口实现关系
IcenterTool ..|> IPageReader
TableAnalysis ..|> ITableParser
PGHandler ..|> IDBOperation
Encryptor ..|> ICrypto
StringUtil ..|> IStringUtil
Logger ..|> ILogger
FormatValidator ..|> IValidator
ContentValidator ..|> IValidator
BusinessValidator ..|> IValidator
FieldMapper ..|> ITransformer
ContentExtractor ..|> ITransformer
Normalizer ..|> ITransformer
IcenterAPI ..|> IHttpClient
PostgreDriver ..|> IDBDriver

' 依赖关系
CLI --> ArgParser : 使用
CLI --> ConfigManager : 使用
CLI --> MainService : 创建

MainService --> IcenterTool : 依赖
MainService --> TableAnalysis : 依赖
MainService --> PGHandler : 依赖
MainService --> Encryptor : 依赖
MainService --> StringUtil : 依赖
MainService --> Logger : 依赖

MainService ..> FormatValidator : 使用
MainService ..> ContentValidator : 使用
MainService ..> BusinessValidator : 使用
MainService ..> FieldMapper : 使用
MainService ..> ContentExtractor : 使用
MainService ..> Normalizer : 使用

IcenterTool --> IcenterAPI : 使用
PGHandler --> PostgreDriver : 使用

IcenterAPI --> IcenterSystem : 连接
PostgreDriver --> PostgreDB : 连接

' 数据流说明
note top of MainService
  <b>核心服务组件</b>
  负责协调各个子组件
  实现完整的业务流程
end note

note right of IcenterTool
  <b>icenter接口组件</b>
  负责页面数据获取
  处理认证和会话管理
end note

note right of TableAnalysis
  <b>表格解析组件</b>
  负责HTML表格解析
  提取结构化数据
end note

note right of PGHandler
  <b>数据库操作组件</b>
  负责PostgreSQL操作
  处理事务和连接管理
end note

note bottom of FormatValidator
  <b>数据验证组件集</b>
  确保数据质量和完整性
  实施业务规则检查
end note

note bottom of FieldMapper
  <b>数据转换组件集</b>
  负责数据格式转换
  字段映射和标准化
end note

' 分层说明
note as N1
  <b>分层架构说明：</b>
  
  <b>应用层：</b> 处理用户输入和程序入口
  <b>领域层：</b> 实现核心业务逻辑和规则
  <b>基础设施层：</b> 提供技术实现和外部接口
  
  各层之间遵循依赖倒置原则
  通过接口进行松耦合
end note

@enduml 