@startuml defect_repos_data_flow
!theme plain
skinparam backgroundColor #FAFAFA

' 数据流颜色定义
skinparam {
    ActivityBackgroundColor #E8F4FD
    ActivityBorderColor #2E86AB
    DatabaseBackgroundColor #D4EDDA
    DatabaseBorderColor #28A745
    StorageBackgroundColor #FCF3CD
    StorageBorderColor #FFC107
    ComponentBackgroundColor #F8D7DA
    ComponentBorderColor #DC3545
}

title 缺陷代码库映射服务 - 数据处理流程

left to right direction

package "外部数据源" {
    database "icenter页面" as icenter_page {
        component "HTML表格" as html_table
        component "表格数据" as table_data
    }
}

package "数据获取层" {
    component "IcenterTool" as icenter_tool #LightBlue
    component "页面读取器" as page_reader #LightBlue
    
    storage "原始HTML" as raw_html #LightYellow
}

package "数据解析层" {
    component "IctTableAnalysis" as table_analyzer #LightCoral
    component "表格定位器" as table_locator #LightCoral
    component "数据提取器" as data_extractor #LightCoral
    
    storage "表格原始数据" as raw_table_data #LightYellow
}

package "数据验证层" {
    component "格式检查器" as format_checker #LightGreen
    component "必填字段验证器" as required_validator #LightGreen
    component "内容验证器" as content_validator #LightGreen
    
    storage "验证结果" as validation_result #LightYellow
}

package "数据转换层" {
    component "字段映射器" as field_mapper #Orange
    component "内容提取器" as content_extractor #Orange
    component "格式标准化器" as format_normalizer #Orange
    
    storage "标准化数据" as normalized_data #LightYellow
}

package "数据持久化层" {
    component "DefectReposPGHandler" as pg_handler #Violet
    component "SQL生成器" as sql_generator #Violet
    component "事务管理器" as transaction_manager #Violet
    
    database "PostgreSQL" as postgres {
        storage "defect_fix_repository_mapping表" as mapping_table
    }
}

' 数据流向
icenter_page --> icenter_tool : HTTP请求
icenter_tool --> page_reader : 调用页面读取
page_reader --> raw_html : 获取HTML内容

raw_html --> table_analyzer : 解析HTML
table_analyzer --> table_locator : 定位目标表格
table_locator --> data_extractor : 提取表格数据
data_extractor --> raw_table_data : 生成原始数据

raw_table_data --> format_checker : 检查数据格式
format_checker --> required_validator : 验证必填字段
required_validator --> content_validator : 验证字段内容
content_validator --> validation_result : 输出验证结果

validation_result --> field_mapper : 字段名映射
field_mapper --> content_extractor : 提取字段内容
content_extractor --> format_normalizer : 标准化格式
format_normalizer --> normalized_data : 生成标准数据

normalized_data --> pg_handler : 准备数据库操作
pg_handler --> sql_generator : 生成SQL语句
sql_generator --> transaction_manager : 执行事务
transaction_manager --> mapping_table : 更新数据表

' 数据结构说明
note top of raw_html
  <b>数据格式：</b>
  HTML字符串
  包含表格结构
end note

note top of raw_table_data
  <b>数据格式：</b>
  List[Dict[中文字段名, 原始值]]
  例：[{'所属子系统': 'TCF', '发现版本': 'v1.0'}]
end note

note top of normalized_data
  <b>数据格式：</b>
  List[Dict[英文字段名, 标准值]]
  例：[{'belong_sub_system': 'TCF', 'discovery_iversion': 'v1.0'}]
end note

note bottom of mapping_table
  <b>数据库表结构：</b>
  - workspace_key (工作区)
  - belong_sub_system (所属子系统)  
  - discovery_iversion (发现版本)
  - system_iteration (迭代)
  - repository (代码库)
  - branch (分支)
  - dev_image (开发镜像)
  - test_script_repository (测试脚本库)
  - test_script_branch (测试脚本分支)
end note

' 处理阶段分组
rectangle "数据获取阶段" #AliceBlue {
    icenter_tool
    page_reader
    raw_html
}

rectangle "数据解析阶段" #MistyRose {
    table_analyzer
    table_locator
    data_extractor
    raw_table_data
}

rectangle "数据验证阶段" #Honeydew {
    format_checker
    required_validator
    content_validator
    validation_result
}

rectangle "数据转换阶段" #PeachPuff {
    field_mapper
    content_extractor
    format_normalizer
    normalized_data
}

rectangle "数据持久化阶段" #Lavender {
    pg_handler
    sql_generator
    transaction_manager
}

@enduml 