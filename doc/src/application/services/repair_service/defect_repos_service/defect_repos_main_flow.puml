@startuml defect_repos_main_flow
!theme plain
skinparam backgroundColor #FAFAFA

' 简化流程颜色定义
skinparam activity {
    BackgroundColor #F8F9FA
    BorderColor #2E86AB
    StartColor #28A745
    EndColor #DC3545
    DiamondBackgroundColor #FFF2CC
    DiamondBorderColor #F39C12
}

skinparam note {
    BackgroundColor #F8F9FA
    BorderColor #6C757D
}

title 缺陷代码库映射服务 - 主业务流程

start
:解析命令行参数;
note right
  用户工号、密码、icenter页面ID
  PostgreSQL连接信息
  工作区等
end note

:初始化日志系统;

:解密数据库密码;
note right
  使用AES解密器解密
  PostgreSQL密码token
end note

:创建DefectRepositoryMappingService实例;
:初始化icenter工具集;
note right
  - IcenterTool (页面读取)
  - IctTableAnalysis (表格解析)
end note

:初始化PostgreSQL工具集;
note right
  - DefectReposPGHandler (数据库操作)
end note

' 数据获取阶段
partition "数据获取阶段" {
    :调用icenter API读取页面数据;
    note right
      通过sid、cid获取HTML页面内容
    end note

    if (页面读取成功?) then (是)
      :解析HTML页面，提取表格数据;
      note right
        根据表头['所属子系统', '发现版本', 
        '迭代', '开发镜像']定位表格
      end note
      
      if (找到匹配表格?) then (是)
        :提取表格行数据;
      else (否)
        :抛出异常: 未找到表格;
        stop
      endif
    else (否)
      :抛出异常: 页面读取失败;
      stop
    endif
}

' 数据验证阶段
partition "数据验证阶段" {
    :检查数据格式;
    note right
      验证数据是否为字典格式
      确保数据结构正确
    end note

    :验证必填字段;
    note right
      必填字段：所属子系统、发现版本、
      迭代、代码库、代码分支、开发镜像
    end note

    :验证字段内容非空;

    if (数据验证通过?) then (是)
    else (否)
      :抛出异常: 数据验证失败;
      stop
    endif
}

' 数据转换阶段
partition "数据转换阶段" {
    :字段名称映射;
    note right
      中文字段名 → 英文字段名
      '所属子系统' → 'belong_sub_system'
      '发现版本' → 'discovery_iversion'
      等等...
    end note

    :提取字段实际内容;
    note right
      处理不同数据格式：
      - 字典类型 (content字段)
      - 对象类型 (content属性)
      - 字符串类型 (直接使用)
    end note

    :设置可选字段默认值;
    note right
      测试脚本相关字段为空时
      设置为NULL
    end note
}

' 数据持久化阶段
partition "数据持久化阶段" {
    :调用PostgreSQL处理器;
    :更新数据库记录;
    note right
      执行INSERT或UPDATE操作
      根据唯一键判断是否已存在
    end note

    if (数据库更新成功?) then (是)
      :记录成功日志;
      :返回处理结果;
    else (否)
      :记录错误日志;
      :抛出异常: 数据库更新失败;
      stop
    endif
}

stop

@enduml 