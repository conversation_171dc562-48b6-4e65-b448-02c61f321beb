@startuml defect_repos_sequence
!theme plain
skinparam backgroundColor #FAFAFA

' 参与者颜色定义
skinparam participant {
    BackgroundColor #E8F4FD
    BorderColor #2E86AB
}

skinparam actor {
    BackgroundColor #D4EDDA
    BorderColor #28A745
}

skinparam boundary {
    BackgroundColor #FFF2CC
    BorderColor #F39C12
}

skinparam control {
    BackgroundColor #F8D7DA
    BorderColor #DC3545
}

skinparam entity {
    BackgroundColor #E2E3E5
    BorderColor #6C757D
}

title 缺陷代码库映射服务 - 交互时序图

actor "系统管理员" as admin
participant "命令行入口" as main #LightBlue
participant "DefectRepositoryMappingService" as service #LightCyan
participant "IcenterTool" as icenter #LightGreen
participant "IctTableAnalysis" as analyzer #LightCoral
participant "DefectReposPGHandler" as pg_handler #LightYellow
participant "PostgreSQL数据库" as database #LightGray

== 系统初始化阶段 ==
admin -> main : 执行命令行启动
activate main

main -> main : 解析命令行参数
main -> main : 初始化日志系统
main -> main : 解密数据库密码

main -> service : 创建服务实例
activate service

service -> icenter : 初始化icenter工具
activate icenter
icenter --> service : 初始化完成
deactivate icenter

service -> analyzer : 初始化表格分析工具
activate analyzer
analyzer --> service : 初始化完成
deactivate analyzer

service -> pg_handler : 初始化数据库处理器
activate pg_handler
pg_handler -> database : 测试数据库连接
activate database
database --> pg_handler : 连接成功
deactivate database
pg_handler --> service : 初始化完成
deactivate pg_handler

service --> main : 服务实例创建完成

== 主业务流程阶段 ==
main -> service : 执行主流程 update_repos_from_icenter_page_main()

service -> service : 获取icenter页面数据 get_defect_repos_mapping_from_icenter_page()
activate service

service -> icenter : 读取页面数据 page_read(sid, cid)
activate icenter
icenter -> icenter : 发送HTTP请求到icenter
icenter --> service : 返回页面HTML内容
deactivate icenter

service -> analyzer : 解析表格数据 search_table(html, headers)
activate analyzer
analyzer -> analyzer : 定位目标表格
analyzer -> analyzer : 提取表格行数据
analyzer -> analyzer : 解析表格内容
analyzer --> service : 返回原始表格数据
deactivate analyzer

service -> service : 验证数据 _check_defect_repos_mapping_datas()
note right of service
  验证步骤：
  1. 检查数据格式
  2. 验证必填字段
  3. 验证字段内容非空
end note

service -> service : 转换数据 _convert_icenter_table_data()
note right of service
  转换步骤：
  1. 字段名映射(中文→英文)
  2. 提取字段实际内容
  3. 处理可选字段默认值
end note

service --> service : 返回标准化数据
deactivate service

service -> service : 更新到数据库 update_defect_repos_mapping_to_pg()

service -> pg_handler : 执行数据库更新 update_defect_repos_mapping_to_pg_main()
activate pg_handler

loop 对每条映射记录
    pg_handler -> database : 检查记录是否存在
    activate database
    database --> pg_handler : 返回查询结果
    deactivate database
    
    alt 记录已存在
        pg_handler -> database : 执行UPDATE操作
        activate database
        database --> pg_handler : 更新成功
        deactivate database
    else 记录不存在
        pg_handler -> database : 执行INSERT操作
        activate database
        database --> pg_handler : 插入成功
        deactivate database
    end
end

pg_handler --> service : 数据库更新完成
deactivate pg_handler

service --> main : 主流程执行完成
deactivate service

main -> main : 记录成功日志
main --> admin : 处理完成
deactivate main

== 异常处理 ==
note over admin, database
  任何阶段发生异常时：
  1. 记录详细错误日志
  2. 抛出包含错误信息的异常
  3. 程序优雅退出
end note

@enduml 