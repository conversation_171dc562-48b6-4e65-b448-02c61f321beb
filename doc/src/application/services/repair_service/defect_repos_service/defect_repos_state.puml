@startuml defect_repos_state
!theme plain
skinparam backgroundColor #FAFAFA

' 状态颜色定义
skinparam state {
    BackgroundColor #E8F4FD
    BorderColor #2E86AB
    StartColor #28A745
    EndColor #DC3545
}

skinparam note {
    BackgroundColor #F8F9FA
    BorderColor #6C757D
}

title 缺陷代码库映射服务 - 状态流转图

[*] --> 未初始化 : 程序启动

state 未初始化 {
    未初始化 : 等待初始化参数
    未初始化 : 系统资源未分配
}

未初始化 --> 初始化中 : 开始初始化

state 初始化中 {
    state 解析参数 #LightYellow
    state 配置日志 #LightYellow
    state 解密密码 #LightYellow
    state 创建服务实例 #LightYellow
    
    [*] --> 解析参数
    解析参数 --> 配置日志 : 参数有效
    配置日志 --> 解密密码 : 日志配置完成
    解密密码 --> 创建服务实例 : 密码解密成功
    创建服务实例 --> [*] : 实例创建完成
}

初始化中 --> 就绪 : 初始化成功
初始化中 --> 初始化失败 : 初始化异常

state 初始化失败 #LightCoral {
    初始化失败 : 记录错误信息
    初始化失败 : 清理已分配资源
}

state 就绪 #LightGreen {
    就绪 : 服务实例已创建
    就绪 : 工具组件已初始化
    就绪 : 准备执行业务流程
}

就绪 --> 数据获取中 : 启动主流程

state 数据获取中 {
    state 连接icenter #LightBlue
    state 读取页面数据 #LightBlue
    state 解析HTML #LightBlue
    state 提取表格 #LightBlue
    
    [*] --> 连接icenter
    连接icenter --> 读取页面数据 : 连接成功
    读取页面数据 --> 解析HTML : 页面读取成功
    解析HTML --> 提取表格 : HTML解析完成
    提取表格 --> [*] : 表格数据提取完成
}

数据获取中 --> 数据验证中 : 数据获取成功
数据获取中 --> 数据获取失败 : 获取异常

state 数据获取失败 #LightCoral {
    数据获取失败 : icenter连接失败或
    数据获取失败 : 页面解析失败或
    数据获取失败 : 表格数据为空
}

state 数据验证中 {
    state 格式检查 #LightCyan
    state 必填字段验证 #LightCyan
    state 内容验证 #LightCyan
    state 业务规则检查 #LightCyan
    
    [*] --> 格式检查
    格式检查 --> 必填字段验证 : 格式正确
    必填字段验证 --> 内容验证 : 字段完整
    内容验证 --> 业务规则检查 : 内容有效
    业务规则检查 --> [*] : 验证通过
}

数据验证中 --> 数据转换中 : 验证通过
数据验证中 --> 数据验证失败 : 验证异常

state 数据验证失败 #LightCoral {
    数据验证失败 : 数据格式错误或
    数据验证失败 : 必填字段缺失或
    数据验证失败 : 字段内容无效
}

state 数据转换中 {
    state 字段映射 #Orange
    state 内容提取 #Orange
    state 格式标准化 #Orange
    state 数据清洗 #Orange
    
    [*] --> 字段映射
    字段映射 --> 内容提取 : 映射完成
    内容提取 --> 格式标准化 : 内容提取完成
    格式标准化 --> 数据清洗 : 格式标准化完成
    数据清洗 --> [*] : 数据转换完成
}

数据转换中 --> 数据持久化中 : 转换完成
数据转换中 --> 数据转换失败 : 转换异常

state 数据转换失败 #LightCoral {
    数据转换失败 : 字段映射错误或
    数据转换失败 : 内容提取失败或
    数据转换失败 : 格式转换异常
}

state 数据持久化中 {
    state 连接数据库 #Violet
    state 检查记录存在性 #Violet
    state 执行数据库操作 #Violet
    state 提交事务 #Violet
    
    [*] --> 连接数据库
    连接数据库 --> 检查记录存在性 : 连接成功
    检查记录存在性 --> 执行数据库操作 : 检查完成
    执行数据库操作 --> 提交事务 : 操作成功
    提交事务 --> [*] : 事务提交成功
}

数据持久化中 --> 处理完成 : 持久化成功
数据持久化中 --> 数据持久化失败 : 持久化异常

state 数据持久化失败 #LightCoral {
    数据持久化失败 : 数据库连接失败或
    数据持久化失败 : SQL执行错误或
    数据持久化失败 : 事务回滚
}

state 处理完成 #LightGreen {
    处理完成 : 所有数据已成功更新
    处理完成 : 业务流程执行完成
    处理完成 : 记录成功日志
}

' 异常状态处理
初始化失败 --> [*] : 程序退出
数据获取失败 --> [*] : 程序退出
数据验证失败 --> [*] : 程序退出
数据转换失败 --> [*] : 程序退出
数据持久化失败 --> [*] : 程序退出
处理完成 --> [*] : 程序正常结束

' 重试机制
数据获取失败 --> 数据获取中 : 重试\n(可配置次数)
数据持久化失败 --> 数据持久化中 : 重试\n(可配置次数)

note top of 未初始化
  <b>初始状态</b>
  系统启动后的初始状态
  等待参数输入和初始化
end note

note right of 数据获取中
  <b>核心业务阶段1</b>
  从icenter系统获取
  缺陷代码库映射信息
end note

note right of 数据验证中
  <b>核心业务阶段2</b>
  确保数据质量
  实施业务规则检查
end note

note right of 数据转换中
  <b>核心业务阶段3</b>
  转换数据格式
  标准化字段内容
end note

note right of 数据持久化中
  <b>核心业务阶段4</b>
  将数据保存到
  PostgreSQL数据库
end note

note bottom of 处理完成
  <b>终止状态</b>
  业务流程成功完成
  系统可以安全退出
end note

@enduml 