@startuml rdc_image_download_flow
!theme plain
skinparam backgroundColor #FAFAFA

' 流程图颜色定义
skinparam {
    ActivityBackgroundColor #F8F9FA
    ActivityBorderColor #2E86AB
    PartitionBackgroundColor #F0F8E8
    PartitionBorderColor #28A745
    NoteBackgroundColor #FFF2CC
    NoteBorderColor #F39C12
    DecisionBackgroundColor #E8F4FD
    DecisionBorderColor #DC143C
}

title 缺陷修复智能体对于缺陷描述信息中的图片下载解决方案

start

partition "第一条流水线 - RDC缺陷信息获取服务" {
    :获取RDC缺陷单信息;
    note right
      包含缺陷描述的原生HTML信息
    end note
    
    :解析缺陷描述HTML内容;
    note right
      原生HTML示例:
      <img src="https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/
      file/viewFile/675fa5e6-d09f-4c76-a718-21aff96d35b3/00313545" 
      title="image.png" alt="image.png" />
    end note
    
    :使用正则表达式匹配图片标签;
    note right
      匹配模式:
      <img src="{img_url}" title="image.png" alt="image.png" />
    end note
    
    if (发现图片标签?) then (是)
        :提取并补全图片url信息;
        note right
            注意：目前仅补全 https://izpc.zte.com.cn 前缀
            当前仅遇到缺少该种前缀的图片url
        end note
        
        :将图片HTML替换为约定标志位;
        note right
          替换为:
          __IMAGE::{img_url}::IMAGE__
        end note
    else (否)
        :保持原有内容不变;
    endif
    
    :处理完所有图片标签;
    
    :去除所有HTML标签;
    note right
      得到纯文本 + 图片标志位的描述信息
    end note
    
    :存储处理后的缺陷描述信息;
}

partition "第二条流水线 - 缺陷修复服务（使用建议）" {
    :读取缺陷描述信息;
    
    :使用正则表达式查找图片标志位;
    note right
      匹配模式:
      """__IMAGE::([^/]+)/([^:]+)::IMAGE__"""
      提取esckey和userid
    end note
    
    if (发现图片标志位?) then (是)
        while (还有图片标志位需要处理?) is (是)
            :解析图片标志位;
            
            :创建RdcApi实例;
            note right
              workspace = 'DAIP'
              user_id = '10155493'
              appcode = '82b2xxxxxxxxxxxxxxxxxxxxxxxxxx'
              rdc_api = RdcApi(workspace, user_id, appcode)
            end note
            
            :调用下载接口;
            note right
              rdc_api.download_rdc_picture(image_url, save_path)
              
              <b>RDC API下载方法：</b>              
              <b>方法签名：</b>
              rdc_api.download_rdc_picture(image_url, save_path)
              目前仅支持如下3种URL类型：
              1. 标准inone接口格式的图片
              2. icenterapi 接口的图片
              3. ueditor 格式接口的图片
              <b>参数说明：</b>
              - image_url: 图片url
              - save_path: 本地保存路径              
              <b>返回：</b>
              下载成功返回True，失败返回False
            end note
            
            if (下载成功?) then (是)
                :记录下载成功日志;
            else (否)
                :记录下载失败日志;
            endif
            
        endwhile (否)
        
    else (否)
        :无图片需要下载;
    endif
    
    :使用处理后的缺陷描述进行修复;
}

stop

floating note left
  <b>图片标志位格式约定：</b>
  
  <b>标准格式：</b>
  __IMAGE::{img_url}::IMAGE__
  
  <b>支持3种图片URL解析并下载：</b>
  1. 标准inone接口格式的图片(https://izpc.zte.com.cn)
    /zte-rdcloud-rdc-rdcserver/file/viewFile/{esckey}/{uid}
  2. icenterapi 接口的图片
    https://icenterapi.zte.com.cn/group3/M05/2D/B3/{code}.png
  3. ueditor 格式接口的图片
    https://izpc.zte.com.cn/zte-plm-wic-ueditor/ueditor/**/*.gif
  
  <b>优势：</b>
  1. 格式统一，便于解析
  2. 包含下载所需的完整信息
  3. 避免HTML标签在智能体处理中的干扰
end note

floating note right
  <b>技术流程总结：</b>
  
  <b>阶段1 - 信息预处理：</b>
  RDC获取 → HTML解析 → 图片标志位转换 → 存储
  
  <b>阶段2 - 智能体处理：</b>
  标志位识别 → 图片下载 → 本地化处理 → 缺陷修复
  
  <b>核心优势：</b>
  1. 分离关注点：获取与使用分离
  2. 标准化处理：统一的标志位格式
  3. 错误隔离：下载失败不影响主流程
end note

@enduml 