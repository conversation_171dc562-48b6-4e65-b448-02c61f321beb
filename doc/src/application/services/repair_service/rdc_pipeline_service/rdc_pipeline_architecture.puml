@startuml rdc_pipeline_architecture
!theme plain
skinparam backgroundColor #F8F9FA
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 11

title RDC Pipeline Service - 系统架构图

package "用户界面层" {
    [命令行接口] as CLI
    [参数解析器] as Parser
}

package "应用服务层" {
    [RdcPipelineService] as RPS
    [配置管理器] as Config
}

package "领域服务层" {
    package "RDC处理" {
        [Rdc<PERSON>and<PERSON>] as RH
        [RdcApi] as RA
        [DefaultBugStrategy] as DBS
        [YamlConfigLoader] as YCL
    }
    
    package "Jenkins处理" {
        [JenkinsHandler] as JH
        [JenkinsApi] as JA
    }
    
    package "数据处理" {
        [DefectReposPGHandler] as DPG
        [DataValidator] as DV
    }
}

package "数据传输层" {
    [ZeroAgentDefectFixDTO] as ZADTO
    [RdcTestcaseDto] as RTDTO
}

package "基础设施层" {
    [SimpleAEScryptor] as AES
    [StringUtils] as SU
    [Log4py] as LOG
    [ThreadPoolExecutor] as TPE
}

package "外部系统" {
    [RDC System] as RDC
    [Jenkins Server] as JS
    [PostgreSQL] as PG
}

' 依赖关系
CLI --> Parser
CLI --> RPS
Parser --> Config
RPS --> Config

RPS --> RH
RPS --> JH
RPS --> ZADTO

RH --> RA
RH --> DBS
RH --> YCL
RH --> DPG
RH --> ZADTO
RH --> RTDTO

DBS --> ZADTO
DBS --> DV

JH --> JA

RA --> RDC
JA --> JS
DPG --> PG

RPS --> AES
RPS --> SU
RPS --> LOG
RPS --> TPE

' 新增依赖关系
DPG --> SU : 版本号裁剪

' 数据流
RDC --> RA : 缺陷数据
RA --> RH : 处理后数据
RH --> RPS : DTOs
RPS --> JH : 流水线参数
JH --> JS : 构建请求
JS --> JH : 构建结果

PG --> DPG : 映射数据
DPG --> RH : 仓库信息

' 查询优先级数据流
SU --> DPG : 裁剪后版本号
DPG --> PG : 优先级查询
PG --> DPG : 查询结果

note right of DBS
  <b>图片处理功能：</b>
  - HTML图片识别
  - 占位符转换
  - 图片下载支持
end note

note right of DPG
  <b>数据库查询优化：</b>
  - 三层查询优先级
  - 版本号裁剪支持
  - 智能匹配策略
  - 避免重复查询
end note

note right of SU
  <b>版本号裁剪功能：</b>
  - 通用版本号格式支持
  - 智能字母识别和裁剪
  - 支持多种项目前缀
  - 边界情况处理
end note

note right of RPS
  <b>核心功能：</b>
  - 协调整个流水线
  - 并发控制
  - 异常处理
end note

@enduml 