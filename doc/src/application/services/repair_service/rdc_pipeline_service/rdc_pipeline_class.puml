@startuml rdc_pipeline_class
!theme plain
skinparam backgroundColor #F8F9FA
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 11

title RDC Pipeline Service - 类结构图

class RdcPipelineService {
    - rdc_handler: RdcHandler
    - jks_handler: <PERSON><PERSON>andler
    - jks_namespace: str
    - jks_job_name: str
    - jks_branch_name: Optional[str]
    - jks_cr_workspaces: List[str]
    - jks_parallelism: int
    - rdc_last_days: int
    
    + __init__(rdc_uid, rdc_workspace, rdc_appcode, pg_config, jenkins_token, jenkins_uid, jks_namespace, jks_job_name, rdc_last_days, rdc_cr_workspaces, jks_parallelism, jks_branch_name, jenkins_url)
    + trigger_single_jenkins_pipeline(namespace, job_name, bug, branch): str
    + trigger_multi_jenkins_pipeline(namespace, job_name, bugs, branch, parallelism): void
    + main_process(): void
}

class RdcHandler {
    - rdc_api: RdcApi
    - loader: YamlConfigLoader
    - pg_handler: DefectReposPGHandler
    - system_workspace_key: str
    
    + __init__(workspace, user_id, app_code, config_source, pg_config)
    + query_bug_work_items(days, cross_workspace_keys): List[ZeroAgentDefectFixDTO]
    + get_fix_datas(days, cross_workspaces): List[ZeroAgentDefectFixDTO]
    - _query_rdc_work_items(workspace, work_item_type, days): List[Dict]
    - _validate_and_filter_items(workspace, rdc_items): List[Dict]
    - _enrich_with_database_info(workspace, valid_items): List[Dict]
    - _assemble_defect_dtos(items_with_db_info): List[ZeroAgentDefectFixDTO]
    - _enrich_with_testcase_info(dtos): List[ZeroAgentDefectFixDTO]
}

class DefaultBugStrategy {
    - config: Dict[str, Any]
    
    + get_query_config(workspace, days): RdcQueryConfig
    + convert_rdc_item_to_basic_data(workspace, rdc_item): Dict[str, Any]
    + should_include_item(item): bool
    - _convert_images_to_placeholders(html_content): str
    - _process_field_mappings(rdc_item, rdc_field_cfg, data, group_zero_fix_field): void
    - _handle_html_text_extraction(rdc_item, rdc_field_name, zero_agent_field_name, data): void
}

class JenkinsHandler {
    - jenkins: JenkinsApi
    
    + __init__(uid, upd, jenkins_url)
    + trigger_and_track(namespace, job_name, params, branch, poll_interval, timeout): str
    - _format_duration(milliseconds): str
}

class DefectReposPGHandler {
    - db: PostgresDB
    - table_name: str
    
    + __init__(db_host, db_port, db_user, db_password, db_name)
    + get_defect_repos_mapping_by_conditions(workspace_key, belong_sub_system, discovery_iversion, system_iteration): List[Dict[str, Any]]
    + get_defect_repos_mapping_by_workspace(workspace_key): List[Dict[str, Any]]
    + update_defect_repos_mapping_to_pg_main(workspace_key, defect_repos_mapping_datas): void
    - _check_query_result(result): List[Dict[str, Any]]
    - _delete_existing_records(workspace_key): int
    - _insert_new_records(workspace_key, defect_repos_mapping_datas): int
}

class StringUtils {
    + {static} crop_version(version: str): str
    + {static} calculate_checksum(data, algorithm): str
    + {static} check_dict_contains_keys(dict_data, keys): bool
}

class ZeroAgentDefectFixDTO {
    + work_item_id: str
    + system_workspace_key: str
    + belong_sub_system: str
    + discovery_iversion: str
    + system_iteration: str
    + problem_statement: str
    + repositories_str: str
    + workspace: str
    + affected_codes: str
    + language: str
    + dev_image: str
    + test_image: str
    + test_info: str
    + test_cases: List[RdcTestcaseDto]
}

class RdcTestcaseDto {
    + work_item_id: str
    + test_case_title: str
    + test_case_status: str
    + test_case_path: str
    + auto_status: str
}

' 关系定义
RdcPipelineService --> RdcHandler : uses
RdcPipelineService --> JenkinsHandler : uses
RdcPipelineService --> ZeroAgentDefectFixDTO : processes

RdcHandler --> DefaultBugStrategy : uses
RdcHandler --> ZeroAgentDefectFixDTO : creates
RdcHandler --> RdcTestcaseDto : creates
RdcHandler --> DefectReposPGHandler : uses

DefectReposPGHandler --> StringUtils : uses

DefaultBugStrategy ..> ZeroAgentDefectFixDTO : converts to

ZeroAgentDefectFixDTO --> RdcTestcaseDto : contains

note right of DefectReposPGHandler
  <b>三层查询优先级：</b>
  1. 子系统+版本号+迭代
  2. 子系统+版本号裁剪+迭代
  3. 子系统+版本号裁剪+default
  <b>版本号裁剪：</b>
  - 使用StringUtils.crop_version()
  - 去除最后一个子版本号的字母部分
end note

note right of StringUtils
  <b>版本号裁剪功能：</b>
  - 通用版本号格式支持
  - 智能字母识别和裁剪
  - 支持多种项目前缀
end note

note right of RdcPipelineService
  <b>核心服务：</b>
  - 协调整个流水线流程
  - 管理并发执行
  - 处理异常和超时
end note

@enduml 