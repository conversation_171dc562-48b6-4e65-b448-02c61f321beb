@startuml rdc_pipeline_component
!theme plain
skinparam backgroundColor #FAFAFA

top to bottom direction

' 组件颜色定义
skinparam component {
    BackgroundColor #E8F4FD
    BorderColor #2E86AB
    FontSize 10
}

skinparam interface {
    BackgroundColor #FFF2CC
    BorderColor #F39C12
    FontSize 9
}

skinparam package {
    BackgroundColor #F0F8E8
    BorderColor #28A745
    FontSize 11
}

skinparam database {
    BackgroundColor #D4EDDA
    BorderColor #28A745
    FontSize 10
}

' 线条颜色定义
skinparam arrow {
    Color #2C3E50
}

title RDC流水线服务 - 组件关系图

' 应用层
package "应用层" #LightBlue {
    [命令行入口] as CLI #Wheat
    [参数解析器] as ArgParser #Wheat
    [配置管理器] as ConfigManager #Wheat
    [RdcPipelineService] as MainService #LightCyan
}

' 领域层
package "领域层" #LightGreen {
    
    package "RDC工具" {
        [RdcHandler] as RdcHandler #MistyRose
        [RdcApi] as RdcApi #MistyRose
        [RdcStrategyFactory] as RdcStrategyFactory #MistyRose
        interface "RDC数据接口" as IRdcData
        interface "缺陷查询接口" as IDefectQuery
    }
    
    package "Jenkins工具" {
        [JenkinsHandler] as JenkinsHandler #LightYellow
        [JenkinsClient] as JenkinsClient #LightYellow
        [BuildTracker] as BuildTracker #LightYellow
        interface "流水线触发接口" as IPipelineTrigger
        interface "构建监控接口" as IBuildMonitor
    }
    
    ' 简化并发管理 - 系统能力
    package "并发管理" {
        [并发管理器] as ConcurrencyManager #Lavender
        interface "并发控制接口" as IConcurrency
    }
    
    package "数据对象" {
        [ZeroAgentDefectFixDTO] as DefectDTO #Pink
        [RdcTestcaseDto] as TestcaseDTO #Pink
        [PipelineParamsBuilder] as ParamBuilder #Pink
        interface "数据传输接口" as IDataTransfer
    }
    
    package "工具类" {
        [SimpleAEScryptor] as Encryptor #Orange
        [StringUtils] as StringUtil #Orange
        [Log4py] as Logger #Orange
        [ExceptionHandler] as ExceptionHandler #Orange
        interface "加密解密接口" as ICrypto
        interface "字符串处理接口" as IStringUtil
        interface "日志记录接口" as ILogger
        interface "异常处理接口" as IException
    }
}

' 分隔线强制换行
note as separator1
  .
end note

' 基础设施层
package "基础设施层" #MistyRose {
    [HTTP客户端] as HttpClient #Thistle
    [并发执行器] as ThreadPoolExecutor #Thistle
    [JSON处理器] as JsonProcessor #Thistle
    interface "HTTP接口" as IHttp
    interface "并发执行接口" as IThreadPool
    interface "序列化接口" as ISerialization
}

' 分隔线强制换行
note as separator2
  .
end note

' 外部系统层
package "外部系统" #LightGray {
    database "RDC系统" as RdcSystem
    database "Jenkins系统" as JenkinsSystem  
    database "PostgreSQL数据库" as PostgreDB
}

' 接口实现关系 - 蓝色虚线
RdcHandler ..[#0066CC]|> IRdcData
RdcHandler ..[#0066CC]|> IDefectQuery
JenkinsHandler ..[#0066CC]|> IPipelineTrigger
JenkinsHandler ..[#0066CC]|> IBuildMonitor
ConcurrencyManager ..[#0066CC]|> IConcurrency
DefectDTO ..[#0066CC]|> IDataTransfer
TestcaseDTO ..[#0066CC]|> IDataTransfer
Encryptor ..[#0066CC]|> ICrypto
StringUtil ..[#0066CC]|> IStringUtil
Logger ..[#0066CC]|> ILogger
ExceptionHandler ..[#0066CC]|> IException
HttpClient ..[#0066CC]|> IHttp
ThreadPoolExecutor ..[#0066CC]|> IThreadPool
JsonProcessor ..[#0066CC]|> ISerialization

' 应用层内部依赖 - 绿色实线
CLI -[#228B22]-> ArgParser : 使用
CLI -[#228B22]-> ConfigManager : 使用
CLI -[#228B22]-> MainService : 创建

' 主服务依赖 - 红色实线
MainService -[#DC143C]-> RdcHandler : 依赖
MainService -[#DC143C]-> JenkinsHandler : 依赖
MainService -[#DC143C]-> ConcurrencyManager : 依赖
MainService -[#DC143C]-> DefectDTO : 使用
MainService -[#DC143C]-> ParamBuilder : 使用

' 工具依赖 - 橙色虚线
MainService ..[#FF8C00]> Encryptor : 解密密钥
MainService ..[#FF8C00]> StringUtil : 处理字符串
MainService ..[#FF8C00]> Logger : 记录日志
MainService ..[#FF8C00]> ExceptionHandler : 处理异常

' 领域层内部依赖 - 紫色实线
RdcHandler -[#8A2BE2]-> RdcApi : 使用
RdcHandler -[#8A2BE2]-> RdcStrategyFactory : 使用
JenkinsHandler -[#8A2BE2]-> JenkinsClient : 使用
JenkinsHandler -[#8A2BE2]-> BuildTracker : 使用

' 基础设施依赖 - 深绿色实线
RdcApi -[#006400]-> HttpClient : 使用
JenkinsClient -[#006400]-> HttpClient : 使用
ConcurrencyManager -[#006400]-> ThreadPoolExecutor : 使用
ParamBuilder -[#006400]-> JsonProcessor : 使用

' 外部系统连接 - 棕色实线
HttpClient -[#8B4513]-> RdcSystem : 连接
HttpClient -[#8B4513]-> JenkinsSystem : 连接
RdcHandler -[#8B4513]-> PostgreDB : 查询

' 注释说明
note top of MainService
  <b>核心编排组件</b>
  负责协调各个子系统
  实现完整的流水线流程
end note

note right of RdcHandler
  <b>RDC集成组件</b>
  负责缺陷单数据获取
  处理数据验证和转换
end note

note right of JenkinsHandler
  <b>Jenkins集成组件</b>
  负责流水线触发和监控
  处理构建状态跟踪
end note

note right of ConcurrencyManager
  <b>系统并发能力</b>
  Python内置ThreadPoolExecutor
  提供并发控制和任务调度
end note

note bottom of DefectDTO
  <b>数据传输对象</b>
  封装缺陷单相关数据
  提供标准化的数据接口
end note

' 线条颜色说明
note as ColorLegend
  <b>连接线颜色说明：</b>
  <color:#0066CC>蓝色虚线 - 接口实现关系</color>
  <color:#228B22>绿色实线 - 应用层内部依赖</color>
  <color:#DC143C>红色实线 - 主服务核心依赖</color>
  <color:#FF8C00>橙色虚线 - 工具类使用</color>
  <color:#8A2BE2>紫色实线 - 领域层内部依赖</color>
  <color:#006400>深绿实线 - 基础设施依赖</color>
  <color:#8B4513>棕色实线 - 外部系统连接</color>
end note

' 架构分层说明
note as N1
  <b>分层架构说明：</b>
  
  <b>应用层：</b> 处理用户输入和程序入口
  <b>领域层：</b> 实现核心业务逻辑和规则
  <b>基础设施层：</b> 提供技术实现和外部接口
  
  各层之间遵循依赖倒置原则
  通过接口进行松耦合
end note

' 隐藏分隔符
hide separator1
hide separator2

@enduml 