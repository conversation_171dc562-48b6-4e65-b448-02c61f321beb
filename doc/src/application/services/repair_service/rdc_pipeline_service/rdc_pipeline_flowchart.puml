@startuml rdc_pipeline_flowchart
!theme plain
skinparam backgroundColor #F8F9FA
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 11

title RDC Pipeline Service - 流程图

start

:解析命令行参数;
:初始化日志系统;
:解密RDC和PG API密钥;
:创建RdcPipelineService实例;

:调用main_process();

partition "RDC数据获取" {
    :调用RdcHandler.get_fix_datas();
    :查询RDC缺陷单基础信息;
    :验证必填字段并过滤;
    :图片HTML处理;
    note right: 转换为占位符格式
    
    partition "数据库查询优化" {
        :从数据库获取代码库映射;
        note right: <b>三层查询优先级：</b>
        1. 子系统+版本号+迭代
        2. 子系统+版本号裁剪+迭代
        3. 子系统+版本号裁剪+default
        
        if (优先级1查询有结果?) then (是)
            :使用优先级1结果;
        else (否)
            :版本号裁剪处理;
            if (优先级2查询有结果?) then (是)
                :使用优先级2结果;
            else (否)
                if (迭代不为default?) then (是)
                    :使用优先级3查询;
                else (否)
                    :返回空结果;
                endif
            endif
        endif
    }
    
    :组装缺陷修复DTO;
    :补充关联测试用例信息;
}

if (获取到缺陷单?) then (是)
    :验证并发参数;
    if (parallelism > 5?) then (是)
        :重置为最大并行度5;
    endif
    
    :创建ThreadPoolExecutor;
    
    partition "并发执行" {
        :遍历缺陷单列表;
        :构建流水线参数;
        :提交任务到线程池;
        :并行触发Jenkins流水线;
        :监控构建状态;
        :收集执行结果;
    }
    
    :统计执行结果;
    :计算成功率;
    :记录最终报告;
else (否)
    :记录无缺陷单日志;
endif

:清理资源;
stop

note right of "图片HTML处理"
  <b>图片处理步骤：</b>
  1. 识别HTML中的img标签
  2. 提取图片URL
  3. 转换为占位符格式
  4. 下载图片到本地
  5. 更新HTML内容
end note

note right of "并发执行"
  <b>并发控制：</b>
  - 最大并行度：5
  - 超时时间：12600秒
  - 异常隔离机制
end note

note right of "数据库查询优化"
  <b>版本号裁剪功能：</b>
  - 通用版本号格式支持
  - 智能字母识别和裁剪
  - 支持多种项目前缀
  - 避免重复查询
end note

@enduml 