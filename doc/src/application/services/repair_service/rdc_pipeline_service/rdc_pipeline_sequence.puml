@startuml rdc_pipeline_sequence
!theme plain
skinparam backgroundColor #F8F9FA
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 11

' 定义颜色方案 - 使用浅色确保文字可见性
skinparam participant {
    BackgroundColor #FFFFFF
    BorderColor #E0E0E0
    FontColor #333333
}

skinparam actor {
    BackgroundColor #F8F9FA
    BorderColor #D1ECF1
    FontColor #0C5460
}

skinparam note {
    BackgroundColor #FFF8DC
    BorderColor #FFE4B5
    FontColor #8B4513
}

' 设置生命线为虚线
skinparam sequence {
    LifeLineBorderColor #CCCCCC
    LifeLineBorderThickness 1
    LifeLineBorderStyle dashed
}

title RDC Pipeline Service - 时序图

' 使用浅色背景区分大模块
box "用户交互层" #F0F8FF
actor User as U #E6F3FF
end box

box "核心服务层" #F0FFF0
participant "RdcPipelineService" as RPS #E8F5E8
participant "RdcHandler" as RH #E8F5E8
end box

box "业务策略层" #FFF8F0
participant "DefaultBugStrategy" as DBS #FFF2E6
end box

box "数据访问层" #F8F0FF
participant "DefectReposPGHandler" as DPG #F0E6FF
participant "StringUtils" as SU #F0E6FF
participant "PostgreSQL" as PG #F0E6FF
end box

box "CI/CD层" #FFF0F0
participant "JenkinsHandler" as JH #FFE6E6
participant "JenkinsApi" as JA #FFE6E6
participant "Jenkins Server" as JS #FFE6E6
end box

box "外部系统" #F0F0F8
participant "RDC System" as RDC #E6E6F0
end box

U -> RPS: main_process()
activate RPS

RPS -> RH: get_fix_datas(days, cross_workspaces)
activate RH

RH -> RH: query_bug_work_items()
activate RH

RH -> DBS: get_query_config(workspace, days)
activate DBS
DBS --> RH: RdcQueryConfig
deactivate DBS

RH -> RDC: queries_query_work_items(payload)
activate RDC
RDC --> RH: rdc_items
deactivate RDC

RH -> RH: _validate_and_filter_items()
activate RH

loop for each rdc_item
    RH -> DBS: convert_rdc_item_to_basic_data()
    activate DBS
    
    DBS -> DBS: _convert_images_to_placeholders()
    note right: 将HTML中的图片转换为占位符格式
    
    DBS --> RH: basic_data
    deactivate DBS
end

RH --> RH: valid_items
deactivate RH

RH -> RH: _enrich_with_database_info()
activate RH

loop for each valid_item
    RH -> DPG: get_defect_repos_mapping_by_conditions()
    activate DPG
    
    note right of DPG
      三层查询优先级：
      1. 子系统+版本号+迭代
      2. 子系统+版本号裁剪+迭代
      3. 子系统+版本号裁剪+default
    end note
    
    DPG -> PG: SELECT query (优先级1)
    activate PG
    PG --> DPG: mapping_data
    deactivate PG
    
    alt 优先级1查询无结果
        DPG -> SU: crop_version(version)
        activate SU
        SU --> DPG: cropped_version
        deactivate SU
        
        DPG -> PG: SELECT query (优先级2)
        activate PG
        PG --> DPG: mapping_data
        deactivate PG
        
        alt 优先级2查询无结果
            DPG -> PG: SELECT query (优先级3)
            activate PG
            PG --> DPG: mapping_data
            deactivate PG
        end
    end
    
    DPG --> RH: problem_mapping_dicts
    deactivate DPG
end

RH --> RH: items_with_db_info
deactivate RH

RH -> RH: _assemble_defect_dtos()
activate RH
RH --> RH: dtos
deactivate RH

RH -> RH: _enrich_with_testcase_info()
activate RH
RH --> RH: dtos_with_testcases
deactivate RH

RH --> RPS: List[ZeroAgentDefectFixDTO]
deactivate RH
deactivate RH

RPS -> RPS: trigger_multi_jenkins_pipeline()
activate RPS

loop for each bug (parallel)
    RPS -> JH: trigger_and_track(namespace, job_name, bug, branch)
    activate JH
    
    JH -> JA: trigger_build(namespace, job_name, branch, params)
    activate JA
    JA -> JS: POST /buildWithParameters
    activate JS
    JS --> JA: Response with Location header
    deactivate JS
    JA --> JH: response
    deactivate JA
    
    JH -> JA: get_queue_items(queue_url)
    activate JA
    JA -> JS: GET queue info
    activate JS
    JS --> JA: queue_info
    deactivate JS
    JA --> JH: queue_info
    deactivate JA
    
    loop until build starts
        JH -> JA: common_get_info_api(queue_url)
        activate JA
        JA --> JH: queue_status
        deactivate JA
    end
    
    loop until build completes
        JH -> JA: common_get_info_api(build_url)
        activate JA
        JA --> JH: build_status
        deactivate JA
    end
    
    JH --> RPS: build_result
    deactivate JH
end

RPS -> RPS: calculate success rate
RPS --> U: execution report
deactivate RPS

@enduml 