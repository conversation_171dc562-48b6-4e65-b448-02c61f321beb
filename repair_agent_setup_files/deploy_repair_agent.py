import sys
import logging as log
import argparse
import subprocess
import platform
import shutil
import json
import re
from pathlib import Path
BASE_DIR = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(BASE_DIR))
from src.domain.common.Log4py import Log4py

__author__ = '魏然10155493'

# 模块级别的logger
logger = log.getLogger(__name__)


PIP_INSTALL_SURFFIX = ' --progress-bar off --no-cache-dir --trusted-host mirrors.zte.com.cn -i https://artsz.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple'
KEY_CHECK_PACKAGES = ["flask", "langchain", "mcp"]  # 用于验证Python虚拟环境的库是否安装成功的几个关键库


class DeployRepairAgent:
    """Repair Agent用于vscode一键部署脚本"""

    def __init__(self, uid: str):
        self.uid = uid
        self.project_root = Path(__file__).resolve().parents[1]
        logger.info(f"初始化部署脚本，用户ID: {uid}, 项目根目录: {self.project_root}")

    def __exec_shell_pipe(self, cmd):
        # 实施打印执行结果（多用于执行时间较长的步骤显示）
        logger.info(cmd)

        # 根据操作系统选择shell
        if platform.system() == "Windows":
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
        elif platform.system() == "Linux":
            # Linux环境下使用bash确保source命令可用
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True, executable='/bin/bash')
        else:
            raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")

        for line in iter(process.stdout.readline, b''):
            logger.info(line.decode().strip())
        process.stdout.close()
        return_code = process.wait()
        if return_code != 0:
            logger.error(f"Command failed with return code {return_code}. cmd: {cmd}")
            raise RuntimeError(f"Command failed with return code {return_code}. cmd: {cmd}")

    def check_env(self):
        """检查环境是否包含 pip tar 命令，检查Python版本是否大于 3.10"""
        logger.info("开始检查环境...")

        # 检查Python版本
        python_version = sys.version_info
        logger.info(f"当前Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

        if python_version != (3, 11):
            raise RuntimeError(f"Python版本必须为3.11，当前版本: {python_version.major}.{python_version.minor}")
        # todo 由于目前在非Python3.11环境中未充分验证，且在非3.11中安装确实出现问题，后续验证充分后再放开
        # if python_version < (3, 10):
        #     raise RuntimeError(f"Python版本必须大于等于3.10，当前版本: {python_version.major}.{python_version.minor}")

        # 检查pip命令
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"],
                                    capture_output=True, text=True, check=True)
            logger.info(f"pip版本检查成功: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError("pip命令不可用，请确保已安装pip")

        # 检查tar命令
        try:
            if platform.system() == "Windows":
                # Windows下检查是否有tar命令（通常通过Git Bash或WSL提供）
                result = subprocess.run(["tar", "--version"],
                                        capture_output=True, text=True, check=True)
                logger.info("tar命令检查成功")
            elif platform.system() == "Linux":
                # Linux/Unix系统
                result = subprocess.run(["tar", "--version"],
                                        capture_output=True, text=True, check=True)
                logger.info("tar命令检查成功")
            else:
                raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("tar命令不可用，但这不是必需的，继续执行...")

        logger.info("环境检查完成")

    def install_uv(self):
        """安装uv"""
        logger.info("开始安装uv...")

        try:
            # 检查uv是否已安装
            result = subprocess.run(["uv", "--version"],
                                    capture_output=True, text=True, check=True)
            logger.info(f"uv已安装，版本: {result.stdout.strip()}")

            # 在容器环境中，检查uv是否可用
            if platform.system() == "Linux":
                logger.info("检测到Linux环境，检查uv在容器中的可用性...")
                try:
                    # 尝试创建一个测试虚拟环境来验证uv是否可用
                    test_cmd = ["uv", "venv", "--help"]
                    subprocess.run(test_cmd, capture_output=True, text=True, check=True, timeout=10)
                    logger.info("uv在容器环境中可用")
                except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                    logger.warning("uv在容器环境中可能不可用，但继续尝试使用")
            elif platform.system() == "Windows":
                logger.info("检测到Windows环境，跳过容器可用性检查")
            else:
                raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")

            return
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.info("uv未安装，开始安装...")

        try:
            # 使用pip安装uv
            logger.info("使用pip安装uv...")
            cmd = f"{sys.executable} -m pip install uv {PIP_INSTALL_SURFFIX}"
            self.__exec_shell_pipe(cmd)
            logger.info("uv安装成功")

            # 验证安装
            result = subprocess.run(["uv", "--version"],
                                    capture_output=True, text=True, check=True)
            logger.info(f"uv安装验证成功，版本: {result.stdout.strip()}")

        except Exception as e:
            logger.error(f"uv安装失败: {e}")
            logger.warning("uv安装失败，将使用pip直接安装依赖")
            # 不抛出异常，允许脚本继续执行

    def install_lib(self):
        """安装Repair Agent相关依赖"""
        logger.info("开始安装Repair Agent相关依赖...")

        # 检查requirements.txt文件是否存在
        requirements_file = self.project_root / "src" / "domain" / "subdomains" / "repair_agent" / "requirements.txt"
        if not requirements_file.exists():
            raise FileNotFoundError(f"requirements.txt文件不存在: {requirements_file}")

        logger.info(f"找到requirements.txt文件: {requirements_file}")

        # 删除现有的虚拟环境（如果存在）
        venv_path = self.project_root / ".venv"
        if venv_path.exists():
            logger.info(f"删除现有虚拟环境: {venv_path}")
            try:
                shutil.rmtree(venv_path)
                logger.info("虚拟环境删除成功")
            except Exception as e:
                logger.error(f"删除虚拟环境失败: {e}")
                raise RuntimeError(f"删除虚拟环境失败: {e}")
        else:
            logger.info("虚拟环境不存在，将创建新的虚拟环境")

        try:
            # 根据操作系统选择不同的安装策略
            if platform.system() == "Windows":
                # Windows环境：依次执行命令
                logger.info("Windows环境，使用Windows安装策略...")

                # 1. 创建虚拟环境
                logger.info("步骤1: 创建虚拟环境...")
                subprocess.run(["uv", "venv"], cwd=self.project_root,
                               capture_output=True, text=True, check=True, timeout=60)
                logger.info("虚拟环境创建成功")

                # 2. 激活虚拟环境并安装依赖
                logger.info("步骤2: 激活虚拟环境并安装依赖...")
                activate_script = self.project_root / ".venv" / "Scripts" / "activate"
                cmd = (f"cd {self.project_root} && {activate_script} && "
                       f"uv pip install -r {requirements_file} "
                       f"-i https://artsz.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple")
                self.__exec_shell_pipe(cmd)
                logger.info("Windows环境依赖安装成功")

            elif platform.system() == "Linux":
                # Linux环境（含容器）：分步执行命令
                logger.info("Linux环境，使用Linux安装策略...")

                # 1. 创建虚拟环境
                logger.info("步骤1: 创建虚拟环境...")
                subprocess.run(["uv", "venv"], cwd=self.project_root,
                               capture_output=True, text=True, check=True, timeout=60)
                logger.info("虚拟环境创建成功")

                # 2. 激活虚拟环境并安装依赖
                logger.info("步骤2: 激活虚拟环境并安装依赖...")
                cmd = (f"cd {self.project_root} && source .venv/bin/activate && "
                       f"uv pip install -r {requirements_file} "
                       f"-i https://artsz.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple")
                self.__exec_shell_pipe(cmd)
                logger.info("Linux环境依赖安装成功")
            else:
                # 不支持的操作系统
                raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")

            # 验证安装
            self._verify_venv_installation()

        except Exception as e:
            logger.error(f"依赖安装失败: {e}")
            raise RuntimeError(f"依赖安装失败: {e}")

    def _verify_venv_installation(self):
        """验证虚拟环境中的包安装情况"""
        logger.info("验证虚拟环境中的包安装...")

        try:
            # 根据操作系统选择虚拟环境中的Python路径
            if platform.system() == "Windows":
                venv_python = self.project_root / ".venv" / "Scripts" / "python.exe"
            elif platform.system() == "Linux":
                venv_python = self.project_root / ".venv" / "bin" / "python"
            else:
                raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")

            # 检查关键包是否安装
            for package in KEY_CHECK_PACKAGES:
                try:
                    result = subprocess.run([str(venv_python), "-c", f"import {package}; print('{package} installed')"],
                                            capture_output=True, text=True, check=False, cwd=self.project_root)
                    if result.returncode == 0:
                        logger.info(f"✓ {package} 安装成功")
                    else:
                        logger.warning(f"✗ {package} 安装可能有问题")
                        logger.debug(f"错误信息: {result.stderr}")
                except Exception as e:
                    logger.warning(f"检查包 {package} 时出错: {e}")

        except Exception as e:
            logger.warning(f"验证虚拟环境时出错: {e}")

    def config_repair(self):
        """更新 src/domain/subdomains/repair_agent/config/mcp_repair_default.yaml 配置"""
        logger.info("开始更新Repair Agent配置文件...")

        config_file = self.project_root / "src" / "domain" / "subdomains" / "repair_agent" / "config" / "mcp_repair_default.yaml"
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        logger.info(f"找到配置文件: {config_file}")

        if self.uid == '00000000':
            logger.info(f"使用默认api_key，uid = {self.uid}")
            return

        try:
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 使用正则表达式查找并替换api_key
            # 匹配 api_key: 后面的值（可能是任何字符串）
            pattern = r'(api_key:\s*)([^\s]+)'
            match = re.search(pattern, content)

            if match:
                old_api_key = match.group(2)
                # 替换为新的用户ID
                new_content = re.sub(pattern, rf'\g<1>{self.uid}', content)

                # 写回文件
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                logger.info(f"更新api_key: {old_api_key} -> {self.uid}")
                logger.info("配置文件更新成功")
            else:
                logger.warning("未找到api_key配置项")

        except Exception as e:
            logger.error(f"配置文件更新失败: {e}")
            raise RuntimeError(f"配置文件更新失败: {e}")

    def config_mcp(self):
        """处理 repair_agent_setup_files/mcp_setting.json 文件"""
        logger.info("开始处理MCP配置文件...")

        # 源文件路径
        source_file = self.project_root / "repair_agent_setup_files" / "mcp_setting.json"
        if not source_file.exists():
            raise FileNotFoundError(f"MCP配置文件不存在: {source_file}")

        logger.info(f"找到MCP配置文件: {source_file}")

        try:
            # 读取配置文件
            with open(source_file, 'r', encoding='utf-8') as f:
                mcp_config = json.load(f)

            # 获取项目根目录的父目录（即install_dir）
            install_dir = self.project_root.parent

            # 在Windows环境下，确保路径使用正确的分隔符
            if platform.system() == "Windows":
                # 将路径转换为Windows格式（使用反斜杠）
                install_dir_str = str(install_dir).replace('/', '\\')
                logger.info(f"Windows环境，转换路径分隔符: {install_dir_str}")
            elif platform.system() == "Linux":
                install_dir_str = str(install_dir)
            else:
                raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")

            logger.info(f"安装目录: {install_dir_str}")

            # 更新配置文件中的${install_dir}占位符
            config_str = json.dumps(mcp_config, indent=4, ensure_ascii=False)
            config_str = config_str.replace("${install_dir}", install_dir_str)

            # 目标文件路径（项目根目录）
            target_file = self.project_root / "mcp_setting.json"

            # 写入目标文件
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(config_str)

            logger.info(f"MCP配置文件已复制到: {target_file}")
            logger.debug(f"配置文件内容已更新，install_dir替换为: {install_dir_str}")

        except Exception as e:
            logger.error(f"MCP配置文件处理失败: {e}")
            raise RuntimeError(f"MCP配置文件处理失败: {e}")

    def create_env_file(self):
        """创建.env文件设置PYTHONPATH"""
        logger.info("开始创建.env文件...")

        env_file = self.project_root / ".env"
        pythonpath_content = f"PYTHONPATH=$PYTHONPATH:{self.project_root}"

        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(f"{pythonpath_content}\n")

            logger.info(f".env文件创建成功: {env_file}")
            logger.debug(f".env文件内容: {pythonpath_content}")

        except Exception as e:
            logger.error(f".env文件创建失败: {e}")
            raise RuntimeError(f".env文件创建失败: {e}")

    def deploy_main(self):
        """主部署流程"""
        logger.info("开始部署Repair Agent...")

        try:
            # 检查环境（如是否包含 pip tar 命令）
            self.check_env()
            # 安装uv
            self.install_uv()
            # 安装Repair Agent相关依赖
            self.install_lib()
            # 更新 src/domain/subdomains/repair_agent/config/mcp_repair_default.yaml 配置
            self.config_repair()
            # 处理 repair_agent_setup_files/mcp_setting.json 文件
            self.config_mcp()
            # 创建.env文件
            self.create_env_file()

        except Exception as e:
            logger.error(f"部署失败: {e}")
            raise

        logger.info("Repair Agent部署完成！")

        # 显示虚拟环境使用说明
        self._show_venv_usage_info()

    def _show_venv_usage_info(self):
        """显示虚拟环境使用说明"""
        venv_path = self.project_root / ".venv"
        if venv_path.exists():
            logger.info("=" * 60)
            logger.info("虚拟环境使用说明:")
            logger.info("=" * 60)

            if platform.system() == "Windows":
                activate_script = venv_path / "Scripts" / "activate"
                python_exe = venv_path / "Scripts" / "python.exe"
                logger.info(f"激活虚拟环境: {activate_script}")
                logger.info(f"使用虚拟环境Python: {python_exe}")
            elif platform.system() == "Linux":
                activate_script = venv_path / "bin" / "activate"
                python_exe = venv_path / "bin" / "python"
                logger.info(f"激活虚拟环境: source {activate_script}")
                logger.info(f"使用虚拟环境Python: {python_exe}")
            else:
                raise RuntimeError(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")


def run_rdc_pipeline_service_main():
    """
    启动示例：
    python repair_agent_setup_files/deploy_repair_agent.py \
        --user-id "10155493"
    """
    parser = argparse.ArgumentParser(
        description="repair agent 部署脚本",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--user-id', default='00000000', help='用户工号')
    # 调试模式标志
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式，使用默认参数值覆盖部分设置')

    args = parser.parse_args()

    # 调试模式覆盖设置
    if args.debug:
        log4py = Log4py('log/deploy_repair_agent_debug.log')
        logger = log4py.get_logger()
        logger.setLevel(log.DEBUG)  # 设置日志级别为DEBUG
    else:
        Log4py('log/deploy_repair_agent.log')

    try:
        dra = DeployRepairAgent(args.user_id)
        dra.deploy_main()
    except Exception as e:
        log.error(f"部署脚本执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run_rdc_pipeline_service_main()
