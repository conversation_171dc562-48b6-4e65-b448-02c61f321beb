{"mcpId": "repair_agent_mcp-2.0", "githubUrl": "repair_agent_mcp-2.0.tar.gz", "name": "repair_agent_mcp", "author": "ZTE Corporation", "description": "智能代码修复工具，基于大语言模型自动分析代码问题并生成修复补丁。支持多种编程语言，提供精确的代码修复建议和详细的修复轨迹信息。", "codiconIcon": "tools", "logoUrl": "icons/repair_agent.png", "category": "code-repair", "tags": ["code-repair", "bug-fix", "code-analysis", "ai-assistant", "programming", "development"], "requiresApiKey": true, "isRecommended": true, "githubStars": 150, "downloadCount": 1250, "createdAt": "2025-07-25T10:00:00.000000Z", "updatedAt": "2025-07-28T10:00:00.000000Z", "installCmd": "tar -zxvf repair_agent_mcp-2.0.tar.gz && cd repair_agent_mcp-2.0 && python  repair_agent_setup_files/deploy_repair_agent.py", "mcpSettingConfig": "repair_agent_mcp.json"}