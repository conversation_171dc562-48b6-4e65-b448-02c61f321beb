# Started by AICoder, pid:udd20i21e3n5f8f141e8093bd3218206d9191b4a
"""
缺陷代码库映射信息服务模块

提供从icenter页面获取缺陷代码库映射信息并更新到PostgreSQL数据库的完整解决方案。
包含数据获取、解析、验证、转换和持久化的全流程处理。

核心设计特点：
1. 分层架构：分离数据访问层（PG操作）与业务逻辑层（icenter交互）
2. 数据校验：严格的数据格式检查和唯一性验证
3. 安全处理：密码字段的AES加密解密处理
4. 异常处理：详细的错误日志记录和友好的错误提示
"""
import argparse
import sys
import logging as log
from typing import List, Dict, Any
from pathlib import Path

from src.domain.common.aescrypt import SimpleAEScryptor
from src.domain.common.string_utils import StringUtils
BASE_DIR = Path(__file__).resolve().parents[4]
sys.path.insert(0, str(BASE_DIR))

from src.domain.common.Log4py import Log4py
from src.domain.subdomains.icenter_tools.icenter_tool import IcenterTool
from src.domain.subdomains.icenter_tools.ict_table_analysis import IctTableAnalysis
from src.domain.subdomains.repair_agent.database.defect_repos_handler import DefectReposPGHandler

# icenter 页面中，缺陷代码库映射信息表格的表头
sample_hearders = ['所属子系统', '发现版本', '迭代', '开发镜像']


class DefectRepositoryMappingService:
    """缺陷代码库映射信息服务类
    
    负责从icenter页面获取缺陷代码库映射信息并更新到PostgreSQL数据库。
    提供完整的业务流程：页面读取、表格解析、数据验证、格式转换和数据库持久化。
    """
    
    def __init__(self, user_id: str, user_pwd: str, ict_sid: str, ict_cid: str, pg_host: str, pg_port: int, pg_user: str, pg_pwd: str, pg_name: str, workspace_key: str):
        """缺陷代码库映射信息服务初始化

        Args:
            user_id (str): 工号
            user_pwd (str): 密码
            ict_sid (str): icenter页面sid
            ict_cid (str): icenter页面cid
            pg_host (str): pg数据库主机地址
            pg_port (int): pg数据库端口号
            pg_user (str): pg数据库用户名
            pg_pwd (str): pg数据库密码
            pg_name (str): pg数据库名称
            workspace_key (str): 工作区（即项目名称），如DAIP
        """
        log.info(f"初始化缺陷代码库映射信息服务完成: uid={user_id} workspace_key={workspace_key} sid={ict_sid} cid={ict_cid} pg_host=...{pg_host[-5:]} pg_name={pg_name}")
        self.ict_sid = ict_sid
        self.ict_cid = ict_cid
        self.workspace_key = workspace_key
        # 初始化 icenter 工具集（页面读取、icenter表格解析等）
        self.__init_ict_handler(user_id, user_pwd)
        # 初始化 pg 工具集（数据库连接、查询、更新等）
        self.__init_pg_handler(pg_host, pg_port, pg_user, pg_pwd, pg_name)

    def __init_ict_handler(self, user_id: str, user_pwd: str):
        # 初始化 icenter 工具集（页面读取、icenter表格解析等）
        self.ict_handler = IcenterTool(user_id, user_pwd)
        self.ict_table_analysis = IctTableAnalysis()
        log.info("初始化 icenter 工具集完成")

    def __init_pg_handler(self, pg_host: str, pg_port: int, pg_user: str, pg_pwd: str, pg_name: str):
        # 初始化 pg 工具集（数据库连接、查询、更新等）
        self.pg_handler = DefectReposPGHandler(pg_host, pg_port, pg_user, pg_pwd, pg_name)
        log.info("初始化 pg 工具集完成")

    def _extract_field_content(self, field_value: Any) -> str:
        """提取字段的实际内容

        该方法用于从不同类型的字段值中提取实际的内容字符串。
        支持处理字典、对象和字符串等不同类型的输入。

        Args:
            field_value (Any): 字段值，可能是以下类型之一：
                - dict: 包含'content'键的字典，如{"content": "actual_value"}
                - object: 具有content属性的对象
                - str: 直接的字符串值
                - None: 空值
                - 其他类型: 将转换为字符串

        Returns:
            str: 提取的实际内容字符串，去除首尾空白字符

        Examples:
            >>> service = DefectRepositoryMappingService(...)
            >>> service._extract_field_content("test")  # 返回 "test"
            >>> service._extract_field_content({"content": "test"})  # 返回 "test"
            >>> service._extract_field_content(None)  # 返回 "None"
        """
        if isinstance(field_value, dict):
            # 如果是字典，尝试获取content字段
            if 'content' in field_value:
                return str(field_value['content']).strip()
            else:
                return str(field_value).strip()
        elif hasattr(field_value, 'content'):
            # 如果是对象且有content属性
            return str(field_value.content).strip()
        else:
            # 其他情况直接转换为字符串
            return str(field_value).strip()

    def _check_defect_repos_mapping_datas(self, defect_repos_mapping_datas: List[Dict[str, Any]]):
        """检查缺陷代码库映射信息字段是否符合要求

        该方法对缺陷代码库映射信息进行全面的数据验证，包括：
        1. 数据格式检查
        2. 必填字段检查
        3. 字段值非空检查
        （已废弃）4. 数据唯一性检查

        Args:
            defect_repos_mapping_datas (List[Dict[str, Any]]): 缺陷代码库映射信息列表，
                每个字典应包含以下字段：
                - 所属子系统 (str): 必填，不能为空
                - 发现版本 (str): 必填，不能为空
                - 迭代 (str): 必填，不能为空
                - 代码库 (str): 必填，不能为空
                - 代码分支 (str): 必填，不能为空
                - 开发镜像 (str): 必填，不能为空
                - 测试脚本所在的代码库 (str, optional): 可选字段
                - 测试脚本所在的代码库分支 (str, optional): 可选字段

        Raises:
            ValueError: 当数据不符合要求时抛出异常，包含具体的错误信息：
                - "缺陷代码库映射信息不能为空": 当输入列表为空时
                - "第{i}条数据格式错误，应为字典类型": 当数据项不是字典时
                - "第{i}条数据缺少必填字段: {field}": 当缺少必填字段时
                - "第{i}条数据的字段 {field} 不能为空": 当必填字段为空时
                （已废弃）- "存在重复数据: 所属子系统={sub_system}, 发现版本={version}, 迭代={iteration}": 当存在重复数据时

        Examples:
            >>> valid_data = [{
            ...     '所属子系统': 'test_system',
            ...     '发现版本': 'v1.0',
            ...     '迭代': 'iter1',
            ...     '代码库': 'test_repo',
            ...     '代码分支': 'main',
            ...     '开发镜像': 'test_image'
            ... }]
            >>> service._check_defect_repos_mapping_datas(valid_data)  # 不抛出异常
        """
        if not defect_repos_mapping_datas:
            raise ValueError("缺陷代码库映射信息不能为空")

        # 检查数据格式和必填字段
        required_fields = ['所属子系统', '发现版本', '迭代', '代码库', '代码分支', '开发镜像']
        optional_fields = ['测试脚本所在的代码库', '测试脚本所在的代码库分支']
        
        for i, data in enumerate(defect_repos_mapping_datas):
            if not isinstance(data, dict):
                raise ValueError(f"第{i+1}条数据格式错误，应为字典类型")
            
            # 检查必填字段
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"第{i+1}条数据缺少必填字段: {field}")
                
                # 提取字段的实际内容
                field_value = self._extract_field_content(data[field])
                if not field_value or not str(field_value).strip():
                    raise ValueError(f"第{i+1}条数据的字段 {field} 不能为空")
            
            # 检查可选字段（测试脚本相关字段可以为空）
            for field in optional_fields:
                if field not in data:
                    # 可选字段不存在时，设置为空字符串
                    data[field] = ""

        # 检查唯一性：所属子系统+发现版本+迭代 必须唯一
        # 取消唯一性检查
        # unique_keys = set()
        # for i, data in enumerate(defect_repos_mapping_datas):
        #     # 提取字段的实际内容用于唯一性检查
        #     sub_system = self._extract_field_content(data['所属子系统'])
        #     discovery_version = self._extract_field_content(data['发现版本'])
        #     iteration = self._extract_field_content(data['迭代'])
            
        #     unique_key = (sub_system, discovery_version, iteration)
        #     if unique_key in unique_keys:
        #         raise ValueError(f"存在重复数据: 所属子系统={sub_system}, 发现版本={discovery_version}, 迭代={iteration}")
        #     unique_keys.add(unique_key)

    def _convert_icenter_table_data(self, icenter_table_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将icenter表格数据转换为字典列表，便于数据库入库

        该方法将icenter页面解析的表格数据转换为标准化的字典格式，
        用于后续的数据库操作。主要进行字段名映射和数据格式转换。

        字段映射关系：
        - '所属子系统' -> 'belong_sub_system'
        - '发现版本' -> 'discovery_iversion'
        - '迭代' -> 'system_iteration'
        - '代码库' -> 'repository'
        - '代码分支' -> 'branch'
        - '开发镜像' -> 'dev_image'
        - '测试脚本所在的代码库' -> 'test_script_repository' (可为NULL)
        - '测试脚本所在的代码库分支' -> 'test_script_branch' (可为NULL)

        Args:
            icenter_table_data (List[Dict[str, Any]]): icenter表格解析后的原始数据，
                每个字典包含中文字段名和对应的值

        Returns:
            List[Dict[str, Any]]: 转换后的字典列表，包含英文字段名和标准化的数据格式

        Examples:
            >>> icenter_data = [{
            ...     '所属子系统': 'test_system',
            ...     '发现版本': 'v1.0',
            ...     '迭代': 'iter1',
            ...     '代码库': 'test_repo',
            ...     '代码分支': 'main',
            ...     '开发镜像': 'test_image'
            ... }]
            >>> result = service._convert_icenter_table_data(icenter_data)
            >>> # result[0] 包含英文字段名和对应的值
        """
        converted_data = []
        
        for row_data in icenter_table_data:
            if not isinstance(row_data, dict):
                continue
                
            # 提取字段值
            converted_row = {}
            
            # 映射字段名称
            field_mapping = {
                '所属子系统': 'belong_sub_system',
                '发现版本': 'discovery_iversion', 
                '迭代': 'system_iteration',
                '代码库': 'repository',
                '代码分支': 'branch',
                '开发镜像': 'dev_image',
                '测试脚本所在的代码库': 'test_script_repository',
                '测试脚本所在的代码库分支': 'test_script_branch'
            }
            
            for chinese_field, english_field in field_mapping.items():
                if chinese_field in row_data:
                    # 使用统一的字段内容提取方法
                    field_value = self._extract_field_content(row_data[chinese_field])
                    
                    # 对于测试脚本相关字段，空值转换为None以支持NULL
                    if english_field in ['test_script_repository', 'test_script_branch']:
                        if not field_value or not str(field_value).strip():
                            converted_row[english_field] = None
                        else:
                            converted_row[english_field] = field_value
                    else:
                        converted_row[english_field] = field_value
                else:
                    # 对于测试脚本相关字段，缺失时设置为None
                    if english_field in ['test_script_repository', 'test_script_branch']:
                        converted_row[english_field] = None
                    else:
                        converted_row[english_field] = ""
            
            converted_data.append(converted_row)
        
        return converted_data

    def get_defect_repos_mapping_from_icenter_page(self) -> List[Dict[str, Any]]:
        """从icenter页面获取缺陷代码库映射信息

        该方法通过调用icenter接口获取页面数据，然后解析其中的表格信息，
        获取缺陷代码库映射配置。整个过程包括页面读取、表格解析、数据验证和格式转换。

        Returns:
            List[Dict[str, Any]]: 缺陷代码库映射信息列表，每个字典包含以下字段：
                - belong_sub_system (str): 所属子系统
                - discovery_iversion (str): 发现版本
                - system_iteration (str): 系统迭代
                - repository (str): 代码库
                - branch (str): 代码分支
                - dev_image (str): 开发镜像
                - test_script_repository (str, optional): 测试脚本所在的代码库
                - test_script_branch (str, optional): 测试脚本所在的代码库分支

        Raises:
            ValueError: 当无法获取数据或数据格式不正确时抛出异常：
                - "未从icenter页面获取到缺陷代码库映射信息": 当页面解析结果为空时
                - "意外的数据类型: {type}": 当解析结果类型不符合预期时

        Examples:
            >>> service = DefectRepositoryMappingService(...)
            >>> result = service.get_defect_repos_mapping_from_icenter_page()
            >>> # result 包含从icenter页面获取的映射信息
        """
        page_data = self.ict_handler.page_read(self.ict_sid, self.ict_cid)
        # 获取缺陷代码库映射信息表格 的 所在icenter页面HTML
        page_html = page_data['bo']['contentBody']
        log.info(f"获取缺陷代码库映射信息表格 的 所在icenter页面HTML完成: {self.ict_sid} {self.ict_cid}")
        # 解析icenter页面HTML，获取缺陷代码库映射信息表格
        repo_mapping_datas = self.ict_table_analysis.search_table(page_html, sample_hearders)
        log.info("解析icenter页面HTML，获取缺陷代码库映射信息表格完成")
        
        if not repo_mapping_datas:
            msg = f"未从icenter页面获取到缺陷代码库映射信息: {self.ict_sid} {self.ict_cid}"
            log.error(msg)
            raise ValueError(msg)
        
        # 确保数据是列表格式
        if isinstance(repo_mapping_datas, dict):
            # 如果是字典，转换为列表
            repo_mapping_datas = [repo_mapping_datas]
        elif not isinstance(repo_mapping_datas, list):
            msg = f"意外的数据类型: {type(repo_mapping_datas)}"
            log.error(msg)
            raise ValueError(msg)
            
        # 检查缺陷代码库映射信息字段是否符合要求（比如是否有重复信息等）
        self._check_defect_repos_mapping_datas(repo_mapping_datas)
        log.info("检查缺陷代码库映射信息字段是否符合要求完成")
        # 将icenter表格数据转换为字典列表，便于数据库入库
        return self._convert_icenter_table_data(repo_mapping_datas)

    def update_defect_repos_mapping_to_pg(self, defect_repos_mapping: List[Dict[str, Any]]):
        """将缺陷代码库映射信息更新到pg数据库

        该方法将处理后的缺陷代码库映射信息保存到PostgreSQL数据库中。
        通过调用DefectReposHandler进行实际的数据库操作。

        Args:
            defect_repos_mapping (List[Dict[str, Any]]): 缺陷代码库映射信息列表，
                每个字典应包含标准化的英文字段名和对应的值

        Examples:
            >>> mapping_data = [{
            ...     'belong_sub_system': 'test_system',
            ...     'discovery_iversion': 'v1.0',
            ...     'system_iteration': 'iter1',
            ...     'repository': 'test_repo',
            ...     'branch': 'main',
            ...     'dev_image': 'test_image'
            ... }]
            >>> service.update_defect_repos_mapping_to_pg(mapping_data)
        """
        self.pg_handler.update_defect_repos_mapping_to_pg_main(self.workspace_key, defect_repos_mapping)

    def update_repos_from_icenter_page_main(self):
        """从icenter页面获取缺陷代码库映射信息并更新到数据库

        这是主要的业务流程方法，整合了从icenter页面获取数据到数据库更新的完整流程。
        包括数据获取、验证、转换和持久化等步骤。

        Raises:
            ValueError: 当数据获取或处理过程中出现错误时抛出异常

        Examples:
            >>> service = DefectRepositoryMappingService(...)
            >>> service.update_repos_from_icenter_page_main()
            >>> # 完成从icenter页面到数据库的完整更新流程
        """
        # 从icenter页面获取缺陷代码库映射信息
        defect_repos_mapping_datas = self.get_defect_repos_mapping_from_icenter_page()
        # 将缺陷代码库映射信息更新到pg数据库
        self.update_defect_repos_mapping_to_pg(defect_repos_mapping_datas)


def run_defect_repos_service_main():
    """
    启动示例：(使用Python3.8以上版本)
    python src/application/services/repair_service/defect_repos_service.py \ 
        --user-id "10155493" \ 
        --user-pwd "***" \ 
        --ict-sid "80efb5cf1135447e99b13184a80d5421" \ 
        --ict-cid "8e2a7adcb9c24e2f9d415ea5269d34bb" \ 
        --pg-token "***" \ 
        --rdc-workspace "DAIP"
    """
    parser = argparse.ArgumentParser(
        description="构建缺陷单并触发缺陷修复流水线",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # icenter相关参数
    parser.add_argument('--user-id', required=True, help='icenter接口使用. 登录icenter的用户工号')
    parser.add_argument('--user-pwd', required=True, help='icenter接口使用. 登录icenter的密码')
    parser.add_argument('--ict-sid', required=True, help='icenter接口使用. 页面的 sid')
    parser.add_argument('--ict-cid', required=True, help='icenter接口使用. 页面的 cid')
    # PG相关参数
    parser.add_argument('--pg-host', default='**************', help='PG接口使用. PG数据库主机地址')
    parser.add_argument('--pg-port', type=int, default=65432, help='PG接口使用. PG数据库端口号')
    parser.add_argument('--pg-user', default='postgres', help='PG接口使用. PG数据库用户名')
    parser.add_argument('--pg-token', required=True, help='PG接口使用. PG数据库token')
    parser.add_argument('--pg-name', default='zero_agent', help='PG接口使用. PG数据库名称')
    # RDC相关参数
    parser.add_argument('--rdc-workspace', required=True, help='工作区（即项目名称），如DAIP')

    # 调试模式标志
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式，使用默认参数值覆盖部分设置')

    args = parser.parse_args()

    # 调试模式覆盖设置
    if args.debug:
        Log4py('local_files/log/defect_repos_service_debug.log')
        log.info("调试模式启用，使用默认参数覆盖部分设置")
        # 设置调试模式下的默认值
        args.user_id = '10155493'
        args.rdc_workspace = 'DAIP'

        # 使用固定值覆盖敏感信息
        # args.rdc_decode = '82b222a48ccfxxxxxxxxxxxxxxxxxxxx'
        # args.jenkins_token = '**********************************'
    else:
        Log4py('local_files/log/defect_repos_service.log')

    key = StringUtils.calculate_checksum('zero')[:16]
    value = StringUtils.value_pg
    decode = args.pg_token
    log.info(decode[:3])
    sa = SimpleAEScryptor(key, 'CBC', decode)
    pg_appcode = sa.decrypt(value)

    # 初始化服务
    drms = DefectRepositoryMappingService(
        user_id=args.user_id,
        user_pwd=args.user_pwd,
        ict_sid=args.ict_sid,
        ict_cid=args.ict_cid,
        pg_host=args.pg_host,
        pg_port=args.pg_port,
        pg_user=args.pg_user,
        pg_pwd=pg_appcode,
        pg_name=args.pg_name,
        workspace_key=args.rdc_workspace,
    )
    drms.update_repos_from_icenter_page_main()


if __name__ == '__main__':
    run_defect_repos_service_main()
# Ended by AICoder, pid:udd20i21e3n5f8f141e8093bd3218206d9191b4a
