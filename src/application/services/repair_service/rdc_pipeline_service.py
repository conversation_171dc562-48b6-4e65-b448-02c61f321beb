"""构建缺陷单并触发缺陷修复流水线
使用该服务的最低Python版本为3.8
"""
import logging as log
from typing import List, Optional, Dict, Any
import concurrent.futures
import sys
import argparse
from pathlib import Path
BASE_DIR = Path(__file__).resolve().parents[4]
sys.path.insert(0, str(BASE_DIR))

from src.domain.common.string_utils import StringUtils
from src.domain.common.aescrypt import SimpleAEScryptor
from src.domain.common.Log4py import Log4py
from src.domain.subdomains.rdc_tools.handler.rdc_handler import Rdc<PERSON>andler
from src.domain.subdomains.jenkins_tools.handler.jenkins_handler import <PERSON><PERSON><PERSON><PERSON>
from src.domain.subdomains.rdc_tools.dto.zero_fix_dto import ZeroAgentDefectFixDTO
from src.domain.subdomains.rdc_tools.utils.string_util import RdcStringUtil

__author__ = '魏然10155493'

JENKINS_MAX_PARALLELISM = 5    # 缺陷修复流水线触发的最大并发数


class RdcPipelineService:
    """构建缺陷单并触发缺陷修复流水线
    """
    rdc_handler: RdcHandler
    jks_handler: <PERSON>Handler

    def __init__(self, rdc_uid: str, rdc_workspace: str, rdc_appcode: str, pg_config: Dict[str, Any],
                 jenkins_token: str, jenkins_uid: str, jks_namespace: str, jks_full_project_name: str,
                 rdc_last_days: int = 30, rdc_cr_workspaces: Optional[List[str]] = None, rdc_appointed_to: Optional[str] = None,
                 jks_parallelism: int = 3, jenkins_url: str = 'https://cloudci.zte.com.cn'):
        """初始化参数

        Args:
            rdc_uid (str): RDC接口使用. 启动RDC缺陷单检索的用户工号
            rdc_workspace (str): RDC接口使用. 工作区（即项目名称），如DAIP
            rdc_appcode (str): RDC接口使用. RDC 应用API 码（项目 - 项目管理 - 项目应用（ITOO）API 码）
            pg_config: 数据库配置，格式为：
                {
                    "db_name": "zero_agent",
                    "db_user": "postgres",
                    "db_password": "xxxxxxx",
                    "db_host": "**************",
                    "db_port": 65432,
                }
            jenkins_token (str): Jenkins接口使用. 启动流水线的Jenkins用户API token
            jenkins_uid (str): Jenkins接口使用. 启动流水线的Jenkins用户工号
            jks_namespace (str): Jenkins接口使用. 启动流水线的Jenkins命名空间 如 central-zxccd
            jks_full_project_name (str): Jenkins接口使用. 启动流水线的Jenkins job名称
            rdc_last_days (int, optional): RDC接口使用. 搜索缺陷单新建至当天的天数. Defaults to 30.
            rdc_cr_workspaces (List[str], optional): RDC接口使用. 多个工作区列表，例如 ['iRAI', 'iMOP', 'DAIP'] . Defaults to None. 如果为空则查询整个RDC中的所有缺陷单
            rdc_appointed_to (str, optional): RDC接口使用. RDC缺陷单检索字段：当前处理人，多个工号用半角逗号分隔，如 10155493,10349517. Defaults to None.
            jks_parallelism (int): Jenkins接口使用. 启动流水线的Jenkins 最大并发数
            jenkins_url (str): Jenkins接口使用. Jenkins服务器URL地址. Defaults to 'https://cloudci.zte.com.cn'.
        """
        # 验证 rdc_appointed_to 参数
        if rdc_appointed_to and rdc_appointed_to.strip():
            RdcStringUtil.validate_rdc_appointed_to(rdc_appointed_to)
        
        # self.rdc_workspace = rdc_workspace
        # self.rdc_uid = rdc_uid
        self.jks_namespace = jks_namespace
        self.jks_full_project_name = jks_full_project_name
        # self.rdc_appcode = rdc_appcode
        self.jks_cr_workspaces = rdc_cr_workspaces
        self.jks_parallelism = jks_parallelism
        self.rdc_last_days = rdc_last_days
        self.rdc_appointed_to = rdc_appointed_to
        self.jks_handler = JenkinsHandler(jenkins_uid, jenkins_token, jenkins_url)
        p_yaml = 'src/domain/subdomains/rdc_tools/config/rdc_field_mappings.yaml'
        self.rdc_handler = RdcHandler(rdc_workspace, rdc_uid, rdc_appcode, p_yaml, pg_config)
        log.info(f'RdcPipelineService 初始化完成, workspace: {rdc_workspace}, jenkins_uid: {jenkins_uid}, '
                 f'cross_workspaces: {rdc_cr_workspaces}, last_days: {rdc_last_days}, rdc_appointed_to: {rdc_appointed_to}')

    def trigger_single_jenkins_pipeline(self, namespace: str, full_project_name: str, bug: ZeroAgentDefectFixDTO) -> str:
        """执行单个Jenkins流水线

        Args:
            namespace (str): Jenkins命名空间
            full_project_name (str): 完整的job名称，如 'qfs_debug_pipelines/repairAgent-RdcDefectProcessor'
            bug (ZeroAgentDefectFixDTO): 缺陷单实体对象

        Returns:
            str: _description_
        """
        # 传递参数详情：https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/462327f7465c11f09a4c3f1a03ea223e/view
        build_params = {
            'rdc_id': bug.work_item_id,  # 'DAIP-1036231',  # 字符串参数
            # '[{"repository": "DAP/MANAGER/MMS", "branch": "release/15.1.1", "relative_path": ""}] ',  # 字符串参数
            'repositories': bug.repositories_str,
            # 'daip-mms-dashboard/dashboard-server/dashboard-server-domain',  # 字符串参数
            'workspace': bug.workspace,     # 工作区路径
            # 'daip-mms-dashboard/dashboard-server/dashboard-server-domain',  # 字符串参数
            'affected_codes': bug.affected_codes,
            # 'DEMO: daip-mms-dashboard/dashboard-server/dashboard-server-domain/src/test/java/com/zte/daip/manager/mms...
            'problem_statement': bug.problem_statement,     # 波及代码路径
            'language': bug.language,
            # 'zxccds-release-docker.artnj.zte.com.cn/swap/zero-agents/sandbox:20241227',
            'sandbox_image': bug.dev_image,
            'test_image': bug.test_image,           # todo 目前始终为空
            'test_info': bug.test_info,             # todo 目前始终为空
            'test_script_repository': bug.test_script_repository,   # 测试脚本代码库
            'test_script_branch': bug.test_script_branch,           # 测试脚本代码库分支
            'test_cases': [testcase.to_dict() for testcase in bug.test_cases] if bug.test_cases else []
        }
        result = self.jks_handler.trigger_and_track(namespace, full_project_name, params=build_params, poll_interval=10, timeout=10800)
        return result

    def trigger_multi_jenkins_pipeline(self, namespace: str, full_project_name: str, bugs: List[ZeroAgentDefectFixDTO], parallelism: int = 3):
        """并行触发多个 Jenkins 流水线

        Args:
            namespace: Jenkins 命名空间
            full_project_name: Jenkins 作业名称
            bugs: 缺陷DTO列表
            parallelism: 缺陷修复流水线触发的并发数（默认3）        """
        if not bugs:
            log.info(
                f"没有需要触发的缺陷流水线, namespace={namespace}, full_project_name={full_project_name}")
            return
        if parallelism > JENKINS_MAX_PARALLELISM:
            log.warning(
                f'并行度 {parallelism} 超过最大并行度 {JENKINS_MAX_PARALLELISM}，将其重置为最大并行度 JENKINS_MAX_PARALISM={JENKINS_MAX_PARALLELISM}.')
            parallelism = JENKINS_MAX_PARALLELISM

        log.info(f"开始触发 {len(bugs)} 个缺陷的流水线，最大并行度: {parallelism}")

        # 使用线程池执行器管理并行任务
        with concurrent.futures.ThreadPoolExecutor(max_workers=parallelism) as executor:
            # 创建 Future 到缺陷ID 的映射
            future_to_bug_id = {}

            for bug in bugs:
                # 提交任务到线程池
                future = executor.submit(
                    self.trigger_single_jenkins_pipeline,
                    namespace,
                    full_project_name,
                    bug
                )
                # 存储映射关系
                future_to_bug_id[future] = bug.work_item_id

            # 处理完成的任务
            success_count = 0
            timeout_seconds = 12600
            for future in concurrent.futures.as_completed(future_to_bug_id):
                bug_id = future_to_bug_id[future]
                try:
                    # 获取结果（会重新抛出异常）
                    result = future.result(timeout=timeout_seconds)  # 超时 秒数
                    log.info(f"缺陷 {bug_id} 流水线执行结束，执行结果：{result}")
                    if result == 'SUCCESS':
                        success_count += 1
                except TimeoutError as e:
                    log.error(f"超时: 缺陷 {bug_id} - 超过 {timeout_seconds} 秒未完成: {e}")
                except ConnectionError as e:
                    log.error(f"网络错误: 缺陷 {bug_id} - {e}")
                except Exception as e:  # 兜底异常处理
                    log.error(f"未知错误: 缺陷 {bug_id} - {e}")

            # 进度统计
            success_rate = (success_count / len(bugs)) * 100
            log.info(f"流水线触发完成: 共 {len(bugs)} 个, 成功 {success_count} 个, 成功率: {success_rate:.1f}%")

    def main_process(self):
        """构建缺陷单并触发缺陷修复流水线 主流程
        """
        # 获取缺陷单列表
        bugs = self.rdc_handler.get_fix_datas(days=self.rdc_last_days, cross_workspaces=self.jks_cr_workspaces or [], rdc_appointed_to=self.rdc_appointed_to)
        # 触发缺陷单修复流水线
        self.trigger_multi_jenkins_pipeline(namespace=self.jks_namespace, full_project_name=self.jks_full_project_name,
                                            bugs=bugs, parallelism=self.jks_parallelism)


def run_rdc_pipeline_service_main():
    """
    启动示例：
    python3 src/application/services/repair_service/rdc_pipeline_service.py \
        --user-id "10155493" \
        --rdc-workspace "DAIP" \
        --rdc-decode "xxxxxxxxxxxxxxxx" \   # 这里为解密密钥，写死即可
        --rdc-last-days 15 \    # 可选 默认30
        --jenkins-token "11737e2ced7xxxxxxxxxxxxxxxxxxxxxxx" \
        --jenkins-uid "10155493" \
        --jenkins-url "https://cloudci.zte.com.cn" \   # 可选 默认https://cloudci.zte.com.cn
        --pg-token "3aexxxxxxxxxxxxxxx" \
        --jks-namespace "central-zxccd" \
        --jks-job-name "qfs_debug_pipelines" \
        --jks-parallelism 3 \   # 可选 默认3
        --jks-branch-name "repairAgent-RdcDefectRepair-debug" \
        --rdc-cr-workspaces "iRAI,iMOP,DAIP"
    """
    parser = argparse.ArgumentParser(
        description="构建缺陷单并触发缺陷修复流水线",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # RDC相关参数
    parser.add_argument('--rdc-uid', required=True, help='RDC接口使用. 启动RDC缺陷单检索的用户工号')
    parser.add_argument('--rdc-appointed-to', type=str, help='RDC接口使用. RDC缺陷单检索字段：当前处理人，多个工号用半角逗号分隔，如 10155493,10349517 ')
    parser.add_argument('--rdc-workspace', required=True, help='RDC接口使用. 工作区（即项目名称），如DAIP')
    parser.add_argument('--rdc-decode', required=True, help='RDC接口使用. RDC应用API码解密密钥')
    parser.add_argument('--rdc-last-days', type=int, default=30, help='RDC接口使用. 搜索缺陷单新建至当天的天数')
    parser.add_argument('--rdc-cr-workspaces', type=lambda s: s.split(','), required=True, help='RDC接口使用. 如果是多个工作区，用半角逗号分隔，如 iRAI,iMOP,DAIP')

    # PG相关参数
    parser.add_argument('--pg-host', default='**************', help='PG接口使用. PG数据库主机地址')
    parser.add_argument('--pg-port', type=int, default=65432, help='PG接口使用. PG数据库端口号')
    parser.add_argument('--pg-user', default='postgres', help='PG接口使用. PG数据库用户名')
    parser.add_argument('--pg-token', required=True, help='PG接口使用. PG数据库token')
    parser.add_argument('--pg-name', default='zero_agent', help='PG接口使用. PG数据库名称')

    # Jenkins相关参数
    parser.add_argument('--jenkins-token', required=True, help='Jenkins接口使用. 启动流水线的Jenkins用户API token')
    parser.add_argument('--jenkins-uid', required=True, help='Jenkins接口使用. 启动流水线的Jenkins用户工号')
    parser.add_argument('--jenkins-url', default='https://cloudci.zte.com.cn', help='Jenkins接口使用. Jenkins服务器URL地址')
    parser.add_argument('--jks-namespace', required=True, help='Jenkins接口使用. 启动流水线的Jenkins命名空间')
    parser.add_argument('--jks-full-project-name', required=True, help='Jenkins接口使用. 启动流水线的Jenkins 完整的项目名称，如 qfs_debug_pipelines/repairAgent-RdcDefectProcessor')
    parser.add_argument('--jks-parallelism', type=int, default=3, help='Jenkins接口使用. 启动流水线的Jenkins最大并发数')

    # 调试模式标志
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式，使用默认参数值覆盖部分设置')

    args = parser.parse_args()

    # 调试模式覆盖设置
    if args.debug:
        log4py = Log4py('local_files/log/rdc_pipeline_service_debug.log')
        logger = log4py.get_logger()
        logger.setLevel(log.DEBUG)  # 设置日志级别为DEBUG
    else:
        Log4py('local_files/log/rdc_pipeline_service.log')

    key = StringUtils.calculate_checksum('zero')[:16]
    value = StringUtils.value
    decode = args.rdc_decode
    sa = SimpleAEScryptor(key, 'CBC', decode)
    rdc_appcode = sa.decrypt(value)
    log.info(rdc_appcode[:4])
    value = StringUtils.value_pg
    decode = args.pg_token
    log.info(decode[:3])
    sa = SimpleAEScryptor(key, 'CBC', decode)
    pg_appcode = sa.decrypt(value)
    # 组装PG配置
    database_config = {
        "db_name": args.pg_name,
        "db_user": args.pg_user,
        "db_password": pg_appcode,
        "db_host": args.pg_host,
        "db_port": args.pg_port,
    }
    log.debug(f"DB config: {args.pg_user}@{args.pg_host}:{args.pg_port}-{args.pg_name}")
    # 初始化服务
    rps = RdcPipelineService(
        rdc_uid=args.rdc_uid,
        rdc_workspace=args.rdc_workspace,
        rdc_appcode=rdc_appcode,
        pg_config=database_config,
        jenkins_token=args.jenkins_token,
        jenkins_uid=args.jenkins_uid,
        jenkins_url=args.jenkins_url,
        jks_full_project_name=args.jks_full_project_name,
        jks_namespace=args.jks_namespace,
        rdc_last_days=args.rdc_last_days,
        rdc_cr_workspaces=args.rdc_cr_workspaces if args.rdc_cr_workspaces else None,
        rdc_appointed_to=args.rdc_appointed_to,
        jks_parallelism=args.jks_parallelism
    )
    rps.main_process()


if __name__ == '__main__':
    run_rdc_pipeline_service_main()
