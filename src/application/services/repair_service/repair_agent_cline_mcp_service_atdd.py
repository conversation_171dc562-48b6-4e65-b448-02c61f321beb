import asyncio
import re
import threading
import traceback
import uuid

from mcp.server.fastmcp import FastMCP

from src.application.services.repair_service.mcp_repair_agent_cline import Repair<PERSON>gent<PERSON>hain
from src.domain.common.zero_state import ZeroState
from src.domain.subdomains.repair_agent.action.cline_action_utils import construct_demonstration_message, \
    set_env_variable
from src.domain.subdomains.repair_agent.config.load_config import load_yaml_config
from loguru import logger
from asyncio import Queue
from typing import Dict, List, Any

from src.domain.subdomains.repair_agent.utils.model.dnstudio import ZTEAIM_3_0, DnStudio, NEBULACODER
from src.domain.subdomains.repair_agent.utils.model.openai import OpenAILlm
from src.domain.subdomains.repair_agent.utils.model.provider import ModelProvider

mcp = FastMCP("Repair Agent MCP Server")


def schema_to_json_with_comment(schema, indent=0):
    lines = []
    space = '  ' * indent
    if schema.get('type') == 'object':
        lines.append(space + '{')
        for k, v in schema.get('properties', {}).items():
            t = v.get('type', 'object')
            example = v.get('example')
            enum = v.get('enum')
            fmt = v.get('format')
            comment = []
            if enum:
                comment.append(f"枚举值: {enum}")
            if fmt:
                comment.append(f"格式: {fmt}")
            if example is not None:
                comment.append(f"示例: {example}")
            comment_str = " // " + "，".join(comment) if comment else ""
            if t == 'object' or 'properties' in v:
                lines.append(f'{space}  "{k}": {schema_to_json_with_comment(v, indent + 1)}{comment_str},')
            elif t == 'array':
                item = v.get('items', {})
                if item.get('type') == 'object' or 'properties' in item:
                    lines.append(f'{space}  "{k}": [{schema_to_json_with_comment(item, indent + 2)}]{comment_str},')
                else:
                    item_type = item.get("type", "object")
                    lines.append(f'{space}  "{k}": [{item_type}]{comment_str},')
            else:
                lines.append(f'{space}  "{k}": {t}{comment_str},')
        lines.append(space + '}')
        return '\n'.join(lines)
    elif schema.get('type') == 'array':
        item = schema.get('items', {})
        if item.get('type') == 'object' or 'properties' in item:
            return f'[{schema_to_json_with_comment(item, indent + 1)}]'
        else:
            return f'[{item.get("type", "object")}]'
    else:
        return schema.get('type', 'object')


def choose_prompt_template(test_info: List[Dict[str, Any]], re_list: List) -> str:
    """根据报错信息简单判断需要什么提示词模版"""

    def match_test_log(test_info_list, re_str, prompt_type_str):
        for testcase in test_info_list:
            stack_info = testcase.get("stack_info", None)
            if stack_info:
                if re.match(re_str, stack_info):
                    return prompt_type_str

            test_log = testcase.get("test_log", None)
            if test_log:
                for log in test_log:
                    log_level = log.get("level", None)
                    log_message = log.get("message", None)
                    if log_level is None or log_message is None:
                        continue

                    if log_level == "ERROR":
                        # 优先检测stack_info是否包含预期字段
                        if re.match(re_str, log_message):
                            return prompt_type_str

        return "default_user_prompt"

    for re_dict in re_list:
        prompt_type = match_test_log(test_info, re_dict["re_str"], re_dict["prompt_type"])
        if prompt_type != "default_user_prompt":
            return prompt_type

    return "default_user_prompt"


def prompt_combine(input_dict: dict) -> str:
    config = load_yaml_config("mcp_repair_default.yaml")
    workspace = input_dict.get("workspace", None)
    issue_classification = input_dict.get("problem_classification", None)
    if issue_classification is None:
        logger.error("`problem_classification` is not provided")
        raise Exception("`problem_classification` is not provided")
    if workspace is None:
        logger.error("`workspace` is not provided")
        raise Exception("`workspace` is not provided")

    if issue_classification == "业务代码问题":
        # 业务代码问题提示词拼装
        test_info = input_dict.get("test_info", [])
        if not test_info:
            logger.error("test_info is empty or not provided")
            raise Exception("test_info is empty or not provided")

        test_interface = input_dict.get("test_interface", None)
        if not test_interface:
            logger.error("test_interface is empty or not provided")
            raise Exception("test_interface is empty or not provided")

        return_status = ['200']
        testcase_str = ""
        prompt_template = "default_user_prompt"
        for num, testcase in enumerate(test_info, start=1):
            test_description = testcase.get("test_description", None)
            assert_expect = testcase.get("assert_expect", None)
            assert_actual = testcase.get("assert_actual", None)
            stack_info = testcase.get("stack_info", None)
            test_log = testcase.get("test_log", None)

            testcase_str += f"测试用例{num}: \n"

            if test_description is not None:
                test_description_str = ""
                for step, desc in enumerate(test_description, start=1):
                    if desc[0] == '#':
                        test_description_str += f"   {step}. {desc[1:]}\n"  # desc[1:] 用于排除描述的第一个`#`符号
                    else:
                        test_description_str += f"   {step}. {desc}\n"
                testcase_str += f"- 测试场景: \n{test_description_str}"

            if assert_expect is not None:
                testcase_str += f"- 预期结果: 返回状态码{assert_expect}\n"
                if assert_expect not in return_status:
                    return_status.append(assert_expect)

            if assert_actual is not None:
                testcase_str += f"- 实际结果: 返回状态码{assert_actual}\n"
                if assert_actual not in return_status:
                    return_status.append(assert_actual)

            testcase_str += "- 错误信息: "
            if assert_actual != assert_expect:
                testcase_str += f"Expected status code {assert_expect}, but got {assert_actual}. "
            if test_log and len(test_log) != 0:
                for log in test_log:
                    if log["level"] == "ERROR":
                        testcase_str += f"{log['message']}. "
            testcase_str += f"Response body: {stack_info}\n\n"

        change_info = input_dict.get("change_info", None)
        change_info_str = ""
        if change_info is not None:
            change_info_str = f"- 最近修改补丁: {change_info}\n"

        sql_error_s = r'[\s\S]*[qQ]uery.*failed'
        field_error_s = r'[a-zA-Z\s]*[Mm]issing.*field'
        re_list = [
            {
                "re_str": sql_error_s,
                "prompt_type": "sql_error_user_prompt"
            },
            {
                "re_str": field_error_s,
                "prompt_type": "field_error_user_prompt"
            }
        ]
        prompt_template = choose_prompt_template(test_info, re_list)

        prompt = config.get(prompt_template, "default_user_prompt")
        if prompt_template == "sql_error_user_prompt":
            # sql类问题
            user_input_prompt = prompt % {
                "testcases": testcase_str,
                "test_interface": test_interface,
                "workspace": workspace,
                "change_info": change_info_str
            }
        elif prompt_template == "field_error_user_prompt":
            # 字段缺失类问题
            success_field = input_dict.get("success_response", None)
            if not success_field:
                logger.error("success_response is empty or not provided")
                raise Exception("success_response is empty or not provided")

            error_fields = input_dict.get("error_response", None)

            success_field_str = schema_to_json_with_comment(success_field['200']['schema'])
            success_field_des = success_field['200']['description']
            response_field = f"- 200 ({success_field_des}):\n```json\n{success_field_str}\n```\n\n"

            if error_fields:
                for error_code, error_dict in error_fields.items():
                    error_des = error_dict.get('description', None)
                    error_schema = error_dict.get('schema', None)
                    if error_des is None:
                        continue

                    if error_schema:
                        # 有schema信息
                        error_filed_str = schema_to_json_with_comment(error_schema)
                        response_field += f"- {error_code} ({error_des}):\n```json\n{error_filed_str}\n```\n\n"
                    else:
                        # 无schema信息
                        response_field += f"- {error_code} ({error_des})\n\n"

            user_input_prompt = prompt % {
                "testcases": testcase_str,
                "response_field": response_field,
                "test_interface": test_interface,
                "workspace": workspace,
                "change_info": change_info_str
            }
        else:
            # 默认类问题
            user_input_prompt = prompt % {
                "testcases": testcase_str,
                "test_interface": test_interface,
                "workspace": workspace,
                "change_info": change_info_str
            }

        return user_input_prompt
    elif issue_classification == "测试脚本问题":
        # 测试脚本问题提示词拼装
        test_info = input_dict.get("test_info", None)

        prompt = """# 背景
现在有一个业务代码和该业务代码对应的Robot Framework的测试用例。当前在确保业务代码没有问题的情况下，发现测试用例存在运行不通过的情况，故需要对相关测试用例代码进行修正。其中，业务代码是以服务的方式提供相关功能。

Robot Framework测试用例通常包含`.robot`文件（主要测试文件）和相关的`.py`文件（如关键字库、资源文件、自定义函数等），这些文件共同构成完整的测试用例体系。

# 问题描述
"""
        if test_info is not None:
            if len(test_info) > 1:
                prompt += "本次输入包含多个测试脚本文件的问题，请**每次只聚焦于第一个测试脚本文件的问题进行分析和修复，修复完成后再处理下一个**。\n\n"
            for testcase in test_info:
                testcase_name = testcase.get('script_path', None)
                if testcase_name is None:
                    continue
                prompt += f"## 测试脚本文件{testcase_name}\n"

                prompt += "**错误堆栈信息**: "
                stack_info = testcase.get('stack_info', None)
                if stack_info is None or len(stack_info) == 0:
                    logger.error("`stack_info` is not provided or empty")
                    raise Exception(f"`stack_info` is not provided or empty, `stack_info`: {stack_info}")
                for info in stack_info:
                    prompt += f"{info['message']}\n"

                prompt += "\n"

                testcase_name = testcase.get('script_path', None)
                if testcase_name is None:
                    continue

        prompt += f"**测试用例所在路径**: {workspace}\n\n"
        prompt += f"# 修复任务\n请根据以上信息，在`{workspace}`工作空间中修复Robot Framework测试用例问题。\n\n"
        prompt += """## 问题分析步骤
1. **错误定位分析**: 从**错误堆栈信息**中识别具体的错误类型、错误位置和错误原因
2. **测试脚本验证**: 确认**测试脚本名称**对应的Python文件是否正确，检查该文件是否存在和可访问
3. **测试逻辑审查**: 分析测试用例中可能存在的语法错误、关键字使用错误或配置问题
4. **依赖关系检查**: 检查测试用例的依赖项、导入库、资源文件（.py文件）和环境配置是否正确
5. **根因定位**: 综合以上分析，确定导致Robot Framework测试用例运行失败的根本原因

## 修复指导原则
- **准确性**: 修复必须解决**错误堆栈信息**中反映的具体问题
- **针对性**: 基于错误类型和位置进行精准修复，避免引入新的问题
- **完整性**: 确保修复后的测试用例能够正常运行，包括所有依赖和环境要求
- **安全性**: 在**测试用例所在路径**中修复时，确保不影响其他相关测试文件
- **可维护性**: 修复方案要清晰易懂，便于后续维护和调试

## 代码修改示例与模式

### 修复模式1: 数据验证问题修复
**问题类型**: 输入参数验证不完整或逻辑错误
**修复模式**:
```python
# 使用正确的测试用例所需的python装饰器
from case_report import case_report

@case_report(enabled=True)
def TC_0001_api_main(envs, logger):
```
## 修复执行要求
1. **严格按照示例模式**: 参考上述修复模式，确保修复代码的结构和逻辑与示例一致
2. **保持代码风格**: 修复后的代码应与原代码保持一致的风格和命名规范
3. **添加必要注释**: 在关键修复点添加注释说明修复原因
4. **验证修复效果**: 确保修复后的代码能够产生**预期结果**
5. **逐步验证**: 修复完成后，验证代码在**测试场景**下的行为是否符合预期"""

        return prompt
    else:
        # 未定义的分类类型
        logger.error(f"invalid issue classification: {issue_classification}")
        raise Exception(f"invalid issue classification: {issue_classification}")


@mcp.tool()
async def fix_atomic(testcase_info: Dict) -> str:
    """使用Repair Agent对问题进行修复，返回值为修复问题的补丁"""
    trans_id = str(uuid.uuid4())
    threading.current_thread().uuid = trans_id
    username = "test"
    config = load_yaml_config("mcp_repair_default.yaml")
    demo_str = config.get("demonstration")
    if demo_str:
        demo = construct_demonstration_message(demo_str)
    else:
        demo = {}
    system_prompt_template = config.get("system_template", "")
    provided_tools = config.get("provided_tools", [])
    tool_params = config.get("tool_params", [])
    env_variables = config.get("env_variables", {})
    if env_variables:
        set_env_variable(env_variables)

    model_config = config.get('model', {})
    provider_name = model_config.get('provider_name', OpenAILlm.PROVIDER)
    model_name = model_config.get('model_name', NEBULACODER)
    api_url = model_config.get('api_url', '')
    api_key = model_config.get('api_key', '')
    model_alias = model_config.get('model_alias', '')

    llm = ModelProvider(provider_name).new_model(model_name=model_name, username=username, api_url=api_url,
                                                 api_key=api_key, model_alias=model_alias)

    try:
        # Initialize the repair agent and configurations
        repair_agent = RepairAgentChain(Queue())
        thread_config = {"configurable": {"thread_id": trans_id}}
        # Get the request data
        # Initialize the repair task state

        inputs = prompt_combine(testcase_info)
        workspace = testcase_info.get("workspace", None)
        if workspace is None:
            logger.error("`workspace` is not provided")
            raise Exception("`workspace` is not provided")

        repair_init_state = ZeroState(
            inputs=inputs,
            workspace=str(workspace),  # Store path as string
            trans_id=trans_id
        )

        # Invoke the repair agent
        graph = repair_agent.create_repair_agent(llm, system_prompt_template, provided_tools, tool_params,
                                                 demonstration=demo)
        repair_end_state = await graph.ainvoke(
            repair_init_state,
            config={"recursion_limit": 100, **thread_config}
        )

        # Extract the patch and trajectory from the result
        patch = repair_end_state.get('patch', '')
        trajectory = llm.trajectory()

        print(f"补丁内容：\n{patch}")

        return patch

    except Exception as e:
        logger.error(f"Error during repair process: {e}")
        logger.error(traceback.format_exc())

        # In case of an error, return an empty patch and traj with error information
        return ""


if __name__ == "__main__":
    mcp.run(
        transport="stdio"
    )

    # import json
    #
    # input_json_path = "/home/<USER>/workplace/zero_agent/attd/业务代码修复测试用例/2/input.json"
    # with open(input_json_path, "r") as file:
    #     input_data = json.load(file)

    # prompt组装测试
    # prompt = prompt_combine(input_data)
    # print(prompt)

  #   input_data = {
  #       "test_info": [
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?version=V11.22.*&model=modelA&page=1&pageSize=10\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a{}\uff0cheaders\u4e3a{}\uff0cquery\u4e3aversion=V11.22.*&model=modelA&page=1&pageSize=10\uff0cpath\u4e3a",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a200\uff0c\u8fd4\u56de\u5305\u542bV11.22.333\u7684\u5217\u8868"
  #               ],
  #               "assert_expect": "200",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Version V11.22.333 not found in response"
  #                   }
  #               ],
  #               "stack_info": "{'total': 0, 'page': 1, 'pagesize': 10, 'versions': []}",
  #               "script_path": "TC_0002_api_main.py"}
  #           ,
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?model=modelA&version=V11.22.333&page=1&pageSize=10\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a${None}\uff0cheaders\u4e3a${None}\uff0cquery\u4e3amodel=modelA&version=V11.22.333&page=1&pageSize=10\uff0cpath\u4e3a${None}",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a200"
  #               ],
  #               "assert_expect": "200",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Missing required fields in response"
  #                   }
  #               ],
  #               "stack_info": "{'total': 1, 'page': 1, 'pagesize': 10, 'versions': [{'uuid': 'a1cvxfvdx-e5f6-7890-g1h2-i3j4k5l6m7n8', 'type': 'humanoid', 'version': 'V11.22.333', 'compatibleModels': ['modelA'], 'desc': [{'lang': 'zh-CN', 'content': '\u7528\u4e8e\u56fa\u4ef6\u5347\u7ea7'}, {'lang': 'en-US', 'content': 'for firmware upgrade'}], 'sha256': 'eb16747742d0ba4e3a4edbbc23ef5adc7c04650abce73c4d3cc4d9fcc32a8e20', 'status': 'PUBLISHED', 'downloadUrl': ''}]}",
  #               "script_path": "TC_0003_api_main.py"
  #           },
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?model=modelA&version=V11.22.333&page=1&pageSize=1\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a{}\uff0cheaders\u4e3a{}\uff0cquery\u4e3amodel=modelA&version=V11.22.333&page=1&pageSize=1\uff0cpath\u4e3a",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a200\uff0c\u8fd4\u56de\u6570\u636e\u683c\u5f0f\u6b63\u786e"
  #               ],
  #               "assert_expect": "200",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Response missing required fields"
  #                   }
  #               ],
  #               "stack_info": "{'total': 1, 'page': 1, 'pagesize': 1, 'versions': [{'uuid': 'a1cvxfvdx-e5f6-7890-g1h2-i3j4k5l6m7n8', 'type': 'humanoid', 'version': 'V11.22.333', 'compatibleModels': ['modelA'], 'desc': [{'lang': 'zh-CN', 'content': '\u7528\u4e8e\u56fa\u4ef6\u5347\u7ea7'}, {'lang': 'en-US', 'content': 'for firmware upgrade'}], 'sha256': 'eb16747742d0ba4e3a4edbbc23ef5adc7c04650abce73c4d3cc4d9fcc32a8e20', 'status': 'PUBLISHED', 'downloadUrl': ''}]}",
  #               "script_path": "TC_0004_api_main.py"
  #           },
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?model=modelA&version=V11.22.333&page=1&pageSize=1000\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a${None}\uff0cheaders\u4e3a${None}\uff0cquery\u4e3amodel=modelA&version=V11.22.333&page=1&pageSize=1000\uff0cpath\u4e3a${None}",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a200\uff0c\u8fd4\u56de\u6570\u636e\u683c\u5f0f\u6b63\u786e"
  #               ],
  #               "assert_expect": "200",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Response missing required fields"
  #                   }
  #               ],
  #               "stack_info": "{'total': 1, 'page': 1, 'pagesize': 1000, 'versions': [{'uuid': 'a1cvxfvdx-e5f6-7890-g1h2-i3j4k5l6m7n8', 'type': 'humanoid', 'version': 'V11.22.333', 'compatibleModels': ['modelA'], 'desc': [{'lang': 'zh-CN', 'content': '\u7528\u4e8e\u56fa\u4ef6\u5347\u7ea7'}, {'lang': 'en-US', 'content': 'for firmware upgrade'}], 'sha256': 'eb16747742d0ba4e3a4edbbc23ef5adc7c04650abce73c4d3cc4d9fcc32a8e20', 'status': 'PUBLISHED', 'downloadUrl': ''}]}",
  #               "script_path": "TC_0005_api_main.py"
  #           },
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?model=modelA&version=V11.22.333&page=1&pageSize=10\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a{}\uff0cheaders\u4e3a{}\uff0cquery\u4e3amodel=modelA&version=V11.22.333&page=1&pageSize=10\uff0cpath\u4e3a{}",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a200\uff0c\u8fd4\u56de\u6570\u636e\u683c\u5f0f\u6b63\u786e"
  #               ],
  #               "assert_expect": "200",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Missing required fields in response"
  #                   }
  #               ],
  #               "stack_info": "{'total': 1, 'page': 1, 'pagesize': 10, 'versions': [{'uuid': 'a1cvxfvdx-e5f6-7890-g1h2-i3j4k5l6m7n8', 'type': 'humanoid', 'version': 'V11.22.333', 'compatibleModels': ['modelA'], 'desc': [{'lang': 'zh-CN', 'content': '\u7528\u4e8e\u56fa\u4ef6\u5347\u7ea7'}, {'lang': 'en-US', 'content': 'for firmware upgrade'}], 'sha256': 'eb16747742d0ba4e3a4edbbc23ef5adc7c04650abce73c4d3cc4d9fcc32a8e20', 'status': 'PUBLISHED', 'downloadUrl': ''}]}",
  #               "script_path": "TC_0008_api_main.py"
  #           },
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?model=modelA&version=V11.22.333&page=100&pageSize=10\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a{}\uff0cheaders\u4e3a{}\uff0cquery\u4e3amodel=modelA&version=V11.22.333&page=100&pageSize=10\uff0cpath\u4e3a",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a200"
  #               ],
  #               "assert_expect": "200",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Response missing required fields"
  #                   }
  #               ],
  #               "stack_info": "{'total': 0, 'page': 100, 'pagesize': 10, 'versions': []}",
  #               "script_path": "TC_0009_api_main.py"
  #           },
  #           {
  #               "test_description": [
  #                   "given \u5fae\u670d\u52a1\u90e8\u7f72\u6210\u529f",
  #                   "when \u53d1\u9001\u4e00\u4e2aGET\u8bf7\u6c42\u5230\u63a5\u53e3api/ability-store-service/v1/queryVersionPackages?model=modelA&version=V11.22.333&page=101&pageSize=10\uff0c\u8bf7\u6c42\u4f53\u643a\u5e26\u7684\u53c2\u6570\u5982\u4e0b\uff1abody\u4e3a${None}\uff0cheaders\u4e3a${None}\uff0cquery\u4e3amodel=modelA&version=V11.22.333&page=101&pageSize=10\uff0cpath\u4e3a${None}",
  #                   "then \u63a5\u53e3\u54cd\u5e94\u6b63\u786e\uff0c\u54cd\u5e94\u503c\u4e3a400"
  #               ],
  #               "assert_expect": "400",
  #               "assert_actual": "200",
  #               "test_log": [
  #                   {
  #                       "level": "ERROR",
  #                       "message": "Test failed. Expected status code 400 but got 200"
  #                   }
  #               ],
  #               "stack_info": "{'total': 0, 'page': 101, 'pagesize': 10, 'versions': []}",
  #               "script_path": "TC_0011_api_main.py"
  #           }
  #       ],
  #       "test_interface": "api/ability-store-service/v1/queryVersionPackages",
  #       "success_response": {"200": {"description": "\u7248\u672c\u5217\u8868", "schema": {"type": "object", "properties": {"total": {"type": "integer", "example": 15}, "page": {"type": "integer", "example": 2}, "pageSize": {"type": "integer", "example": 5}, "versions": {"type": "array", "items": {"type": "object", "properties": {"uuid": {"type": "string", "example": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8"}, "type": {"type": "string", "enum": ["humanoid"], "example": "humanoid"}, "version": {"type": "string", "example": "V01.02.003"}, "compatibleModels": {"type": "array", "items": {"type": "string"}, "example": ["modelA", "modelB"]}, "desc": {"type": "array", "items": {"type": "object", "properties": {"lang": {"type": "string", "example": "zh-CN"}, "content": {"type": "string", "example": "\u7528\u4e8e\u56fa\u4ef6\u5347\u7ea7"}}}}, "sha256": {"type": "string", "example": "50d858e0985ecc7f60418aaf0cc5ab587f42c2570a884095a9e8ccacd0f6545c"}, "downloadUrl": {"type": "string", "format": "url", "example": "https://cdn.example.com/download/humanoid-V01.02.003.zip?Expires=123456789&OSSAccessKeyId=abc123&Signature=xyz789"}}}}}}}}, "error_response": {"400": {"description": "\u65e0\u6548\u53c2\u6570"}}, "workspace": "/code/demo/ability-store", "problem_classification": "\u4e1a\u52a1\u4ee3\u7801\u95ee\u9898"
  # }
  #
  #   # 修复测试
  #   ret = asyncio.run(fix_atomic(input_data))
  #   print(ret)

    # input = "{'message': 'query version packages failed: query total count failed: ERROR: operator does not exist: jsonb @> text[] (SQLSTATE 42883)'}"
    # file_error_s = r'[\s\S]*[qQ]uery.*failed'
    # if re.match(file_error_s, input):
    #     print("yes")
