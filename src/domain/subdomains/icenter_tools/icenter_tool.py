import getpass
import re
import datetime
import logging as log
from typing import List
from src.domain.subdomains.icenter_tools.model.icenter_page import IcenterPage
from src.domain.subdomains.icenter_tools.rest_api_tool import RestApiTool
from src.domain.common.Log4py import Log4py
# icenter接口： https://i.zte.com.cn/#/shared/58d524cde3914c3ebd00042d4192a50e/wiki/page/2898579cfc9248cba997ac94aed778a7/view
__author__ = '魏然10155493'


class IcenterTool():
    ra_tool = None
    user_pwd = None

    def __init__(self, user_id, user_pwd=None) -> None:
        self.user_id = user_id
        if not user_pwd:
            self.user_pwd = getpass.getpass(f"Please input your password({self.user_id}): ")
        else:
            self.user_pwd = user_pwd
        self.update_token()

    def update_token(self):
        self.ra_tool = RestApiTool(self.user_id, self.user_pwd)

    # 页面内容读取
    def page_read(self, space_id, content_id):
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/{content_id}?spaceId={space_id}"
        content = self.ra_tool.get_api(url)
        return content.json()

    # 获取页面的所有子节点
    def get_page_sub_nodes(self, sid, cid):
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/subtree?contentId={cid}&spaceId={sid}&environmentType=browser&&hasDepth=false"
        content = self.ra_tool.get_api(url)
        return content.json()

    # 新建页面
    def post_create_new_page(self, sid, parent_cid, title):
        url = f'https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content?spaceId={sid}&parentId={parent_cid}'
        body = {
            "description": "",
            "employees": [],
            "groupKeyList": [],
            "parentId": parent_cid,
            "spaceId": sid,
            "title": title,
            "contentBody": f"<p>{title}</p>",
            "summary": title,
            "templateId": ""
        }
        # content.json()['bo']['id'] 为新建页面的id
        content = self.ra_tool.post_api(url, body=body)
        return content.json()

    def page_read_with_url(self, url):
        pat = r'/(\w+)/wiki/page/(\w+)'
        rst = re.compile(pat).findall(url)
        sid, cid = rst[0]
        print(f'[INFO] sid={sid}, cid={cid}')
        page_data = self.page_read(sid, cid)
        return page_data

    # 页面点赞
    def page_flower(self, space_id, content_id):
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-extend/content/{content_id}/flower/?t=timestamp&spaceId={space_id}&environmentType=browser"
        content = self.ra_tool.post_api(url)
        return content.json()

    # 页面转发到 zmail
    def page2zmail(self, cid, sid, emailTitle, postscript=None, commonMail=None, commonMailName=None, employees=None, ccEmployees=None, groups=[], ccGroups=[]):
        """
        body示例：
        {
            "commonMail": "<EMAIL>",
            "commonMailName": "测试专用公共邮箱",
            "employees": [
                {
                "employeeNo": "10244906",
                "employeeName": "刘新"
                }
            ],
            "ccEmployees": [
                {
                "employeeNo": "6092002767",
                "employeeName": "卢慧芳"
                }
            ],
            "groups": [],
            "ccGroups": [],
            "spaceId": "93fbce45733c45b3b79c659140b513b0",
            "postscript": "测试邮件",
            "emailTitle": "转发自iCenter的"kkk323"页面",
            "iframeHeight": "5000px",
            "jumpUrl": "https://i.zte.com.cn/#/shared/93fbce45733c45b3b79c659140b513b0/wiki/page/b1dac38cee9044da9492ecd28d8f94ef/view"
            }
        """
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/{cid}/shareContent/?spaceId={sid}"
        jumpUrl = f"https://i.zte.com.cn/#/shared/{sid}/wiki/page/{cid}/view"
        body = {
            "commonMail": commonMail,
            "commonMailName": commonMailName,
            "employees": employees,         # 列表类型，每个元素为字典，格式为  {"employeeNo": "00000000", "employeeName": "张三"}
            "ccEmployees": ccEmployees,     # 列表类型，每个元素为字典，格式为  {"employeeNo": "00000000", "employeeName": "张三"}
            "groups": groups,
            "ccGroups": ccGroups,
            "spaceId": sid,
            "postscript": postscript,
            "emailTitle": emailTitle,
            "iframeHeight": "5000px",   # 固定值
            "jumpUrl": jumpUrl
        }
        content = self.ra_tool.post_api(url, body=body)
        return content.json()

    # 页面转发到 icenter消息
    def page2msg(self, space_id, content_id, employee_No, employee_name=None):
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/{content_id}/msgForward/?spaceId={space_id}"
        body = {
            "employees": [{
                "employeeNo": str(employee_No),
                "employeeName": employee_name
            }],
            "groups": [],
            "groupKeyList": [],
            "spaceId": space_id,
            "link": {
                "linkPC": f"https://i.zte.com.cn/#/shared/{space_id}/wiki/page/{content_id}/view",
                "linkMobile": f"https://i.zte.com.cn/zte-rd-icenter-ssr/wiki/page/{content_id}/comments/0?fromType=MOA&spaceId={space_id}"
            }
        }
        content = self.ra_tool.post_api(url, body=body)
        return content.json()

    # 页面转发到 icenter消息（群组）
    def page2msg_group(self, space_id, content_id, group_id, group_name):
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/{content_id}/msgForward/?spaceId={space_id}"
        body = {
            "employees": [],
            "groups": [{
                "groupId": str(group_id),
                "groupName": group_name
            }],
            "groupKeyList": [],
            "spaceId": space_id,
            "link": {
                "linkPC": f"https://i.zte.com.cn/#/shared/{space_id}/wiki/page/{content_id}/view",
                "linkMobile": f"https://i.zte.com.cn/zte-rd-icenter-ssr/wiki/page/{content_id}/comments/0?fromType=MOA&spaceId={space_id}"
            }
        }
        content = self.ra_tool.post_api(url, body=body)
        return content.json()

    # 页面修改
    def page_update(self, sid, cid, content):
        # 获取页面版本号、标题
        json_data = self.page_read(sid, cid)
        title = json_data['bo']['title']
        currentVersion = json_data['bo']['currentVersion']
        # 修改页面
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/{cid}?spaceId={sid}"
        body = {
            "employees": [],
            "groups": [],
            "groupKeyList": [],
            "urlTemplate": "",
            "contentBody": content,
            "spaceId": sid,
            # "spaceName": "",
            "title": title,
            "currentVersion": currentVersion,
            # "summary": "测试环境的的数据，测试，测试测试"
        }
        response = self.ra_tool.post_api(url, body=body)
        return response.json()

    def page_sub_delete(self, sid: str, cid: str, exclude_titles: List[str] = None, exclude_keywords: List[str] = None, delta_days: int = 7) -> List[IcenterPage]:
        """子页面删除

        Args:
            sid (str): 父页面 sid
            cid (str): 父页面 cid
            exclude_titles (List[str], optional): 排除删除的页面标题列表. Defaults to None.
            exclude_keywords (List[str], optional): 排除删除的页面标题关键字. Defaults to None.
            delta_days (int, optional): 页面过期天数. Defaults to 7.

        Returns:
            List[IcenterPage]: 删除的页面列表
        """
        # 获取 子页面 列表
        info = self.get_page_sub_nodes(sid, cid)
        page_all: List[IcenterPage] = []    # 所有子页面
        page_del: List[IcenterPage] = []    # 待删除的页面列表
        # page_all = [IcenterPage(item['currentVersion'], sid, item['id'], item['title']) for item in info['bo']]
        for item in info['bo']:
            title = item['title']
            update_dt = datetime.datetime.strptime(item['currentVersion'], "%Y%m%d%H%M%S")
            sub_cid = item['id']
            page_all.append(IcenterPage(update_dt, sid, sub_cid, title))
        if not page_all:
            return page_del

        # 过滤 子页面
        cur_time = datetime.datetime.now()
        for page in page_all:
            # 排除页面过滤
            title = page.title
            if exclude_titles and title in exclude_titles:
                log.info(f'在标题排除列表中，该页面不删除：{title}')
                continue
            # 排除关键字过滤
            is_delete = True
            msg = ''
            if exclude_keywords:
                for key in exclude_keywords:
                    if key in title:
                        is_delete = False
                        msg = f'标题中存在排除关键字: "{key}"，该页面不删除'
                        break
            # 时间过期过滤
            if is_delete:
                time_diff = cur_time - page.cur_version
                if time_diff <= datetime.timedelta(days=delta_days):
                    is_delete = False
                    msg = f'页面在有效日期 {delta_days} 日内，该页面不删除'
            if is_delete:
                page_del.append(page)
            else:
                log.info(f"{msg}: {title}")

        # 删除 子页面
        # log.warning(f"These pages will be deleted: {page_del}")
        for page in page_del:
            title = page.title
            cid = page.cid
            sid = page.sid
            log.info(f'删除页面: {title}')
            self.ra_tool.delete_page(sid, cid)
        return page_del


def demo_page_read():
    # https://i.zte.com.cn/#/space/80efb5cf1135447e99b13184a80d5421/wiki/page/5fead0a75d5e44759ef03849805579d6/view
    # https://i.zte.com.cn/#/space/<----------space_id------------>/wiki/page/<----------content_id---------->/view
    space_id = "80efb5cf1135447e99b13184a80d5421"
    content_id = "5fead0a75d5e44759ef03849805579d6"
    space_id = "b3c97a2c11b14e63a8b585577b418fca"
    content_id = "694d299b292f4727b98c5fb5c7250836"
    space_id = "b3c97a2c11b14e63a8b585577b418fca"
    content_id = "33e2ef4559a7465b88ad776f3cd5b331"
    ito = IcenterTool("10155493")
    content = '''<p>【此页面由ZDSP站点信息导出，请勿手工修改1 asdf】</p>
            <p>信息行1a</p>
            <p>信息行2a</p>
            <p>信息行3a</p>
            '''
    # ito.page_update(space_id, content_id, content)
    # info = ito.page2zmail(content_id, space_id, postscript="这是邮件附言 2023年2月19日19:49:38 【一些信息Demo】", emailTitle="测试邮件-2023-2-19", employees=[{"employeeNo": "10155493"}])
    info = ito.page_read(space_id, content_id)
    print(info)
    # info = ito.page_flower(space_id, content_id)
    # info = ito.page2msg(space_id, content_id, "10300763", None)
    # info = ito.page2msg(space_id, content_id, "10244476", None)
    # info = ito.page2msg_group(space_id, content_id, "sip:<EMAIL>", "TopN扫描提效")
    # print(info['']['contentBody'])
    # print(info['']['contentBody'])
    pass


def demo_page_sub():
    # https://i.zte.com.cn/#/space/51b5ed25e6064fa89990389616b2e826/wiki/page/54398e6354bc4a06aabbdf36c35b5e09/view
    space_id = "51b5ed25e6064fa89990389616b2e826"
    space_id = "80efb5cf1135447e99b13184a80d5421"
    content_id = "25b4442a1a6140798b206105247d45e5"
    content_id = "0a32c98b60434fe29f18ec547e727e01"
    ito = IcenterTool("10155493")
    exclude_titles: List[str] = ['会议纪要2024年8月16日', '会议纪要2024年9月6日']
    exclude_keywords: List[str] = ['9月', 'java']
    ito.page_sub_delete(space_id, content_id, exclude_titles=exclude_titles, exclude_keywords=exclude_keywords, delta_days=14)
    # ito.page_update(space_id, content_id, content)
    # info = ito.page2zmail(content_id, space_id, postscript="这是邮件附言 2023年2月19日19:49:38 【一些信息Demo】", emailTitle="测试邮件-2023-2-19", employees=[{"employeeNo": "10155493"}])
    # info = ito.get_page_sub_nodes(space_id, content_id)
    # title = "页面标题"
    # for item in info['bo']:
    #     if title == item['title'].strip():
    #         cid = item['id']
    #         break
    # else:
    #     # 新建页面，并获取页面id
    #     content = ito.post_create_new_page(space_id, content_id, title)
    #     # content.json()['bo']['id'] 为新建页面的id
    #     print(content)
    #     pass


def icenter_group2name_lst(p_txt):
    """
    将icenter群聊导出的文本文件
    工号	中文	英文
    10151234	张三	Zhang San
    00123456	李四	Li Si
    转为邮件发送字符串
    张三10151234;李四00123456
    """
    pat = r'(\d+)\t+(\w+).*?'
    group_lst = []
    with open(p_txt) as f:
        for line in f:
            rst = re.compile(pat).findall(line)
            if rst:
                group_lst.append(rst[0][1] + rst[0][0])
    print(';'.join(group_lst))


if __name__ == '__main__':
    # p_txt = 'local_files/icenter_item_followup/output_group.lst'
    # icenter_group2name_lst(p_txt)
    # demo_page_read()
    # 配置日志
    Log4py('local_files/log/demo.log')
    demo_page_sub()
    print("all done, weiran, 2023-1-19 16:40:20")
