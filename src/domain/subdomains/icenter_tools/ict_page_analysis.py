import datetime
from lxml import etree
__author__ = '魏然10155493'


class SingleLineInfo():
    # 单独一行数据信息
    # line_date_lst = []  # 行日期列表
    # line_text_lst = []  # 行文字描述列表
    # line_item_lst = []  # 行checkbox列表，保存所有可能的 checkbox （考虑到一个单元格可能有多条 checkobx 情况）
    # line_uid = None     # 行负责人id
    # line_uname = None   # 行负责人name

    def __init__(self):
        self.line_date_lst = []  # 行日期列表
        self.line_text_lst = []  # 行文字描述列表
        self.line_item_lst = []  # 行checkbox列表，保存所有可能的 checkbox （考虑到一个单元格可能有多条 checkobx 情况）
        self.line_uid = None     # 行负责人id
        self.line_uname = None   # 行负责人name


class RowspanLineInfo():

    def __init__(self):
        self.cell_total = 0     # 单元格数
        self.cell_count = 0     # 单元格处理数
        self.rowspan_date_lst = []   # 多行合并的日期
        self.rowspan_text_lst = []   # 多行合并的文本
        self.rowspan_num = None      # 合并行数
        self.multi_count = 0   # 在一个处理多行合并情况下的行数计数器


class IctPageAnalysis():
    item_info_lst = []  # 最终输出任务列表
    error_dct_lst = []      # 解析异常返回值列表 [{"page_url": xxx, "page_desc": xxx, "msg": xxx, "table_title": xxx}]
    dt_today = None
    hdata = None

    def __init__(self, page_data):
        self.item_info_lst = []  # 最终输出任务列表
        self.error_dct = {}
        self.dt_today = datetime.datetime.now()
        self.hdata = etree.HTML(page_data)

    def analysis_simple_checkbox(self, page_data):
        # 目前暂不支持页面单独出现的任务项，只解析表格或提醒宏里面的项目项
        pass

    def icenter_table2json(self, header_lst, header_selected=[]):
        # 将 icenter 页面的表格转为 json 输出
        # header_lst: 表头字段列表，用于定位待解析的表格，只有表格的表头包含全部的 header_lst 字段才会被解析
        # header_selected: 读取的到json里面的字段，如果为空则全部读取
        # 注意：表格不能包含合并行或内嵌表格！
        pass

    def __analysis_inline_task_list(self, elem_html):
        items_info = []
        # 处理 inline-task-list checkbox 数据
        for inline_item in elem_html.xpath('.//ul[contains(@class, "inline-task-list")]/li[@data-inline-task-id and not(contains(@class, "checked"))]'):
            # 获取用户信息
            item_unames = inline_item.xpath('.//a[contains(@class, "at-user")]/@data-employeename')
            item_uids = inline_item.xpath('.//a[contains(@class, "at-user")]/@data-employeeid')
            uname = item_unames[-1] if item_unames else None    # 约定，最后出现的工号信息为责任人
            uid = item_uids[-1] if item_uids else None          # 约定，最后出现的工号信息为责任人
            # 获取描述信息
            content_lst = None
            cur_text_lst = inline_item.xpath('.//text()')
            if cur_text_lst:
                content_lst = [txt.replace('\ufeff', '').replace('\xa0', '').strip() for txt in cur_text_lst]
                content_lst = [txt for txt in content_lst if txt]   # 去除空串
            content = ' '.join(content_lst) if content_lst else None
            # 获取日期信息
            days = None
            str_deadline = None
            cur_date_lst = []
            str_dates = inline_item.xpath('.//time/text()')
            if str_dates:
                cur_date_lst += [datetime.datetime.strptime(date, '%Y-%m-%d') for date in str_dates]
                dt_deadline = min(cur_date_lst) if cur_date_lst else None
                str_deadline = dt_deadline.strftime('%Y-%m-%d')
                days = (dt_deadline - self.dt_today).days

            # 获取checkbox 信息，以下内容均可以为 None，由本行其它单元格信息填充
            item_info = {
                "uid": uid,
                "uname": uname,
                "content": content,    # 工作项说明文本，可以为 None
                "deadline": str_deadline,
                "delta_days": days
            }
            items_info.append(item_info)
        return items_info

    def __analysis_table_merge_cell(self, td, rli, max_cols):
        # 合并行分析（不允许单独出现工号和checkbox）
        # 合并行的单元格一般用来获取 公共描述信息、日期
        # 获取合并行数
        rowspan_num = int(td.xpath('./@rowspan')[0])
        if rowspan_num == 12:
            print(rowspan_num)
        if not rli.cell_total:
            rli.cell_total = max_cols * rowspan_num     # 全部单元格数目
            rli.rowspan_num = rowspan_num
            print(f"[IFNO] 本次处理合并行 {rowspan_num}行 * {max_cols}列 == {rli.cell_total}")
        rli.cell_count += rowspan_num     # 相当于本次处理了 rowspan_num 个单元格
        # 获取合并单元格日期（单元格中仅有一个日期字段时有效）
        str_dates = td.xpath('./time/text()')
        if str_dates:
            rli.rowspan_date_lst.append(datetime.datetime.strptime(str_dates[0], '%Y-%m-%d'))
        else:
            # 获取合并单元格文本
            cur_text_lst = []
            cur_text_lst += td.xpath('./text()')
            cur_text_lst += td.xpath('.//*/text()')
            if cur_text_lst:
                rli.rowspan_text_lst += [txt.replace('\\ufeff', '').replace('\xa0', '').strip() for txt in cur_text_lst]

    def __analysis_table_single_cell(self, sli, td):
        # 非合并字段处理
        has_unchecked = False
        # 处理 checkbox 数据
        items_info = self.__analysis_inline_task_list(td)
        if len(items_info) > 0:
            sli.line_item_lst += items_info
            has_unchecked = True
        if has_unchecked:
            return 0

        # 处理单独一个工号情形，则此工号为该行默认责任人（如果一行多个，则以最后一个出现的为准）
        ele_at_user = td.xpath('./a[contains(@class, "at-user")]')
        if ele_at_user:
            sli.line_uid = ele_at_user[0].xpath('./@data-employeeid')[0]
            sli.line_uname = ele_at_user[0].xpath('./@data-employeename')[0]
            return 0

        # 处理单独一个日期情形，则将此日期加入公共日期列表
        str_dates = td.xpath('./time/text()')
        if str_dates:
            sli.line_date_lst += [datetime.datetime.strptime(date, '%Y-%m-%d') for date in str_dates]

        # 处理说明字段处理
        # tds_html[3].xpath('./text()')[0].replace('\\ufeff\n', '').strip()
        # cur_text_lst = td.xpath('.//*[not(self::ul) and not(ancestor::ul)]/text()')     # 此表达式由 chatgpt 提供
        cur_text_lst = td.xpath('.//text()')
        if cur_text_lst:
            tmp_lst = [txt.replace('\ufeff', '').replace('\xa0', '').strip() for txt in cur_text_lst]
            sli.line_text_lst.append(' '.join([txt for txt in tmp_lst if txt]))
        return 0

    def __get_valid_items(self, sli, rli):
        # line_date_lst 行日期列表
        # line_text_lst 行文字描述列表
        # line_item_lst 行checkbox列表，保存所有可能的 checkbox （考虑到一个单元格可能有多条 checkobx 情况）
        # line_uid      行负责人id
        # line_uname    行负责人name
        # item_info = {
        #         # "types": [],      # 发送邮件还是消息，由策略函数处理，本处地方不做判定，只提供工作项基础信息
        #         # "sid": "xxxxxx",
        #         # "cid": "xxxxxx",    # page_title: page_data['bo']['title']
        #         "uid": item_uid,
        #         "uname": item_name,
        #         "content": content,    # 工作项说明文本
        #         "deadline": str_deadline,
        #         "delta_days": days,
        #         # "postscript": "xxxxxxxxxxx",
        #         # "emailTitle": "xxxxxxxxxxx",
        # }
        for item_info in sli.line_item_lst:
            if not item_info['uid']:
                if sli.line_uid:
                    item_info['uid'] = sli.line_uid
                    item_info['uname'] = sli.line_uname
                else:
                    continue

            if not item_info['deadline']:
                if rli.rowspan_date_lst or sli.line_date_lst:
                    dt_deadline = min(rli.rowspan_date_lst + sli.line_date_lst)
                    item_info['deadline'] = dt_deadline.strftime('%Y-%m-%d')
                    item_info['delta_days'] = (dt_deadline - self.dt_today).days
                else:
                    continue

            if not item_info['content']:
                item_info['content'] = '-'.join(rli.rowspan_text_lst + [txt for txt in sli.line_text_lst if txt])

            self.item_info_lst.append(item_info)
            print(item_info)
        # end for item_info in line_item_lst:

    def analysis_icenter_followup_content(self):
        # 解析icenter 页面的跟催目录表格
        header_sample = {"跟催页面", "告警/天"}
        followup_page_lst = []
        for table_html in self.hdata.xpath('//tbody'):
            # 获取表头列表
            table_headers = table_html.xpath('./tr[@class="firstRow"]//text()')
            if not header_sample <= set(table_headers):
                # 通过表头判定是否为跟催目录表格
                print(table_headers)
                continue
            # 获取普通行
            for html_line in table_html.xpath('./tr[not(@class)]'):
                line_item_dct = {"cid": None}
                html_cells = html_line.xpath('./td')
                for n, field in enumerate(table_headers):
                    if field == "跟催页面":
                        if len(html_cells[n].xpath('./a/@_contentid')) == 0:
                            break
                        page_info = {
                            "sid": html_cells[n].xpath('./a/@_spaceid')[0],
                            "cid": html_cells[n].xpath('./a/@_contentid')[0],
                            "title": html_cells[n].xpath('./a/@title')[0]
                        }
                        line_item_dct["sid"] = page_info["sid"]
                        line_item_dct["cid"] = page_info["cid"]
                        line_item_dct["title"] = page_info["title"]
                        pass
                    elif field == "超期抄送":
                        employees = []
                        # 获取用户信息
                        for xml_employees in html_cells[n].xpath('.//a[contains(@class, "at-user")]'):
                            employee = {
                                "ename": xml_employees.xpath('./@data-employeename')[0],
                                "eid": xml_employees.xpath('./@data-employeeid')[0]
                            }
                            employees.append(employee)
                            pass
                        line_item_dct['ccEmployees'] = employees
                        pass
                    else:
                        line_item_dct[field] = ' '.join(html_cells[n].xpath('.//text()'))
                # end for n, field in enumerate(table_headers):
                if line_item_dct['cid']:
                    followup_page_lst.append(line_item_dct)
            # end for html_line in table_html.xpath('./tr[not(@class)]'):
            return followup_page_lst
            pass

        pass

    def analysis_table_checkbox(self):
        # 解析表格中的工作项
        for table_html in self.hdata.xpath('//tbody'):
            # 对每个表格逐一分析
            # 表头，用于异常分析
            table_headers = None
            try:
                # 查看该表格列数
                max_cols = len(table_html.xpath('./tr[@class="firstRow"]/th'))

                # 合并行参数初始化
                # 仅针对多行时的处理有效
                rli = RowspanLineInfo()

                # 对每行进行分析
                for line_html in table_html.xpath('./tr'):
                    # 行处理
                    # 判断是否处理合并行
                    if not rli.cell_total:
                        rli = RowspanLineInfo()
                    # 判定是否合并行处理完成
                    elif rli.cell_total == rli.cell_count:
                        assert rli.multi_count == rli.rowspan_num, f"处理逻辑出问题 {rli}"
                        # 当前合并行处理完成，重置
                        rli = RowspanLineInfo()

                    # 单行初始化
                    # line_date_lst = []  # 行日期列表
                    # line_text_lst = []  # 行文字描述列表
                    # line_item_lst = []  # 行checkbox列表，保存所有可能的 checkbox （考虑到一个单元格可能有多条 checkobx 情况）
                    # line_uid = None     # 行负责人id
                    # line_uname = None   # 行负责人name
                    sli = SingleLineInfo()

                    # 表头行处理: tr(行) 下面就是 th（列）
                    ths_html = line_html.xpath('./th')
                    cur_cols = len(ths_html)    # 获得列元素（即单元格）个数
                    if max_cols and cur_cols == max_cols:
                        table_headers = line_html.xpath(".//text()")
                        print("---- Table -----")
                        print(f"[Table Headers] {table_headers}")
                        continue

                    # 普通行处理:  tr(行) 下面就是 td（列）
                    tds_html = line_html.xpath('./td')
                    # 具体分2种情况：
                    # 1）单独一行（td中无rowspan属性）
                    # 2）多行的合并行（td中有rowspan属性）
                    # 对于每一行的【单元格】处理
                    for td in tds_html:
                        # 说明字段和日期字段可以为合并字段
                        # 工号字段和checkbox字段不可为合并字段
                        if td.xpath('./@rowspan'):
                            # 合并行分析（不允许单独出现工号和checkbox）
                            # 合并行的单元格一般用来获取 公共描述信息、日期
                            self.__analysis_table_merge_cell(td, rli, max_cols)
                        else:
                            # 非合并字段处理
                            self.__analysis_table_single_cell(sli, td)
                            rli.cell_count += 1
                        pass
                    # end for td in tds_html:   # 一行的所有列（单元格）分析完成

                    # 检查是否同时有 工号属性 和 checkbox属性（未选中），如果有则存一条
                    if sli.line_item_lst:
                        self.__get_valid_items(sli, rli)
                    rli.multi_count += 1
                # end for line_html in table_html.xpath('./tr'):    # 表格所有行分析完成
            # end for table_html in hdata.xpath('//tbody'):     # 该页面所有表格分析完成
            except Exception as err:
                print('=============> 处理表格出错！')
                print(f"出错表格所在表头信息: {table_headers}")
                err_dct = {
                    "msg": str(err),
                    "table_headers": table_headers
                }
                self.error_dct_lst.append(err_dct)
                # todo 发邮件给持续启动人员
                print(err)
        pass

    def analysis_macro_checkbox(self):
        for macro_html in self.hdata.xpath('//div[@class="insert-macro"]'):
            # 对每个宏进行解析
            items_info = self.__analysis_inline_task_list(macro_html)
            # 将解析到的任务项加入总列表
            self.item_info_lst += items_info
            pass
        pass

    def main_process(self):
        # 清空任务项
        self.item_info_lst = []
        self.error_dct_lst = []
        # 获取表格中的任务项
        self.analysis_table_checkbox()
        # 获取宏中的任务项
        self.analysis_macro_checkbox()
        # 返回解析错误的表格
        return self.error_dct_lst


if __name__ == "__main__":
    # with open('data/icenter_checkbox_all.html') as f:
    with open('local_files/icenter_item_followup/icenter_table2json.html', encoding='utf-8') as f:
        page_data = f.read()
    ipa = IctPageAnalysis(page_data)
    ipa.analysis_icenter_followup_content()
    print('2023-2-23 23:56:03')
