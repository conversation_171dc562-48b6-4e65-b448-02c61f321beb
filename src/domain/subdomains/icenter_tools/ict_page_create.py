from lxml import etree
__author__ = '魏然10155493'

TEMPLATE_DIR = 'src/domain/subdomains/icenter_tools/template'


class IctPageCreate:
    def build_table_header(self, headers):
        # 组装表头行
        attrib = {
            'class': 'firstRow'
        }
        xml_header = etree.Element("tr", attrib=attrib)

        # 插入表头字段-行号
        attrib_child = {
            'class': 'line-number',
            'data-sort-type': 'reversebyasc',
        }
        child = etree.Element("th", attrib=attrib_child)
        child.text = '#'
        xml_header.insert(1, child)

        # 插入表头字段-其它字段
        attrib_child = {
            'valign': 'top',
            'data-sort-type': 'orderbyasc',
        }
        # 注意从第二列开始，第一列是行号
        for n, h in enumerate(headers, 2):
            child = etree.Element("th", attrib=attrib_child)
            child.text = h
            xml_header.insert(n, child)

        return xml_header

    def build_table_cell_text(self, text):
        xml_cell = etree.Element("td")
        xml_cell.text = str(text)
        return xml_cell

    def build_table_cell_employee_lst(self, employee_lst):
        xml_cell = etree.Element("td")
        attrib_child = {
            'href': None,
            'contenteditable': 'false',
            # 'class': "at-user active",
            'class': "at-user",
            'data-employeename': None,
            'data-employeeid': None,
            # 'data-uuid': None,
            'target': "_blank",
            "rel": "noopener"
        }
        for n, employee in enumerate(employee_lst, 1):
            uid = employee['employeeNo']
            uname = employee['employeeName']
            attrs = attrib_child.copy()
            attrs['href'] = f"/#/profile/{uid}"
            attrs['data-employeeid'] = uid
            attrs['data-employeename'] = uname
            child = etree.Element("a", attrib=attrs)
            child.text = f"{uname}{uid}"
            xml_cell.insert(n, child)
        return xml_cell

    def build_table_cell_icenter_url(self, icenter_url):
        xml_cell = etree.Element("td")
        cid = icenter_url['cid']
        sid = icenter_url['sid']
        title = icenter_url['title']
        attrib_child = {
            'href': f"/#/space/{sid}/wiki/page/{cid}/view",
            '_spaceid': sid,
            '_contentid': cid,
            '_contenttitle': title,
            'title': title,
            'target': "_blank",
            'rel': "noopener"
        }
        child = etree.Element("a", attrib=attrib_child)
        child.text = title
        xml_cell.insert(1, child)
        return xml_cell

    def build_table_cell_url(self, data):
        # 构建普通 URL 链接
        xml_cell = etree.Element("td")
        url = data['url']
        title = data['title']
        attrib_child = {
            'href': url,
            'textvalue': title,
            'title': title,
            'target': "_blank",
            'rel': "noopener"
        }
        child = etree.Element("a", attrib=attrib_child)
        child.text = title
        xml_cell.insert(1, child)
        return xml_cell

    def build_table_cell_icenter_date(self, icenter_date):
        xml_cell = etree.Element("td")
        date = icenter_date
        attrib_child = {
            'contenteditable': 'false',
            'class': 'icenter-macro-task_list non-editable',
        }
        child = etree.Element("time", attrib=attrib_child)
        child.text = date
        xml_cell.insert(1, child)
        return xml_cell

    def build_icenter_table(self, data_lst):
        """
        [
            {
                "key1_text": {
                    "type": "text",
                    "content": "this is text",
                },
                "key2_employee_lst": {
                    "type": "ict_user",
                    "content": [
                        {"uid": "user id", "uname": "user name"}
                    ],
                },
                "key3_icenter_url": {
                    "type": "ict_url",
                    "content":{
                        "cid": "cf673f3c99c2481c91cc71474a171455",
                        "sid": "b3c97a2c11b14e63a8b585577b418fca",
                        "title": "this ia title"
                    }
                },
                "key4_icenter_date": {
                    "type": "ict_date",
                    "content": "2023-08-10",
                }
            },
        ]
        """
        # 加载 icenter 表格 页面模板
        xml_table_root = None
        with open(f'{TEMPLATE_DIR}/icenter_table.xml', encoding='utf-8') as f:
            xml_table_root = etree.XML(f.read())
        xml_table = xml_table_root.xpath("//tbody")[0]
        # 建立表头
        xml_header = self.build_table_header(data_lst[0].keys())
        xml_table.insert(1, xml_header)
        # 插入行数据
        for row, line_data in enumerate(data_lst, 2):
            xml_line = etree.Element("tr")
            # 插入行号
            attrib_child = {
                'class': 'line-number',
                'contenteditable': 'false',
            }
            xml_cell = etree.Element("td", attrib=attrib_child)
            xml_cell.text = str(row - 1)    # 数据行从第 2 行开始，但是行号从 1 计数
            xml_line.insert(0, xml_cell)

            # 插入其它列的字段值（注意因为有行号，列从1开始计数）
            for col, v in enumerate(line_data.values(), 1):
                xml_cell = None
                if not isinstance(v, dict):
                    # 如果非字典类型，则一律转为字符串（默认类型）
                    xml_cell = self.build_table_cell_text(v)
                elif v['type'] == 'text':
                    xml_cell = self.build_table_cell_text(v['content'])
                elif v['type'] == 'ict_user':
                    xml_cell = self.build_table_cell_employee_lst(v['content'])
                elif v['type'] == 'ict_url':
                    xml_cell = self.build_table_cell_icenter_url(v['content'])
                elif v['type'] == 'ict_date':
                    xml_cell = self.build_table_cell_icenter_date(v['content'])
                elif v['type'] == 'url':
                    xml_cell = self.build_table_cell_url(v['content'])
                else:
                    raise Exception("非法的类型")
                xml_line.insert(col, xml_cell)
            xml_table.insert(row, xml_line)
        return xml_table_root

    def create_dashboard_page_simple(self, data_lst):
        """构建表格页面简单版
        注意，仅构建一个表格的页面

        Args:
            data_lst (_type_): icenter数据格式页面

        Returns:
            _type_: _description_
        """
        # 加载 icenter wiki 页面模板
        html_root = None
        with open(f'{TEMPLATE_DIR}/icenter_wiki_page.html', encoding='utf-8') as f:
            html_root = etree.HTML(f.read())
        xml_table = self.build_icenter_table(data_lst)
        html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_table)
        return etree.tostring(html_root).decode('utf-8')

    def create_icenter_followup_page(self, data_lst, wb_config):
        # 加载 icenter wiki 页面模板
        html_root = None
        with open(f'{TEMPLATE_DIR}/icenter_wiki_page.html', encoding='utf-8') as f:
            html_root = etree.HTML(f.read())
        with open(f'{TEMPLATE_DIR}/icenter_html_elems.xml', encoding='utf-8') as f:
            # 插入配置页面URL
            str_elems = f.read()
            cid = wb_config['stream_url_cid']
            sid = wb_config['stream_url_sid']
            ctitle = wb_config['stream_url_ctitle']
            str_elems = str_elems.replace('__CID__', cid).replace("__SID__", sid).replace("__CTITLE__", ctitle)
            ict_url_xml = etree.HTML(str_elems).xpath('//a[@_contentid]')[0]
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(ict_url_xml)
        if data_lst:
            # 插入表格（最多只插入500条）
            issue_count = len(data_lst)
            if issue_count > 500:
                # 若超过500条漏洞，则输出提示语
                xml_string = etree.fromstring(f'<p> 漏洞数目过多，共 {issue_count} 条，仅记录前 500 条</p>')
                html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_string)
            xml_table = self.build_icenter_table(data_lst[:500])
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_table)
        else:
            # 若无漏洞，则输出提示语
            xml_string = etree.fromstring('<p> 无该级别漏洞 </p>')
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_string)
        return etree.tostring(html_root).decode('utf-8')

    def create_icenter_table_simple_page(self, data_lst, page_cfg):
        # 加载 icenter wiki 页面模板
        html_root = None
        with open('icenter_tools/template/icenter_wiki_page.html', encoding='utf-8') as f:
            html_root = etree.HTML(f.read())
        with open(f'{TEMPLATE_DIR}/icenter_html_elems.xml', encoding='utf-8') as f:
            # 插入配置页面URL
            str_elems = f.read()
            cid = page_cfg['src_stream_url_cid']
            sid = page_cfg['src_stream_url_sid']
            ctitle = page_cfg['src_stream_url_ctitle']
            str_elems = str_elems.replace('__CID__', cid).replace("__SID__", sid).replace("__CTITLE__", ctitle)
            ict_url_xml = etree.HTML(str_elems).xpath('//a[@_contentid]')[0]
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(ict_url_xml)
        if data_lst:
            # 插入表格（最多只插入500条）
            issue_count = len(data_lst)
            issue_desc = page_cfg['issue_desc']
            if issue_count > 500:
                # 若超过500条数据，则输出提示语
                xml_string = etree.fromstring(f'<p> {issue_desc} 数目过多，共 {issue_count} 条，仅记录前 500 条</p>')
                html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_string)
            xml_table = self.build_icenter_table(data_lst[:500])
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_table)
        else:
            # 若无数据，则输出提示语
            xml_string = etree.fromstring(f'<p> {issue_desc} 数据为空 </p>')
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_string)
        return etree.tostring(html_root).decode('utf-8')

    def create_icenter_single_table_page(self, data_lst):
        # 构建单个 table 的页面
        # 加载 icenter wiki 页面模板
        html_root = None
        with open(f'{TEMPLATE_DIR}/icenter_wiki_page.html', encoding='utf-8') as f:
            html_root = etree.HTML(f.read())
        if data_lst:
            # 插入表格（最多只插入500条）
            issue_count = len(data_lst)
            if issue_count > 500:
                # 若超过500条数据，则输出提示语
                xml_string = etree.fromstring(f'<p> 表格数据过多，共 {issue_count} 条，仅记录前 500 条</p>')
                html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_string)
            xml_table = self.build_icenter_table(data_lst[:500])
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_table)
        else:
            # 若无数据，则输出提示语
            xml_string = etree.fromstring('<p> 表格数据为空 </p>')
            html_root.xpath('//div[@class="wiki-preview-page"]')[0].append(xml_string)
        return etree.tostring(html_root).decode('utf-8')
