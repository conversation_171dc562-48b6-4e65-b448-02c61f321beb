from lxml import etree
__author__ = '魏然10155493'


class IctTableAnalysis():
    """
    支持4种类型的单元格内容解析，枚举如下：
    [
        {
            "type": "text",         # 普通文本，也是默认的类型
            "content": "这是解析出的普通文本"
        }, {
            "type": "ict_user",     # icenter 页面中的用户（列表形式）
            "content": [
                {'employeeNo': "10155493", 'employeeName": "魏然"},
            ]
        }, {
            "type": "ict_url",      # icenter 页面中的URL
            "content": {'sid': "b3c97a2c11b14e63a8b585577bxxxxxx", 'cid': "877d8adc3da440a0b681d3eacxxxxxx", 'title': "测试页面"}
        }, {
            "type": "ict_date",     # icenter 页面日期
            "content": "2023-08-10"
        }
    ]
    """

    def analysis_cell(self, html_cell):
        cell_info = {
            "type": None,
            "content": None,
        }
        # 是否为 icenter 页面中的URL
        if html_cell.xpath('./a/@_contentid'):
            cell_info['type'] = 'ict_url'
            cell_info['content'] = {
                "sid": html_cell.xpath('./a/@_spaceid')[0],
                "cid": html_cell.xpath('./a/@_contentid')[0],
                "title": html_cell.xpath('./a/@title')[0]
            }
        # 是否为 icenter icenter 页面日期
        elif html_cell.xpath('./time'):
            cell_info['type'] = 'ict_date'
            cell_info['content'] = html_cell.xpath('./time/text()')[0]
        # 是否为 icenter icenter 页面中的用户（列表形式）
        elif html_cell.xpath('.//a[contains(@class, "at-user")]'):
            cell_info['type'] = 'ict_user'
            employees = []
            # 获取用户信息
            for xml_employees in html_cell.xpath('.//a[contains(@class, "at-user")]'):
                employee = {
                    "employeeName": xml_employees.xpath('./@data-employeename')[0],
                    "employeeNo": xml_employees.xpath('./@data-employeeid')[0]
                }
                employees.append(employee)
            cell_info['content'] = employees
        # 普通文本处理
        else:
            cell_info['type'] = 'text'
            cell_info['content'] = ' '.join(html_cell.xpath('.//text()'))
        return cell_info

    def analysis_table(self, table_html, extract_headers=None):
        rst_info = {
            "code": 0,
            "data": None
        }
        rst_data = []
        # 获取表头字段列表
        table_headers = table_html.xpath('./tr[@class="firstRow"]//text()')
        # 检验表头是否包含全部待选取的表头字段
        if extract_headers and not (set(extract_headers) <= set(table_headers)):
            rst_info['code'] = -1
            rst_info['data'] = f"待选取的字段多于表头字段 {table_headers} ---- {extract_headers}"
            return rst_info
        # 非表头行  解析
        for html_line in table_html.xpath('./tr[not(@class)]'):
            line_item_dct = dict()
            html_cells = html_line.xpath('./td')
            for n, field in enumerate(table_headers):
                if extract_headers and (field not in extract_headers):
                    continue
                cell_info = self.analysis_cell(html_cells[n])
                line_item_dct[field] = cell_info
            rst_data.append(line_item_dct)
        return rst_data
        pass

    def search_table(self, page_data, sample_hearders, extract_headers=None):
        """
        检索icenter页面的表格，并以字典形式返回表格信息（类似csv的字典读取）
        page_data: icenter页面的代码文本
        sample_hearders: 需要分析的表格表头样例（会返回第一个包含匹配的表格）
        extract_headers: 需要返回的表头字段（为空则返回所有字段信息）
        """
        page_html = etree.HTML(page_data)
        for table_html in page_html.xpath('//tbody'):
            # 获取表头列表
            table_headers = table_html.xpath('./tr[@class="firstRow"]//text()')
            if not set(sample_hearders) <= set(table_headers):
                # 通过表头判定是否为跟催目录表格
                print(table_headers)
                continue
            rst_data = self.analysis_table(table_html, extract_headers)
            return rst_data

    pass
