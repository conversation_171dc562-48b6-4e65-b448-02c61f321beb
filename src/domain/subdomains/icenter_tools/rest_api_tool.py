import socket
import requests
import urllib3
import hashlib
import json


class RestApiTool():
    headers = {
        'Content-type': 'application/json',
        'X-Emp-No': None, 'X-Auth-Value': None,
        'X-Lang-Id': 'zh_CN'
    }
    user_auth = None
    proxies = {
        "http": "",
        "https": ""
    }

    def __init__(self, user_id, user_pwd) -> None:
        urllib3.disable_warnings()  # [waring] 不打印警告信息，这行可注释掉以便于查看警告信息
        self.user_id = user_id
        self.user_pwd = user_pwd
        self.headers['X-Emp-No'] = user_id
        # self.user_auth = self.get_token()
        self.get_user_auth()
        pass

    # def check_auth_valid(self):
    #     ret = self.get_token()
        # if ret['code']['code'] == '0000' and ret['bo']['code'] == '0000' :
        #     self.headers['X-Auth-Value'] = ret['other']['token']
        #     # 摘要式身份认证
        #     self.user_auth = requests.auth.HTTPDigestAuth(self.user_id, self.headers['X-Auth-Value'])
        #     return True
        # else:
        #     return False

    def post_api(self, url, headers=None, body=None, files=None):
        if not headers:
            headers = self.headers
        data = json.dumps(body)
        response = requests.post(url, data=data, files=files, headers=headers, proxies=self.proxies, verify=False)
        return response
    pass

    def put_api(self, url, headers=None, body=None, files=None):
        if not headers:
            headers = self.headers
        data = json.dumps(body)
        response = requests.put(url, data=data, files=files, headers=headers, proxies=self.proxies, verify=False)
        return response

    def delete_page(self, sid, cid):
        url = f"https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/{cid}?spaceId={sid}"
        response = requests.delete(url, headers=self.headers, proxies=self.proxies, verify=False, timeout=60)
        return response

    def get_api(self, url):
        response = requests.get(url, auth=self.user_auth, headers=self.headers, proxies=self.proxies)
        if response.status_code != 200:
            print('UAC 认证失败，请检查密码是否正确!')
        return response

    def get_host_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*********', 80))
            ip = s.getsockname()[0]
        finally:
            s.close()
        return ip

    def get_user_auth(self):
        user_id = self.user_id
        user_pwd = self.user_pwd
        proxies = self.proxies

        loginsyscode = 'Portal'
        originsyscode = ''

        url = "https://uac.zte.com.cn/uaccommauth/auth/comm/verify.serv"
        clientip = self.get_host_ip()

        text =\
            {
                "account": user_id,
                "passWord": user_pwd,
                "loginClientIp": clientip,
                "loginSystemCode": loginsyscode,
                "originSystemCode": originsyscode,
                "other": {
                    "networkArea": '1',
                    "networkAccessType": '1'
                },
                "verifyCode": hashlib.md5(str(user_id+user_pwd+clientip+loginsyscode+originsyscode).encode(encoding='utf-8')).hexdigest()
            }
        headers = {'Content-type': 'application/json'}
        content = requests.post(url, data=json.dumps(text), headers=headers, proxies=proxies)
        if content.status_code != 200:
            print('error, retry!')
            # sleep(2)
            content = requests.post(url, data=json.dumps(text), headers=headers, proxies=proxies)
        ret = content.json()

        if ret['code']['code'] == '0000' and ret['bo']['code'] == '0000':
            # 获取 token
            self.headers['X-Auth-Value'] = ret['other']['token']
            # 摘要式身份认证
            self.user_auth = requests.auth.HTTPDigestAuth(self.user_id, self.headers['X-Auth-Value'])
            return True
        else:
            return False
