-- 缺陷修复关键信息查询表
-- 用途：从RDC缺陷单/变更活动单中获取 "belong_sub_system"、"discovery_iversion"、"system_iteration" 这3个字段的组合信息，来查找表中  "repository", "branch", "dev_image" 这3个字段信息
-- 创建时间：2024年
-- PostgreSQL版本：10.4

-- DROP TABLE public.defect_fix_repository_mapping;
-- 创建表
CREATE TABLE defect_fix_repository_mapping (
    id SERIAL PRIMARY KEY,
    workspace_key VARCHAR(100) NOT NULL,
    belong_sub_system VARCHAR(200) NOT NULL,
    discovery_iversion VARCHAR(500) NOT NULL,
    system_iteration VARCHAR(100) NOT NULL,
    repository VARCHAR(500) NOT NULL,
    branch VARCHAR(200) NOT NULL,
    dev_image VARCHAR(500) NOT NULL,
	test_script_repository varchar(500) NULL,
	test_script_branch varchar(200) NULL,
    insert_time TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NULL
);

-- 添加表注释
COMMENT ON TABLE defect_fix_repository_mapping IS '从RDC缺陷单/变更活动单中获取 "belong_sub_system"、"discovery_iversion"、"system_iteration" 这3个字段的组合信息，来查找表中  "repository", "branch", "dev_image", "test_script_repository", "test_script_branch" 这5个字段信息';

-- 添加字段注释
COMMENT ON COLUMN defect_fix_repository_mapping.id IS '唯一标识ID，自增主键';
COMMENT ON COLUMN defect_fix_repository_mapping.workspace_key IS '工作区(项目名称)，对应RDC字段：workspaceKey';
COMMENT ON COLUMN defect_fix_repository_mapping.belong_sub_system IS '所属子系统，对应RDC字段：BelongSubSystem，部分项目的RDC没有该项，需要注意';
COMMENT ON COLUMN defect_fix_repository_mapping.discovery_iversion IS '发现版本（来自iVersion），对应RDC字段：DiscoveryVersionFromiVersion/iChangeDiscoveryVersion，注意缺陷单和变更活动单来自的RDC字段不一样';
COMMENT ON COLUMN defect_fix_repository_mapping.system_iteration IS '迭代号，对应RDC字段：System_IterationPath';
COMMENT ON COLUMN defect_fix_repository_mapping.repository IS '代码库';
COMMENT ON COLUMN defect_fix_repository_mapping.branch IS '代码分支，部分项目的RDC有，但是不使用，如CodeBranch';
COMMENT ON COLUMN defect_fix_repository_mapping.dev_image IS '编译镜像，部分项目的RDC有，但是不使用';   
COMMENT ON COLUMN defect_fix_repository_mapping.test_script_repository IS '测试脚本代码库地址，部分项目的RDC有，但是不使用';
COMMENT ON COLUMN defect_fix_repository_mapping.test_script_branch IS '测试脚本代码库分支，部分项目的RDC有，但是不使用';
COMMENT ON COLUMN defect_fix_repository_mapping.insert_time IS '插入时间';
COMMENT ON COLUMN defect_fix_repository_mapping.update_time IS '更新时间';

-- 创建唯一索引（防止重复数据，基于4个核心查询字段）
CREATE UNIQUE INDEX idx_defect_fix_repo_mapping_unique 
ON defect_fix_repository_mapping (workspace_key, belong_sub_system, discovery_iversion, system_iteration);

-- 创建复合查询索引（用于快速查找）
CREATE INDEX idx_defect_fix_repo_mapping_query 
ON defect_fix_repository_mapping (workspace_key, belong_sub_system, discovery_iversion, system_iteration); 
