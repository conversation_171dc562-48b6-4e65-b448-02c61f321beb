from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any


@dataclass
class RdcTestcaseDto:
    """测试智能体所需要的测试用例信息实体类"""
    # 必须满足的字段
    work_item_id: str       # 工作项ID
    test_case_title: str    # 测试用例标题 RDC - System_Title

    # 可选字段
    test_case_status: Optional[str] = None  # 状态 RDC - System_State - name
    test_case_path: Optional[str] = None    # 用例所在层级路径 data['bo']['directories']['DAIP-946387'][0]['path']
    auto_status: Optional[str] = None       # 自动化状态 RDC - HasAutomated

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于JSON序列化"""
        return asdict(self)
