"""
    标题：信息映射表的维护与更新解决方案

    实体介绍：
    1、用户：触发执行的人，需要该用户的账号密码来鉴权，从而触发后续流程
    2、icenter系统：是一个复杂的富文本在线编辑系统，用户可以在icenter页面中编写一个表格，表格里写好缺陷单所必须的信息；
    3、PG数据库：需要将icenter系统中的表格数据同步到PG数据库中，从而方便后续的查询和分析
    
    信息映射表维护方案：
    1、由用户在指定icenter页面中编写一个表格，表格里写好缺陷单所必须的信息；

    入库流程设计：
    1、icenter页面的表格数据获取：
      1.1 使用用户提供的账号密码登录icenter系统，获取指定页面的HTML数据 ；如果用户账号密码错误，则系统报错
      1.2 解析HTML数据，获取到表格数据；
      1.3 将表格数据转换为实体类列表；
    2、入库处理；
      2.1 删除PG数据库中所有该用户查询icenter页面表格中配置的工作区数据
      2.2 将1.3获取到的实体类列表保存到PG数据库中；如果实体类列表中存在重复数据，则系统报错 
    
    触发方式：Jenkins触发，可以每天触发一次或多次
"""