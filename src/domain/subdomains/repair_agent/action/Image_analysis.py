from src.domain.subdomains.rdc_tools.handler.rdc_api import RdcApi
import re
import requests
import json
import os
import base64
from src.domain.subdomains.repair_agent.utils.logger_config import logger
TMP_DIR = "/tmp/repairagent"
# 服务器地址（修改为你的服务器 IP/端口）
API_BASE_URL = "http://***********:31252"


def process_problem_statement(workspace:str, user_id:str, appcode:str, problem_statement:str):
    """
    处理包含图片标记的文本
    :param problem_statement: 输入的问题描述文本
    :return: 替换图片标记后的文本
    """
    rdc_api = RdcApi(workspace, user_id, appcode)
    # 正则表达式匹配图片标记
    pattern = r'__IMAGE::(.*?)::IMAGE__'
    matches = re.findall(pattern, problem_statement)

    # 创建临时目录存储图片
    temp_dir = "temp_images"
    temp_dir = TMP_DIR+"/"+temp_dir
    os.makedirs(temp_dir, exist_ok=True)

    # 缓存已识别的结果（避免重复处理相同图片）
    image_cache = {}

    # 处理每个匹配到的图片标记
    try:
        for image_identifier in matches:
            if image_identifier in image_cache:
                continue

            # 下载图片,
            image_name = "tmp.png"
            save_path = os.path.join(temp_dir, image_name)
            rdc_api.download_rdc_picture(image_identifier, save_path)

            # 识别图片内容
            text_content = convert_to_text(save_path)
            if os.path.exists(save_path):
                os.remove(save_path)
                logger.info(f"已删除：{save_path}")
            image_cache[image_identifier] = text_content

        # 替换原文本中的图片标记
        def replace_match(match):
            image_identifier = match.group(1)
            return image_cache.get(image_identifier, match.group(0))

        result = re.sub(pattern, replace_match, problem_statement)
    except Exception as e:
        logger.error(e)
        result = problem_statement

    return result

def convert_image_to_base64(image_path):
    """ 将本地图片转换为 Base64 编码 """
    with open(image_path, "rb") as img_file:
        return base64.b64encode(img_file.read()).decode("utf-8")


def send_text_request(args):
    """ 发送文本推理请求 """
    url = f"{API_BASE_URL}/inference/text"
    payload = {"text": args.text}

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        data = response.json()
        logger.info("\n[Response]:", json.dumps(data, indent=4, ensure_ascii=False))
    else:
        logger.error("\n[Error]:", response.text)


def convert_to_text(image_path):
    """ 发送图像推理请求 """
    url = f"{API_BASE_URL}/inference/image"
    prompt = "请将图片中的信息转换为文字信息"
    payload = {"text": prompt, "max_tokens": 8192, "temperature": 0.0}

    if image_path:
        if not os.path.exists(image_path):
            logger.error("\n[Error]: 本地图片路径不存在！")
            return
        image_base64 = convert_image_to_base64(image_path)
        payload["image_base64"] = image_base64
    else:
        logger.error("\n[Error]: 必须提供 `image_path`")
        return

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        data = response.json()
        logger.info("\n[Response]:" + json.dumps(data, indent=4, ensure_ascii=False))
        return data["response"]
    else:
        logger.error("\n[Error]:" + response.text)
        return response.text


if __name__ == "__main__":
    problem_statement = " V20.24.40.06P02.n2506270556创建手动执行的直方图任务，启动任务验证再次启动任务，提供任务已启动不可再次启动，且任务仍然处于启动状态实际，任务处于了暂停状态__IMAGE::https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/675fa5e6-d09f-4c76-a718-21aff96d35b3/00313545::IMAGE____IMAGE::https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/df3c54da-32a0-4852-a674-6f9534dd56d2/00313545::IMAGE____IMAGE::https://izpc.zte.com.cn/zte-rdcloud-rdc-rdcserver/file/viewFile/d8769d09-2591-4593-a231-e59452e1d49f/00313545::IMAGE__"



    #str = process_problem_statement(workspace, user_id, appcode, problem_statement)
    #print(str)




