import os
import requests
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import logging
from src.infrastructure.tools.serving_upload_to_artifactory import upload_to_artifactory

# 初始化日志
logger = logging.getLogger(__name__)

class BuildArtifactAction:
    def __init__(self):
        self.exec_service_url = "http://localhost:40181"
        self.request_timeout = 1800  # 客户端请求超时(秒)
        self.exec_timeout = 1800   # 与服务端一致的执行超时(秒)

    def _get_file_mtime(self, file_path: str) -> Optional[float]:
        """获取文件修改时间，文件不存在返回None"""
        try:
            return os.path.getmtime(file_path)
        except FileNotFoundError:
            logger.warning(f"文件不存在: {file_path}")
            return None

    def _execute_remote_command(self, command: str, workdir: str) -> Dict[str, Any]:
        """
        发送命令到执行服务端
        返回格式: {'exec_ok': bool, 'code_ok': bool, 'exec_res': str}
        """
        try:
            logger.info(f"正在执行远程命令: {command}")
            response = requests.post(
                f"{self.exec_service_url}/exec",
                json={
                    "command": command,
                    "workdir": workdir,
                    "timeout": self.exec_timeout
                },
                timeout=self.request_timeout
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"请求执行服务失败: {str(e)}")
            return {
                'exec_ok': False,
                'code_ok': False,
                'exec_res': str(e)
            }

    def package_action(
            self, 
            workspace_path: str,
            command: str,
            package_path: str,
            artifactory_url: Optional[str] = None,
            api_key: Optional[str] = None
    ) -> Optional[str]:
        """
        执行打包动作并上传到Artifactory

        Args:
            workspace_path: 工作目录绝对路径
            command: 构建命令(如"mvn package -DskipTests")
            package_path: 相对于工作目录的产物路径
            artifactory_url: Artifactory上传URL(可选)
            api_key: Artifactory API密钥(可选)
            
        Returns:
            成功返回Artifactory URL，失败返回None
        """
        # 标准化路径处理
        workspace_path = os.path.abspath(workspace_path)
        output_path = os.path.normpath(os.path.join(workspace_path, package_path))

        logger.info(f"工作目录: {workspace_path}")
        logger.info(f"预期产物路径: {output_path}")

        # 1. 执行构建命令
        logger.info(f"正在执行命令: {command}")
        exec_result = self._execute_remote_command(command, workspace_path)

        if not exec_result.get('exec_ok', False):
            error_msg = exec_result.get('exec_res', '未知错误')
            logger.error(f"远程执行失败: {error_msg}")
            return None

        # 2. 验证构建产物
        post_build_mtime = self._get_file_mtime(output_path)
        if post_build_mtime is None:
            logger.error(f"产物文件不存在: {output_path}")
            return None

        logger.info(f"构建成功! 产物路径: {output_path}")

        # 3. 上传到Artifactory (可选)
        if not artifactory_url or not api_key:
            logger.info("未配置Artifactory参数，跳过上传")
            return None

        try:
            # 获取纯文件名（不带路径）
            file_name = os.path.basename(package_path)
            
            # 生成时间戳格式：YYYYMMDD_HHMMSS
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 构造上传路径：基础URL/时间戳/原文件名
            versioned_url = f"{artifactory_url.rstrip('/')}/{timestamp}/{file_name}"

            logger.info(f"正在上传文件到: {versioned_url}")
            if upload_to_artifactory(
                file_path=output_path,
                artifactory_url=versioned_url,
                api_key=api_key
            ):
                logger.info(f"上传成功: {versioned_url}")
                return versioned_url

            logger.error("上传失败")
            return None
        except Exception as e:
            logger.error(f"上传过程中出错: {str(e)}", exc_info=True)
            return None