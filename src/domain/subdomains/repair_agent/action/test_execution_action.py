from src.domain.subdomains.jenkins_tools.handler.jenkins_handler import <PERSON><PERSON><PERSON><PERSON>
from src.domain.subdomains.repair_agent.action.build_artifact_action import BuildArtifactAction
from src.domain.subdomains.repair_agent.config.load_config import load_yaml_config
import logging
logger = logging.getLogger(__name__)

def test_execution_action(rdc_params: dict) -> dict:
    """
    After the code editing is completed, you need to call the test execution action for testing
    """
    #  step1：从RDC中的解析字段中获取测试执行智能体所需要的参数
    logger.info("====== 测试执行流程开始 ======")
    test_cases = rdc_params.get("test_cases", [])
    build_package_action = BuildArtifactAction()
    #  step2：对当前workspace代码内容进行打包上传制品库
    # package_path = build_package_action.package_action(workspace_path=rdc_params.get("workspace", "./"),
    #                                                    command=rdc_params.get("command", ""),
    #                                                    package_path=rdc_params.get("package_path", ""),
    #                                                    artifactory_url=rdc_params.get("artifactory_url", ""),
    #                                                    api_key=rdc_params.get("api_key", ""))
    package_path = "zdh-hdfs-autotests/target/zdh-hdfs-autotests-3.4.1-zdh15.6.1-SNAPSHOT.tar.gz"
    #  step3：调用触发流水线执行测试执行智能体
    test_params = {"test_cases": test_cases, "package_path": package_path, "rdc_id": rdc_params.get("rdc_id", ""),
                   'test_script_repository': rdc_params.get('test_script_repository', ""),
                   'test_script_branch': rdc_params.get('test_script_branch', "")}
    #
    jenkins_uid = rdc_params.get("jenkins_uid", "")
    jenkins_token = rdc_params.get("jenkins_token", "")
    #
    namespace = rdc_params.get("namespace", "")  # 需要透传
    full_project_name = rdc_params.get("jenkins_full_job_name", "")  # 完整的Jenkins job名称，如 'qfs_debug_pipelines/repairAgent-RdcDefectProcessor'
    jks_handler = JenkinsHandler(jenkins_uid, jenkins_token)
    logger.info("====== 调用测试执行流水线开始 ======")
    #  step4：等待测试执行的结果
    test_info = jks_handler.trigger_and_track(namespace, full_project_name, params=test_params)
    logger.info("====== 测试执行的结果 ======")
    logger.info(test_info)
    # 加载task任务模板
    config = load_yaml_config("test_exec_agent.yaml")
    task_prompt = config.get("default_user_prompt", "")
    change_info = rdc_params.get("patch", "")
    #  step5：对测试执行的结果进行整理
    task = get_new_task(test_info, change_info, task_prompt, rdc_params.get("workspace", "./"))

    return task

def get_test_execution_params(rdc_params: dict) -> dict:
    keys = [
        "test_case_path",
        "test_case_title",
        "test_case_status",
        "auto_status",
        "script_branch",
        "script_repo",
        "package_path"
    ]
    return {key: rdc_params.get(key, "") for key in keys}




def get_new_task(test_info, change_info, prompt, workspace):
    testcase_str = ""
    return_status = ['200']
    test_interface = ""
    for num, testcase in enumerate(test_info, start=1):
        test_description = testcase.get("test_description", None)
        assert_expect = testcase.get("assert_expect", None)
        assert_actual = testcase.get("assert_actual", None)
        stack_info = testcase.get("stack_info", None)
        test_log = testcase.get("test_log", None)
        test_interface += testcase.get("test_interface", None)
        testcase_str += f"测试用例{num}: \n"

        if test_description is not None:
            test_description_str = ""
            for step, desc in enumerate(test_description, start=1):
                if desc[0] == '#':
                    test_description_str += f"   {step}. {desc[1:]}\n"  # desc[1:] 用于排除描述的第一个`#`符号
                else:
                    test_description_str += f"   {step}. {desc}\n"
            testcase_str += f"- 测试场景: \n{test_description_str}"

        if assert_expect is not None:
            testcase_str += f"- 预期结果: 返回状态码{assert_expect}\n"
            if assert_expect not in return_status:
                return_status.append(assert_expect)

        if assert_actual is not None:
            testcase_str += f"- 实际结果: 返回状态码{assert_actual}\n"
            if assert_actual not in return_status:
                return_status.append(assert_actual)

        testcase_str += "- 错误信息: "
        if assert_actual != assert_expect:
            testcase_str += f"Expected status code {assert_expect}, but got {assert_actual}. "
        if test_log and len(test_log) != 0:
            for log in test_log:
                if log["level"] == "ERROR":
                    testcase_str += f"{log['message']}. "
        testcase_str += f"Response body: {stack_info}\n\n"

    change_info_str = ""
    if change_info is not None:
        change_info_str = f"- 最近修改补丁: {change_info}\n"

    user_input_prompt = prompt % {
        "testcases": testcase_str,
        "test_interface": test_interface,
        "workspace": workspace,
        "change_info": change_info_str
    }
    return user_input_prompt







if __name__ == "__main__":
    pass