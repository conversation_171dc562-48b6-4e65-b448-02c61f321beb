mcp_tool_for_llm: |-
  ## use_mcp_tool
  Description: Request to use a tool provided by a connected MCP server. Each MCP server can provide multiple tools with different capabilities. Tools have defined input schemas that specify required and optional parameters.
  Parameters:
    - server_name: (required) The name of the MCP server providing the tool
    - tool_name: (required) The name of the tool to execute
    - arguments: (required) A JSON object containing the tool's input parameters, following the tool's input schema
  Usage:
    <use_mcp_tool>
    <server_name>server name here</server_name>
    <tool_name>tool name here</tool_name>
    <arguments>
    {
      "param1": "value1",
      "param2": "value2"
    }
    </arguments>
    </use_mcp_tool>

mcp_tool_use_example: |-
  ## Example 4: Requesting to use an MCP tool

  <use_mcp_tool>
  <server_name>weather-server</server_name>
  <tool_name>get_forecast</tool_name>
  <arguments>
  {
    "city": "San Francisco",
    "days": 5
  }
  </arguments>
  </use_mcp_tool>

mcp_server_info: |-
  ====

  MCP SERVERS
  
  The Model Context Protocol (MCP) enables communication between the system and locally running MCP servers that provide additional tools and resources to extend your capabilities.
  
  # Connected MCP Servers
  
  When a server is connected, you can use the server's tools via the \`use_mcp_tool\` tool.
  
  %(servers_info)s
  
  # MCP Servers Are Not Always Necessary

  The user may not always request the use or creation of MCP servers. Instead, they might provide tasks that can be completed with existing tools. While using the MCP SDK to extend your capabilities can be useful, it's important to understand that this is just one specialized type of task you can accomplish. You should only implement MCP servers when the user explicitly requests it (e.g., "add a tool that...").
  
  Remember: The MCP documentation and example provided above are to help you understand and work with existing MCP servers or create new ones when requested by the user. You already have access to tools and capabilities that can be used to accomplish a wide range of tasks.

mcp_capabilities: |-
  - You have access to MCP servers that may provide additional tools and resources. Each server may provide different capabilities that you can use to accomplish tasks more effectively.

mcp_rules: |-
  - MCP operations should be used one at a time, similar to other tool usage. Wait for confirmation of success before proceeding with additional operations.
