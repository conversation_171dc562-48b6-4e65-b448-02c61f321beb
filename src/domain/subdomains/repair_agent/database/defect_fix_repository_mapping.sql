CREATE TABLE public.defect_fix_repository_mapping (
	id serial NOT NULL,
	workspace_key varchar(100) NOT NULL,
	belong_sub_system varchar(200) NOT NULL,
	discovery_iversion varchar(500) NOT NULL,
	system_iteration varchar(100) NOT NULL,
	repository varchar(500) NOT NULL,
	branch varchar(200) NOT NULL,
	dev_image varchar(500) NOT NULL,
	test_script_repository varchar(500) NULL,
	test_script_branch varchar(200) NULL,
	insert_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
	update_time timestamp NULL,
	CONSTRAINT defect_fix_repository_mapping_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_defect_fix_repo_mapping_query ON public.defect_fix_repository_mapping USING btree (workspace_key, belong_sub_system, discovery_iversion, system_iteration);

-- Permissions

ALTER TABLE public.defect_fix_repository_mapping OWNER TO postgres;
GRANT ALL ON TABLE public.defect_fix_repository_mapping TO postgres; 