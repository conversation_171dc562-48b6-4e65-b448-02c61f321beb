"""缺陷代码库映射信息数据库操作模块

提供缺陷代码库映射信息的数据库操作功能，包括增删改查等操作。
支持PostgreSQL数据库，用于管理缺陷修复相关的代码库映射信息。
"""
# Started by AICoder, pid:519a4zca3en939914d8209393108425cf22662b8
from typing import List, Dict, Any, Optional
import logging as log
from src.domain.subdomains.repair_agent.database.postgre_db import PostgresDB
from src.domain.common.string_utils import StringUtils


DEFECT_FIX_REPOS_MAPPING_QUERY_FIELDS = ['id', 'repository', 'branch', 'dev_image', 'test_script_repository', 'test_script_branch']


class DefectReposPGHandler:
    """缺陷代码库映射信息数据库操作类
    
    负责缺陷代码库映射信息的数据库操作，包括增删改查功能。
    支持PostgreSQL数据库，提供批量插入、条件查询等操作。
    """
    
    def __init__(self, db_host: str, db_port: int, db_user: str, db_password: str, db_name: str):
        """缺陷代码库映射信息数据库操作类

        Args:
            db_host (str): 数据库主机地址
            db_port (int): 数据库端口号
            db_user (str): 数据库用户名
            db_password (str): 数据库密码
            db_name (str): 数据库名称
        """
        database_config = {
            "dbname": db_name,
            "user": db_user,
            "password": db_password,
            "host": db_host,
            "port": db_port,
        }
        self.db = PostgresDB(database_config)
        self.table_name = "defect_fix_repository_mapping"

    def update_defect_repos_mapping_to_pg_main(self, workspace_key: str, defect_repos_mapping_datas: list):
        """将缺陷代码库映射信息更新到pg数据库

        采用方案一：删除后插入
        步骤1: 删除所有 workspace_key 记录
        步骤2: 将缺陷代码库映射信息插入到 pg 数据库

        Args:
            workspace_key (str): 工作空间key
            defect_repos_mapping_datas (list): 缺陷代码库映射信息列表

            缺陷代码库映射信息表DDL如下：
            CREATE TABLE public.defect_fix_repository_mapping (
                id serial NOT NULL,
                workspace_key varchar(100) NOT NULL,
                belong_sub_system varchar(200) NOT NULL,
                discovery_iversion varchar(500) NOT NULL,
                system_iteration varchar(100) NOT NULL,
                repository varchar(500) NOT NULL,
                branch varchar(200) NOT NULL,
                dev_image varchar(500) NOT NULL,
                test_script_repository varchar(500) NULL,
                test_script_branch varchar(200) NULL,
                insert_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                update_time timestamp NULL,
                CONSTRAINT defect_fix_repository_mapping_pkey PRIMARY KEY (id)
            );
            CREATE INDEX idx_defect_fix_repo_mapping_query ON public.defect_fix_repository_mapping 
            USING btree (workspace_key, belong_sub_system, discovery_iversion, system_iteration);
            CREATE UNIQUE INDEX idx_defect_fix_repo_mapping_unique ON public.defect_fix_repository_mapping 
            USING btree (workspace_key, belong_sub_system, discovery_iversion, system_iteration);
        """
        log.info(f"开始更新工作空间 {workspace_key} 的缺陷代码库映射信息")
        
        # 步骤1: 删除所有 workspace_key 记录
        deleted_count = self._delete_existing_records(workspace_key)
        log.info(f"删除了 {deleted_count} 条旧记录")
        
        # 步骤2: 将缺陷代码库映射信息插入到 pg 数据库
        if defect_repos_mapping_datas:
            inserted_count = self._insert_new_records(workspace_key, defect_repos_mapping_datas)
            log.info(f"插入了 {inserted_count} 条新记录")
        else:
            log.warning("没有新的缺陷代码库映射信息需要插入")
            
        log.info(f"工作空间 {workspace_key} 的缺陷代码库映射信息更新完成")

    def _delete_existing_records(self, workspace_key: str) -> int:
        """删除指定工作空间的所有现有记录

        Args:
            workspace_key (str): 工作空间key

        Returns:
            int: 删除的记录数量
        """
        conditions = {"workspace_key": workspace_key}
        deleted_count = self.db.delete_data(self.table_name, conditions)
        return deleted_count

    def _insert_new_records(self, workspace_key: str, defect_repos_mapping_datas: List[Dict[str, Any]]) -> int:
        """插入新的缺陷代码库映射信息记录

        Args:
            workspace_key (str): 工作空间key
            defect_repos_mapping_datas (List[Dict[str, Any]]): 缺陷代码库映射信息列表

        Returns:
            int: 插入的记录数量
        """
        # 转换数据格式，添加workspace_key字段
        insert_data_list = []
        for data in defect_repos_mapping_datas:
            # 允许NULL值的字段 - 空字符串转换为None以支持数据库NULL
            test_script_repo = data.get("test_script_repository")
            test_script_branch = data.get("test_script_branch")
            
            # 对测试脚本相关字段进行空值转换
            if test_script_repo == "" or test_script_repo is None:
                test_script_repo = None
            if test_script_branch == "" or test_script_branch is None:
                test_script_branch = None
            
            insert_data = {
                "workspace_key": workspace_key,
                "belong_sub_system": data.get("belong_sub_system", ""),
                "discovery_iversion": data.get("discovery_iversion", ""),
                "system_iteration": data.get("system_iteration", ""),
                "repository": data.get("repository", ""),
                "branch": data.get("branch", ""),
                "dev_image": data.get("dev_image", ""),
                "test_script_repository": test_script_repo,
                "test_script_branch": test_script_branch
            }
            insert_data_list.append(insert_data)
        
        # 批量插入数据
        inserted_count = self.db.insert_batch_data(self.table_name, insert_data_list)
        return inserted_count

    def get_defect_repos_mapping_by_workspace(self, workspace_key: str) -> List[Dict[str, Any]]:
        """根据工作空间key查询缺陷代码库映射信息

        Args:
            workspace_key (str): 工作空间key

        Returns:
            List[Dict[str, Any]]: 缺陷代码库映射信息列表
        """
        conditions = {"workspace_key": workspace_key}
        result = self.db.select_data(self.table_name, conditions, fetchall=True)
        # Convert result to proper type
        if isinstance(result, list):
            return result
        return []

    def _check_query_result(self, result: Any) -> List[Dict[str, Any]]:
        """检查查询结果

        Args:
            result (Any): 查询结果

        Returns:
            List[Dict[str, Any]]: 缺陷代码库映射信息列表        
        """
        # Handle different return types from select_data
        query_lst = []
        if isinstance(result, int) or not result:
            query_lst = []
        elif isinstance(result, list):
            if len(result) == 0:
                query_lst = []
            else:
                query_lst = result
        # Convert RealDictRow to Dict
        query_lst = [dict(row) for row in query_lst]
        return query_lst

    def get_defect_repos_mapping_by_conditions(
        self, workspace_key: str, belong_sub_system: Optional[str] = None,
        discovery_iversion: Optional[str] = None,
        system_iteration: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """根据条件查询缺陷代码库映射信息

        查询优先级：
        1. 子系统+版本号+迭代
        2. 子系统+版本号裁剪+迭代
        3. 子系统+版本号裁剪+default

        Args:
            workspace_key (str): 工作空间key
            belong_sub_system (str, optional): 所属子系统
            discovery_iversion (str, optional): 发现版本
            system_iteration (str, optional): 系统迭代

        Returns:
            List[Dict[str, Any]]: 缺陷代码库映射信息列表
        """
        # 设置默认值
        sub_system = belong_sub_system if belong_sub_system else 'default'
        version = discovery_iversion if discovery_iversion else 'default'
        iteration = system_iteration if system_iteration else 'default'
        
        # 优先级1: 子系统+版本号+迭代
        conditions = {
            "workspace_key": workspace_key,
            "belong_sub_system": sub_system,
            "discovery_iversion": version,
            "system_iteration": iteration
        }
        
        result = self.db.select_data(
            self.table_name, conditions, 
            fields=DEFECT_FIX_REPOS_MAPPING_QUERY_FIELDS, fetchall=True
        )
        query_lst = self._check_query_result(result)
        
        # 如果第一次查询有结果，直接返回
        if query_lst:
            return query_lst
        
        # 优先级2: 子系统+版本号裁剪+迭代
        cropped_version = version
        cropped_version = StringUtils.crop_version(version)
        if cropped_version != version:  # 只有当裁剪后的版本号与原版本号不同时才查询
            conditions["discovery_iversion"] = cropped_version
            log.debug(f"精确查找未找到任何数据，使用 优先级2: 子系统+版本号裁剪+迭代 进行查询: {conditions}")
            result = self.db.select_data(
                self.table_name, conditions,
                fields=DEFECT_FIX_REPOS_MAPPING_QUERY_FIELDS, fetchall=True
            )
            query_lst = self._check_query_result(result)
            
            if query_lst:
                return query_lst
        
        # 优先级3: 子系统+版本号裁剪+default
        # 只有当迭代不为default时才查询default迭代
        if iteration != 'default':
            conditions["system_iteration"] = 'default'
            log.debug(f"精确查找未找到任何数据，使用 优先级3: 子系统+版本号裁剪+default 进行查询: {conditions}")
            result = self.db.select_data(
                self.table_name, conditions,
                fields=DEFECT_FIX_REPOS_MAPPING_QUERY_FIELDS, fetchall=True
            )
            query_lst = self._check_query_result(result)
        
        return query_lst
# Ended by AICoder, pid:519a4zca3en939914d8209393108425cf22662b8
