import subprocess
import time
import os
import signal
import sys
import requests
from datetime import datetime
from loguru import logger

# 清理代理环境变量
os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("all_proxy", None)


class LocAgentServiceManager:
    def __init__(self, shell_script_path, log_dir="logs"):
        self.shell_script_path = shell_script_path
        os.makedirs(log_dir, exist_ok=True)  # 确保日志目录存在
        self.log_dir = log_dir
        self.process = None

    def service_is_started(self):
        return True is self.process is not None

    def start_service(self, ip="0.0.0.0", port="5002", extra_args=None,
                      log_file_name=f'service_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', timeout=60):
        """在Linux下启动服务，日志重定向到文件"""
        log_file = os.path.join(self.log_dir, log_file_name)
        extra_args = extra_args or []  # 默认空列表，动态参数

        # 检查shell脚本是否存在
        if not os.path.isfile(self.shell_script_path):
            logger.error(f"Shell script not found: {self.shell_script_path}")
            return False

        # 检查端口是否被占用
        if os.system(f"netstat -tuln | grep :{port} > /dev/null") == 0:
            logger.error(f"Port {port} is already in use")
            return False

        try:
            # 构造shell命令，包含动态参数和日志重定向
            cmd = (
                f'bash {self.shell_script_path} '
                f'--host={ip} --port={port} {" ".join(extra_args)} >> {log_file} 2>&1'
            )
            # 启动服务进程
            self.process = subprocess.Popen(
                cmd,
                shell=True,
                preexec_fn=os.setsid  # 创建新进程组
            )
            logger.info(f"Started service with PID: {self.process.pid}, IP: {ip}, Port: {port}, logging to: {log_file}")

            # 等待服务启动并验证
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"http://{ip}:{port}/", timeout=1)
                    if response.status_code in (200, 404, 401):  # 假设这些状态码表示服务已启动
                        logger.info(f"LocAgent Service is up and running on http://{ip}:{port}")
                        return True
                except requests.RequestException:
                    time.sleep(1)  # 继续等待
            logger.error(f"LocAgent Service did not start within {timeout} seconds")
            self.stop_service()  # 清理未成功启动的进程
            return False

        except Exception as e:
            logger.error(f"Failed to start service: {e}")
            return False

    def send_request(self, ip="0.0.0.0", port="5002", data=None):
        """向服务端发送请求"""
        url = f"http://{ip}:{port}"
        try:
            # 调试：记录代理环境变量
            logger.debug(f"Environment HTTP_PROXY: {os.environ.get('HTTP_PROXY')}")
            logger.debug(f"Environment HTTPS_PROXY: {os.environ.get('HTTPS_PROXY')}")

            # 创建新会话，禁用代理
            session = requests.Session()
            session.trust_env = False
            session.proxies = {"http": None, "https": None}

            # 发送请求
            response = session.post(url, json=data, timeout=5)
            logger.info(f"Request sent to {url}, response status: {response.status_code}")
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Request to {url} failed: {e}")
            return None

    def stop_service(self):
        """关闭服务"""
        if not self.process:
            logger.debug("No service process to stop")
            return

        try:
            # 向进程组发送SIGTERM信号
            os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
            # 等待进程结束
            self.process.wait(timeout=5)
            logger.info(f"LocAgent Service with PID {self.process.pid} stopped")
        except subprocess.TimeoutExpired:
            logger.error(f"LocAgent Service with PID {self.process.pid} did not stop within timeout")
            try:
                # 强制杀死进程
                os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                self.process.wait(timeout=5)
                logger.info(f"LocAgent Service with PID {self.process.pid} forcefully stopped")
            except Exception as e:
                logger.error(f"Failed to forcefully stop service: {e}")
        except Exception as e:
            logger.error(f"Failed to stop service: {e}")
        finally:
            self.process = None


def main():
    # 配置shell脚本路径和动态参数
    shell_script_path = "/media/vdc/10289111/projects/codeAgent1/codeAgent/src/infrastructure/tools/locagent/run_mcp.sh"
    extra_args = [
        "--repo=/home/<USER>/Desktop/locagent",
        "--disable_vector_retrieve"
    ]
    service_manager = LocAgentServiceManager(shell_script_path, log_dir="logs")

    # 启动服务
    if not service_manager.start_service(ip="0.0.0.0", port="5002", extra_args=extra_args):
        logger.error("LocAgent Service failed to start, exiting...")
        sys.exit(1)

    try:
        # 发送请求
        response = service_manager.send_request(
            ip="0.0.0.0",
            port="5002",
            data={"key": "value"}  # 替换为实际请求数据
        )
        if response is not None:
            logger.info(f"Response received: {response}")
        else:
            logger.error("No response received")
    finally:
        # 确保服务关闭，释放端口
        service_manager.stop_service()


if __name__ == "__main__":
    main()
