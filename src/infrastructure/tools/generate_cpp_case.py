import os
import glob
# 打印输出结果
import json
import re
import time


# 要扫描的文件扩展名（C++源文件和头文件）
VALID_EXTENSIONS = ('.cpp', '.cc', '.cxx', '.hpp', '.hxx')
# ====================== 配置结束 ======================

def find_files_needing_tests(root_dir):
    """
    查找包含函数/方法定义的文件（需要补充单元测试的文件）
    :param root_dir: 项目根目录
    :return: 需要补充测试的文件路径列表
    """
    # 匹配类成员函数定义的正则表达式
    member_func_pattern = re.compile(
        r'\b\w+(?:\s*::\s*\w+)*\s*\w+\s*::\s*\w+\s*\([^{]*\)\s*(?:const|noexcept)?\s*\{',
        re.DOTALL
    )
    
    # 匹配普通函数定义的正则表达式
    function_pattern = re.compile(
        r'\b\w+(?:\s*::\s*\w+)*\s*\w+\s*\([^{]*\)\s*\{',
        re.DOTALL
    )
    
    target_files = []
    
    for foldername, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.lower().endswith(VALID_EXTENSIONS):
                filepath = os.path.join(foldername, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as file:
                        content = file.read()
                        # 移除单行注释（避免干扰匹配）
                        content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
                        # 移除多行注释（简易版）
                        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
                        
                        # 检查是否包含函数定义
                        if member_func_pattern.search(content) or function_pattern.search(content):
                            #filepath去掉root_dir部分，只保留目录和文件名
                            file_name_without_test = os.path.relpath(filepath, root_dir).replace('\\', '/')
                            target_files.append(file_name_without_test)
                except UnicodeDecodeError:
                    # 跳过无法解码的文件
                    continue
                except Exception as e:
                    print(f"Error processing {filepath}: {str(e)}")
    
    return target_files


def generate_cpp_test_cases(root_dir, spdirectory, project_name, question_id):
    output = []

    filelist = find_files_needing_tests(os.path.join(root_dir, spdirectory))
    user_prompt = """
## 测试框架及重要依赖：
1. **测试框架选型**：使用gtest框架，版本号1.16.0
2. **外部依赖处理**：使用emock框架Mock出被测函数所依赖的其他外部函数，版本号0.9.0
3. **分支覆盖**：分析被测函数的分支，为每个分支都生成测试用例

## 单元测试用例文件要求
- 不要尝试新增或者修改目录**ut/testsuite/**以外的代码文件.
- 单元测试文件在 ` ut/testsuite/` 目录下,目录结构保持和源码目录结构一致。
- 如果业务代码文件名叫 **file_name.cpp**，那对应的单元测试文件名叫 **file_name_test.cpp**
    > 举个简单的例子：业务代码文件名叫 vfw_stat.cpp ，那对应的单元测试文件名叫 vfw_stat_test.cpp
- 如果目录下已经有 *test.cpp 文件，则在文件尾添加单元测试用例
- 不要读取**dr-dts**目录下面的任何文件
- ut cmake文件路径：ut/CMakeLists.txt 可以读取这个文件获取更多的编译相关信息
- 源代码中include的头文件,测试用例代码可以直接使用，不允许你添加头文件。

## 单元测试用例编码要求
- 代码变量必须要初始化.
- 若头文件错误，先不要修复错误，再次检查一遍.
- 必须使用 `EXPECT` 断言进行判断，禁止使用 `ASSERT` 断言.
- 测试用例不需要通过命名空间namespace隔离.
- 对于C++类中的私有对象的测试，可以通过实例化类的对象进行调用;
- 不能Mock正在测试的目标方法本身;
- 不要为标记为 'static' 的函数生成测试用例，应该通过调用它的上层接口来触发并测试其逻辑;
- 在测试用例中不得直接调用源码中的 'static' 函数;
- 所有测试应围绕模块的公开接口（public API）进行;


## EMOCK的语法
EMOCK(function / "function signature") 
    .stubs() / defaults() / expects(never() | once() | exactly(3) | atLeast(3) | atMost(3) ) 
    [.before("some-mocker-id")] 
    [.with( any() | eq(3) | neq(3) | gt(3) | lt(3) | spy(var_out) | check(check_func) 
     | outBound(var_out) | outBoundP(var_out_addr, var_size) | mirror(var_in_addr, var_size) 
     | smirror(string) | contains(string) | startWith(string) | endWith(string) )] 
    [.after("some-mocker-id")] 
    .will( returnValue(1) | repeat(1, 20) | returnObjectList(r1, r2) 
     | invoke(func_stub) | ignoreReturnValue() 
     | increase(from, to) | increase(from) | throws(exception) | die(3) ) 
    [.then(returnValue(2))] 
    [.id("some-mocker-id")]

用法示例：假设有个函数:

```c

namespace foo {
int bar();
}
```

你应该这样使用EMOCK.

```c
EMOCK("foo::bar").stubs().will(returnValue(1));
```c


## 请你仿照示例写出新被测代码的测试用例
## 再次重申要求：
1. 生成的单元测试代码完整；考虑正常、异常、边界等场景；
2. 生成没有编译错误的代码
3. 请生成多个用例，单元测试用例编码要求的前提下，尽可能覆盖被测函数中的每个分支.

## 示例代码
- 示例一
如果已经有单元测试文件时，直接添加用例代码即可.

```c
TEST(${case_name})
{
    // 初始化变量

    ${function}();

    // 验证
}
```

- 示例二
如果没有单元测试用例文件,新增测试用例代码文件.

```c
如果没有单元测试用例文件
#include "gtest/gtest.h"
#include "emock/emock.hpp"
#include "postgres.h"
// 下面添加其它必须得头文件

TEST(${case_name})
{
    // 初始化变量

    ${function}();

    // 验证
}
```
其中 `${function}` 是被测试的函数

- 示例三
如果需要对测试函数 Mock，可以使用 `EMOCK` 框架来打桩
```c
TEST(${case_name})
{
    // 初始化变量

    EMOCK(${function})
        .stubs()
        .will(returnValue(${expected_value}));
    ${function}();

    // 验证

    // 清理 Mock
    GlobalMockObject::verify();
}
```
其中 `${function}` 是被测试的函数，`${expected_value}` 是函数预期返回值


## 编译运行命令:
 
 ```
    sh build_ut.sh

 ```
"""

    prompt = os.environ.get('ZA_USER_PROMPT',default=user_prompt)
    for file in filelist:
        filetmp = os.path.join(spdirectory,file)
        output.append({
            "question_id": question_id,
            "project": project_name,
            "filename":os.path.join(spdirectory,file),
            "project_path": os.path.join(root_dir,spdirectory,file),
            "question": (
                f"""# 任务(Task)：
为{filetmp}中的类中的public方法补全单元测试用例.并且对变更代码进行评审.

{prompt}
"""
            )
        })
    
        question_id += 1
    return output,question_id


def save_test_cases_to_json(output, directory, dst_path):
    if not os.path.exists(dst_path):
        os.makedirs(dst_path)
    file_path = os.path.join(dst_path, 'bench_ut_questions.json')

    # 打开文件以写入模式，并使用 json.dump 写入数据
    with open(file_path, 'w', encoding='utf-8') as file:
        json.dump(output, file, indent=4, ensure_ascii=False)


def main(project_name, root_dir, spdirectory,  dst_path):
    question_id = 1
    output,question_id = generate_cpp_test_cases(root_dir, spdirectory, project_name, question_id)
    output.sort(key=lambda x: (x["project_path"], x["filename"])) 
    save_test_cases_to_json(output, "",dst_path)
    

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generate test cases for Java projects.")
    parser.add_argument("project", help="Name of the project.")
    parser.add_argument("workspace", help="Root directory of the project.")
    parser.add_argument("spdirectory", help="special dir.")
    parser.add_argument("dstpath", help="dst path for json.")
    args = parser.parse_args()

    main(args.project, args.workspace, args.spdirectory, args.dstpath)