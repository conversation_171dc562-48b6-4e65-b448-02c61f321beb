#!/bin/bash

readonly LOG_INFO="[INFO]"
readonly LOG_SUCC="[SUCCESS]"
readonly LOG_WARN="[WARNING]"
readonly LOG_ERROR="[ERROR]"

readonly COLOR_RED="\e[1;31m"
readonly COLOR_BLUE="\e[1;34m"
readonly COLOR_GREEN="\e[1;32m"
readonly COLOR_YELLOW="\e[1;33m"
readonly COLOR_DEFAULT="\e[0m"
readonly COLOR_BOLD="\e[1m"

readonly PWD_DIR=$(pwd)
readonly ROOT_DIR=$(dirname "$(readlink -f "$0")")
readonly DOWNLOAD_DIR="${ROOT_DIR}/Download"
MINIFORGE_DIR="${HOME}/miniforge3"

readonly MINIFORGE_INSTALLER_URL="https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/tools/Miniforge3-24.9.2-0-Linux-x86_64.sh"
readonly MINIFORGE_INSTALLER_NAME=$(basename "$MINIFORGE_INSTALLER_URL")

readonly LOCAGENT_CONDA_URL="https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/tools/locagent_conda.tar.gz"
readonly LOCAGENT_CONDA_NAME=$(basename "$LOCAGENT_CONDA_URL")

readonly LOCAGENT_REPO_URL="https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/tools/LocAgent.zip"
readonly LOCAGENT_REPO_NAME=$(basename "$LOCAGENT_REPO_URL")

readonly CONDA_ENV_NAME="locagent"
readonly PYTHON_VERSION="3.12"

readonly HTTPS_PROXY="http://proxyhk.zte.com.cn:80"
readonly GLOBAL_INDEX_URL="https://mirrors.aliyun.com/pypi/simple/"
# readonly GLOBAL_TRUSTED_HOST="artnj.zte.com.cn"

function log_info() {
    local message="${1:-}"
    echo -e "${COLOR_BLUE}${LOG_INFO}${COLOR_DEFAULT} ${message}."
}

function log_succ() {
    local message="${1:-}"
    echo -e "${COLOR_GREEN}${LOG_SUCC}${COLOR_DEFAULT} ${message}!"
}

function log_warn() {
    local message="${1:-}"
    echo -e "${COLOR_YELLOW}${LOG_WARN}${COLOR_DEFAULT} ${message}!" >&2
}

function log_error() {
    local message="${1:-}"
    local exit_code="${2:-1}"

    echo -e "${COLOR_RED}${LOG_ERROR}${COLOR_DEFAULT} ${message}!" >&2
    exit "${exit_code}"
}

function check_command() {
    if ! command -v "$1" >/dev/null 2>&1; then
        log_warn "Command '$1' not found"
        return 1
    fi
    return 0
}

function cleanup() {
    if [[ -d "${DOWNLOAD_DIR}" ]]; then
        if ! rm -rf "${DOWNLOAD_DIR}"; then
            log_warn "Failed to remove temporary directory '${DOWNLOAD_DIR}'"
        fi
    fi
}

function initialize() {
    log_info "Initializing download folders..."

    if ! mkdir -p "${DOWNLOAD_DIR}"; then
        log_error "Failed to create project directory"
    fi
}

function check_strict_platform() {

    log_info "Performing strict platform check..."

    local platform=$(uname -s 2>/dev/null || echo "unknown")
    if [ "${platform}" != "Linux" ]; then
        log_error "This script only supports Linux platform. Detected OS: ${platform}"
    fi

    log_succ "Linux platform verification successful"
}

function set_script_permissions() {

    log_info "Setting script permissions in '${ROOT_DIR}'"

    if ! find "${ROOT_DIR}" -type f -name "*.sh" -exec chmod 777 {} + 2>/dev/null; then
        local error_count=$(find "${ROOT_DIR}" -type f -name "*.sh" | wc -l)
        if ((error_count > 0)); then
            log_error "Failed to set permissions on ${error_count} bash scripts"
        else
            log_warn "No bash files found in '${ROOT_DIR}'"
        fi
    fi

    log_succ "Script permissions set successfully"
}

function check_python_env() {
    log_info "Checking Python environment..."

    if check_command python3; then
        local python_version=$(python3 -V 2>&1 | awk '{print $2}')
        log_info "Python version: ${python_version}"
    else
        log_error "Python 3 is not installed. Please install it first"
    fi

    if check_command pip3; then
        local pip_version=$(pip3 -V | awk '{print $2}')
        log_info "pip version: ${pip_version}"
    else
        log_error "pip3 is not installed. Please install it first"
    fi
}

function set_https_proxy() {

    log_info "Setting https_proxy environment variable..."
    local new_proxy=""

    if [[ -z "${https_proxy}" ]]; then
        new_proxy="${HTTPS_PROXY:-}"
    else
        new_proxy="${https_proxy}"
    fi

    if [[ "${new_proxy}" == https://* ]]; then
        new_proxy="http://${new_proxy#https://}"
        log_info "Converted HTTPS proxy to HTTP: ${new_proxy}"
    fi

    export https_proxy="${new_proxy}"

    if [[ -n "${https_proxy}" ]]; then
        log_succ "Successfully set https_proxy=${https_proxy}"
    else
        log_warn "https_proxy is empty after setting"
    fi
}

function check_miniforge() {

    log_info "Checking if Miniforge3 is installed..."

    # Use whereis to find conda installation
    local conda_paths=$(whereis conda)

    if [[ -n "${conda_paths}" && "${conda_paths}" != "conda:" ]]; then
        # Extract the first conda binary path from whereis output
        local conda_binary=$(echo "${conda_paths}" | awk '{print $2}')

        if [[ -n "${conda_binary}" && -f "${conda_binary}" ]]; then
            # Get the miniforge directory by going up from the conda binary path
            local miniforge_dir=$(dirname $(dirname "${conda_binary}"))

            if check_command "${conda_binary}"; then
                log_info "conda is already found in '${conda_binary}'. No need to install Miniforge3."
                MINIFORGE_DIR="${miniforge_dir}"
                return 0
            fi
        fi
    fi

    log_info "Miniforge3 installation not found"
    return 1
}

function install_miniforge() {

    if ! check_miniforge; then

        log_info "Downloading Miniforge3 installer..."

        if ! wget -P "${DOWNLOAD_DIR}" "${MINIFORGE_INSTALLER_URL}"; then
            log_error "Download of Miniforge3 installer failed"
        fi

        local installer_cmd=(
            sh "${DOWNLOAD_DIR}/${MINIFORGE_INSTALLER_NAME}"
            -b
            -p "${MINIFORGE_DIR}"
        )

        log_info "Installing Miniforge3..."
        if ! "${installer_cmd[@]}"; then
            log_warn "Miniforge3 installation failed. Trying with HTTPS proxy..."
            set_https_proxy

            if ! "${installer_cmd[@]}"; then
                log_warn "Miniforge3 installation failed. Trying with custom pip index URL..."
                export PIP_INDEX_URL="${GLOBAL_INDEX_URL}"

                if ! "${installer_cmd[@]}"; then
                    log_error "Miniforge3 installation failed after all attempts"
                fi
            fi
        fi

        log_info "Initializing conda..."
        export PATH="${MINIFORGE_DIR}/bin:${PATH}"

        if ! check_command conda; then
            log_error "conda command not found after installation"
        fi

        if ! conda init bash >/dev/null 2>&1; then
            log_warn "Failed to initialize conda for bash"
        fi

        source ~/.bashrc 2>/dev/null || true
    fi

    log_info "conda version: $(conda --version 2>/dev/null || echo "unknown")"

    if ! conda config --set auto_activate_base false; then
        log_warn "Failed to set 'auto_activate_base=false'"
    fi
}

function deploy_conda_env() {

    if conda env list | awk '{print $1}' | grep -q "^${CONDA_ENV_NAME}$"; then
        log_info "Conda environment '${CONDA_ENV_NAME}' already exists"
        return
    fi

    if ! check_command tar; then
        log_error "tar command is required to extract the archive"
    fi

    log_info "Downloading conda environment..."
    if wget -P "${DOWNLOAD_DIR}" "${LOCAGENT_CONDA_URL}"; then
        log_info "Download conda environment complete"
    else
        log_error "Download of conda environment failed"
    fi

    log_info "Creating conda environment '${CONDA_ENV_NAME}' with Python ${PYTHON_VERSION}..."

    local env_dir="${MINIFORGE_DIR}/envs/${CONDA_ENV_NAME}"
    if mkdir -p "${env_dir}"; then
        log_succ "Created conda environment directory: '${env_dir}'"
    else
        log_error "Failed to create conda environment directory: '${env_dir}'"
    fi

    local tar_path="${DOWNLOAD_DIR}/${LOCAGENT_CONDA_NAME}"
    if tar -xzf "${tar_path}" -C "${env_dir}"; then
        log_info "Decompressing compression '${tar_path}' to '${env_dir}'"
    else
        log_error "Failed to extract the archive: '${tar_path}'"
    fi

    (
        if ! cd "${env_dir}"; then
            log_error "Failed to enter directory: '${env_dir}'"
        fi

        if ! ./bin/conda-unpack; then
            log_error "Failed to unpack the conda environment"
        fi
    )

    case $? in
    0)
        log_info "Conda environment unpacked successfully"
        ;;
    1)
        log_error "Aborted due to previous errors"
        ;;
    *)
        log_error "Unexpected error during unpacking"
        ;;
    esac

    if ! conda env list | awk '{print $1}' | grep -q "^${CONDA_ENV_NAME}$"; then
        log_error "'${CONDA_ENV_NAME}' conda environment does not exist"
    fi

    log_succ "Conda environment '${CONDA_ENV_NAME}' created successfully"
}

function deploy_locagent_repo() {

    log_info "Deploying LocAgent repo from '${LOCAGENT_REPO_URL}'..."

    local locagent_folder="${ROOT_DIR}/${LOCAGENT_REPO_NAME%%.*}"
    if [[ -d "${locagent_folder}" ]]; then
        log_info "LocAgent repo already exists in '${locagent_folder}'"
        if ! rm -rf "${locagent_folder}"; then
            log_error "Failed to delete the existing repo: '${locagent_folder}'"
        fi
    fi

    if ! check_command unzip; then
        log_error "unzip command is required to extract the archive"
    fi

    log_info "Downloading LocAgent repo..."
    if wget -P "${DOWNLOAD_DIR}" "${LOCAGENT_REPO_URL}"; then
        log_info "Download LocAgent repo complete"
    else
        log_error "Download of LocAgent repo failed"
    fi

    local zip_path="${DOWNLOAD_DIR}/${LOCAGENT_REPO_NAME}"
    if unzip -q "${zip_path}" -d "${ROOT_DIR}"; then
        log_info "Decompression LocAgent repo successful"
    else
        log_error "Failed to extract the archive: '${zip_path}'"
    fi

    log_succ "Deploying LocAgent repo successful"
}

function main() {
    trap cleanup EXIT

    initialize
    check_strict_platform
    set_script_permissions
    check_python_env
    install_miniforge
    deploy_conda_env
    deploy_locagent_repo
}

main
