#!/bin/bash

readonly LOG_INFO="[INFO]"
readonly LOG_SUCC="[SUCCESS]"
readonly LOG_WARN="[WARNING]"
readonly LOG_ERROR="[ERROR]"

readonly COLOR_RED="\e[1;31m"
readonly COLOR_BLUE="\e[1;34m"
readonly COLOR_GREEN="\e[1;32m"
readonly COLOR_YELLOW="\e[1;33m"
readonly COLOR_DEFAULT="\e[0m"
readonly COLOR_BOLD="\e[1m"

readonly IPV4_REGEX='^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
readonly PORT_REGEX='^(6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{3}|10[2-9][0-9]|102[5-9])$'

readonly PWD_DIR=$(pwd)
readonly ROOT_DIR=$(dirname "$(readlink -f "$0")")
MINIFORGE_DIR="${HOME}/miniforge3"

readonly LOCAGENT_DIR="${ROOT_DIR}/LocAgent"
readonly LOCAGENT_MCP_DIR="${LOCAGENT_DIR}/mcp_service"
readonly LOCAGENT_MCP_SCRIPT_NAME="locagent_mcp.py"
readonly LOCAGENT_MCP_SCRIPT_PATH="${LOCAGENT_MCP_DIR}/${LOCAGENT_MCP_SCRIPT_NAME}"

readonly CONDA_ENV_NAME="locagent"
readonly PYTHON_VERSION="3.12"

AGENT_MCP_HOST=''
AGENT_MCP_PORT=''
AGENT_REPO_DIR=''
DISABLE_VECTOR_RETRIEVE=''

function log_info() {
    local message="${1:-}"
    echo -e "${COLOR_BLUE}${LOG_INFO}${COLOR_DEFAULT} ${message}."
}

function log_succ() {
    local message="${1:-}"
    echo -e "${COLOR_GREEN}${LOG_SUCC}${COLOR_DEFAULT} ${message}!"
}

function log_warn() {
    local message="${1:-}"
    echo -e "${COLOR_YELLOW}${LOG_WARN}${COLOR_DEFAULT} ${message}!" >&2
}

function log_error() {
    local message="${1:-}"
    local exit_code="${2:-1}"

    echo -e "${COLOR_RED}${LOG_ERROR}${COLOR_DEFAULT} ${message}!" >&2
    exit "${exit_code}"
}

function check_command() {
    if ! command -v "$1" >/dev/null 2>&1; then
        log_warn "Command '$1' not found"
        return 1
    fi
    return 0
}

function show_help() {

    local script_name=$(basename "$0")
    local help_text="\

${COLOR_GREEN}${script_name} - LocAgent MCP Service${COLOR_DEFAULT}

${COLOR_BOLD}Usage:${COLOR_DEFAULT}
  ${script_name} [Options]

${COLOR_BOLD}Required options:${COLOR_DEFAULT}
  --repo=<REPO_PATH>     Specify the code repository path

${COLOR_BOLD}Optional options:${COLOR_DEFAULT}
  --host=<IP>          Specified service IP (default: 127.0.0.1)
  --port=<PORT>        Specified port (default: 8080)
  --disable_vector_retrieve   Disable vector indexing and retrieval
  --help               Display this help information

${COLOR_BOLD}Option format:${COLOR_DEFAULT}
  Support --host *********** or --host=*********** Two ways of writing

${COLOR_BOLD}Example:${COLOR_DEFAULT}
  ${script_name} --repo=/data/repo
  ${script_name} --repo=/data/repo --host=******** --port=9000
"
    echo -e "${help_text}"
    exit 0
}

function show_options_env() {

    echo -e "${COLOR_BLUE} Options: ${COLOR_DEFAULT}"
    echo -e "${COLOR_BLUE} - repo-path =${COLOR_DEFAULT} ${AGENT_REPO_DIR}"
    echo -e "${COLOR_BLUE} - host-ip =${COLOR_DEFAULT} ${AGENT_MCP_HOST}"
    echo -e "${COLOR_BLUE} - port =${COLOR_DEFAULT} ${AGENT_MCP_PORT}"

    echo -e "${COLOR_BLUE} Environmental variable: ${COLOR_DEFAULT}"
    echo -e "${COLOR_BLUE} - OPENAI_API_KEY =${COLOR_DEFAULT} ${OPENAI_API_KEY}"
    echo -e "${COLOR_BLUE} - OPENAI_API_BASE =${COLOR_DEFAULT} ${OPENAI_API_BASE}"
    echo -e "${COLOR_BLUE} - LOCAGENT_MCP_HOST =${COLOR_DEFAULT} ${LOCAGENT_MCP_HOST}"
    echo -e "${COLOR_BLUE} - LOCAGENT_MCP_PORT =${COLOR_DEFAULT} ${LOCAGENT_MCP_PORT}"
    echo -e "${COLOR_BLUE} - LOCAGENT_ROOT_DIR =${COLOR_DEFAULT} ${LOCAGENT_ROOT_DIR}"
    echo -e "${COLOR_BLUE} - LOCAGENT_REPO_DIR =${COLOR_DEFAULT} ${LOCAGENT_REPO_DIR}"
    echo -e "${COLOR_BLUE} - GRAPH_INDEX_DIR =${COLOR_DEFAULT} ${GRAPH_INDEX_DIR}"
    echo -e "${COLOR_BLUE} - BM25_INDEX_DIR =${COLOR_DEFAULT} ${BM25_INDEX_DIR}"
    echo -e "${COLOR_BLUE} - VECTOR_INDEX_DIR =${COLOR_DEFAULT} ${VECTOR_INDEX_DIR}"
    echo -e "${COLOR_BLUE} - PYTHONPATH =${COLOR_DEFAULT} ${PYTHONPATH}"
    echo -e "${COLOR_BLUE} - all_proxy =${COLOR_DEFAULT} ${all_proxy}"
    echo -e "${COLOR_BLUE} - https_proxy =${COLOR_DEFAULT} ${https_proxy}"
    echo -e "${COLOR_BLUE} - DISABLE_VECTOR_RETRIEVE =${COLOR_DEFAULT} ${DISABLE_VECTOR_RETRIEVE}"
}

function parse_options() {

    local TEMP
    if ! TEMP=$(getopt -o '' --long help,host:,port:,repo:,disable_vector_retrieve -n "$0" -- "$@"); then
        log_error "Parameter parsing failed and the program terminated..."
    fi

    eval set -- "${TEMP}"

    while true; do
        case "$1" in
        --help)
            show_help
            ;;
        --host)
            AGENT_MCP_HOST="$2"
            shift 2
            ;;
        --port)
            AGENT_MCP_PORT="$2"
            shift 2
            ;;
        --repo)
            AGENT_REPO_DIR="$2"
            shift 2
            ;;
        --disable_vector_retrieve)
            DISABLE_VECTOR_RETRIEVE='True'
            shift
            ;;
        --)
            shift
            break
            ;;
        *)
            log_error "Unknown option parsing situation"
            ;;
        esac
    done
}

function check_options() {

    if [[ -n "${AGENT_MCP_HOST}" && "${AGENT_MCP_HOST}" =~ "${IPV4_REGEX}" ]]; then
        log_error "Invalid IPv4 address format ${AGENT_MCP_HOST}"
    fi

    if [[ -n "${AGENT_MCP_PORT}" && "${AGENT_MCP_PORT}" =~ "${PORT_REGEX}" ]]; then
        log_error "Invalid port number: ${AGENT_MCP_PORT} (must be between 1025 and 65535)"
    fi

    if [[ "${AGENT_REPO_DIR}" == '' ]]; then
        log_error "repo option is not received, and the configuration is missing."
    elif [[ ! -d "${AGENT_REPO_DIR}" ]]; then
        log_error "The directory does not exist: ${AGENT_REPO_DIR}"
    fi
}

function check_miniforge() {

    log_info "Checking if Miniforge3 is installed..."

    # Use whereis to find conda installation
    local conda_paths=$(whereis conda)

    if [[ -n "${conda_paths}" && "${conda_paths}" != "conda:" ]]; then
        # Extract the first conda binary path from whereis output
        local conda_binary=$(echo "${conda_paths}" | awk '{print $2}')

        if [[ -n "${conda_binary}" && -f "${conda_binary}" ]]; then
            # Get the miniforge directory by going up from the conda binary path
            local miniforge_dir=$(dirname $(dirname "${conda_binary}"))

            if check_command "${conda_binary}"; then
                log_info "conda is already found in '${conda_binary}'. No need to install Miniforge3."
                MINIFORGE_DIR="${miniforge_dir}"
                return 0
            fi
        fi
    fi

    log_info "Miniforge3 installation not found"
    return 1
}

function check_conda_env() {

    if ! check_miniforge; then
        log_error "Miniforge3 installation not found"
    fi

    if ! check_command conda; then
        log_error "conda command not found"
    fi

    log_info "Run miniforge3 activate conda interactions..."
    source "${MINIFORGE_DIR}/etc/profile.d/conda.sh"

    if ! conda env list | awk '{print $1}' | grep -q "^${CONDA_ENV_NAME}$"; then
        log_error "Conda environment '${CONDA_ENV_NAME}' not found"
    fi

    log_info "Activate the conda environment '${CONDA_ENV_NAME}'..."
    if ! conda activate "${CONDA_ENV_NAME}"; then
        log_error "Failed to activate the conda environment '${CONDA_ENV_NAME}'"
    fi

    if [[ "${CONDA_DEFAULT_ENV}" != "${CONDA_ENV_NAME}" ]]; then
        log_error "The default conda environment is '${CONDA_DEFAULT_ENV}', which may not match the activated environment '${CONDA_ENV_NAME}'"
    fi
}

function set_agent_env() {

    if [[ -n "${AGENT_MCP_HOST}" ]]; then
        export LOCAGENT_MCP_HOST="${AGENT_MCP_HOST}"
    fi

    if [[ -n "${AGENT_MCP_PORT}" ]]; then
        export LOCAGENT_MCP_PORT="${AGENT_MCP_PORT}"
    fi

    export OPENAI_API_KEY="8612e7bd0533452f9e88382cf3112ba4"
    export OPENAI_API_BASE="https://maas-apigateway.dt.zte.com.cn/STREAM/qwen3-235b-a22b/v1"

    export all_proxy="http://proxyhk.zte.com.cn:80"
    export https_proxy="http://proxyhk.zte.com.cn:80"

    if [[ -n "${PYTHONPATH}" ]]; then
        export PYTHONPATH="${PYTHONPATH}:${LOCAGENT_DIR}"
    else
        export PYTHONPATH="${LOCAGENT_DIR}"
    fi

    export LOCAGENT_ROOT_DIR="${LOCAGENT_DIR}"
    export LOCAGENT_REPO_DIR="${AGENT_REPO_DIR}"

    export GRAPH_INDEX_DIR="${HOME}/.zeroAgent/index/graph_index_v2.3"
    export BM25_INDEX_DIR="${HOME}/.zeroAgent/index/BM25_index"
    export VECTOR_INDEX_DIR="${HOME}/.zeroAgent/index/VECTOR_index"
    export DISABLE_VECTOR_RETRIEVE="${DISABLE_VECTOR_RETRIEVE}"
}

function main() {

    parse_options "$@"
    set_agent_env
    show_options_env
    check_options
    check_conda_env
    python "${LOCAGENT_MCP_SCRIPT_PATH}"
}

main "$@"
