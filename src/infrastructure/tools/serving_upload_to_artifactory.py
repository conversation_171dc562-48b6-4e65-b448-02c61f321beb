import requests
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)

def upload_to_artifactory(
        file_path: Union[str, Path],
        artifactory_url: str,
        api_key: Optional[str] = None
) -> bool:
    """
    Upload a file to JFrog Artifactory with authentication.

    Args:
        file_path (Union[str, Path]): Local path to the file to upload
        artifactory_url (str): Base URL of Artifactory (e.g., http://your-artifactory:8081/artifactory/my-repo/path/to/file.ext)
        api_key (Optional[str]): Artifactory API key for authentication

    Returns:
        bool: True if upload successful, False otherwise

    Raises:
        FileNotFoundError: If the specified file does not exist
        ValueError: If authentication credentials are invalid or missing
    """
    # Convert file_path to Path object and validate
    file_path = Path(file_path)
    if not file_path.is_file():
        raise FileNotFoundError(f"File not found: {file_path}")

    # Prepare authentication
    auth = None
    headers = {}

    if api_key:
        headers["X-JFrog-Art-Api"] = api_key
    else:
        raise ValueError("API key must be provided")

    try:
        # Open file in binary mode and upload
        with open(file_path, 'rb') as file:
            response = requests.put(
                url=artifactory_url,
                data=file,
                auth=auth,
                headers=headers
            )

        # Check response status
        if response.status_code == 201:
            logger.info(f"Successfully uploaded {file_path.name} to {artifactory_url}")
            return True
        else:
            logger.info(f"Failed to upload {file_path.name}. Status: {response.status_code}, Response: {response.text}")
            return False

    except requests.RequestException as e:
        logger.error(f"Error uploading file: {str(e)}")
        return False
