bo:
  archiveStatus: 0
  contentBody: <div class="copyFromZEditor"><p>注：</p><p>1、一行一条信息，不得合并单元格，否则页面解析失败</p><p>2、查表时，将“所属子系统”、“发现版本”、“迭代”为组合查询，查询结果需要唯一，否则报错</p><table
    data-sort="sortEnabled" interlaced="disabled" width="1903"><tbody><tr class="firstRow"><th
    class="line-number" width="47"><p>#</p></th><th width="122"><p>所属子系统</p></th><th
    width="269"><p>发现版本</p></th><th width="85"><p>迭代</p></th><th width="201"><p>代码库</p></th><th
    width="140"><p>代码分支</p></th><th width="585"><p>开发镜像</p></th><th width="209"><p>测试脚本所在的代码库</p></th><th
    width="244"><p>测试脚本所在的代码库分支</p></th></tr><tr><td class="line-number" width="47"
    contenteditable="false">1</td><td width="122"><p>hdfs</p></td><td width="269"><p>NetNumen
    DAIPV20.25.40.05</p></td><td width="85"><p>25R5-1</p></td><td width="201"><p>DAP/ZDH/Hadoop</p></td><td
    width="140"><p>develop-3.4.1</p></td><td width="585"><p>dap-release-docker.artnj.zte.com.cn/cci/dap/common-build:250117</p></td><td
    width="209"><p>DAP/demo/Test</p></td><td width="244"><p>master</p></td></tr><tr><td
    class="line-number" width="47" contenteditable="false">2</td><td width="122"><p>hdfs</p></td><td
    width="269"><p>NetNumen DAIPV20.25.40.05</p></td><td width="85"><p>25R5-2</p></td><td
    width="201"><p>DAP/ZDH/Hadoop</p></td><td width="140"><p>develop-3.4.1</p></td><td
    width="585"><p>dap-release-docker.artnj.zte.com.cn/cci/dap/common-build:250117</p></td><td
    width="209"><p>DAP/demo/Test</p></td><td width="244"><p>master</p></td></tr><tr><td
    class="line-number" width="47" contenteditable="false">3</td><td width="122"><p>DLF</p></td><td
    width="269"><p>NetNumen DAIPV20.25.40.08</p></td><td width="85"><p>25R8-2</p></td><td
    width="201"><p>DAP/MANAGER/DLG</p></td><td width="140"><p>develop</p></td><td
    width="585"><p>dap-release-docker.artnj.zte.com.cn/cci/dap/common-build:250117</p></td><td
    width="209"><p>DAP/demo/Test</p></td><td width="244"><p>master</p></td></tr><tr><td
    class="line-number" width="47" contenteditable="false">4</td><td width="122"><p>hdfs</p></td><td
    width="269"><p>NetNumen DAIPV20.25.40.05</p></td><td width="85"><p>default</p></td><td
    width="201"><p>DAP/ZDH/Hadoop</p></td><td width="140"><p>release/15.5.1</p></td><td
    width="585"><p>dap-release-docker.artnj.zte.com.cn/cci/dap/common-build:250117</p></td><td
    width="209" class=""><br></td><td width="244" class=""><br></td></tr><tr><td class="line-number"
    width="47" contenteditable="false">5</td><td width="122"><p>hdfs</p></td><td width="269"><p>NetNumen
    DAIPV20.24.40.06</p></td><td width="85"><p>default</p></td><td width="201"><p>DAP/ZDH/Hadoop</p></td><td
    width="140"><p>release/14.6.1</p></td><td width="585"><p>docker.artnj.zte.com.cn/cci/dap/common-build:180905</p></td><td
    width="209" class=""><br></td><td width="244" class=""><br></td></tr><tr><td class="line-number"
    width="47" contenteditable="false">6</td><td width="122"><p>BDR</p></td><td width="269"><p>NetNumen
    DAIPV20.25.40.07</p></td><td width="85"><p>default</p></td><td width="201"><p>DAP/MANAGER/BDR</p></td><td
    width="140"><p>release/15.7.1</p></td><td width="585"><p>dap-release-docker.artnj.zte.com.cn/cci/dap/common-build:250117</p></td><td
    width="209" class=""><br></td><td width="244" class=""><br></td></tr><tr><td class="line-number"
    width="47" contenteditable="false">7</td><td width="122"><p>DLF</p></td><td width="269"><p>NetNumen
    DAIPV20.25.40.08</p></td><td width="85"><p>default</p></td><td width="201"><p>DAP/MANAGER/DLG</p></td><td
    width="140"><p>release/15.8.1</p></td><td width="585"><p>dap-release-docker.artnj.zte.com.cn/cci/dap/common-build:250117</p></td><td
    width="209"><p>DAP/demo/Test2</p></td><td width="244"><p>master</p></td></tr><tr><td
    class="line-number" width="47" contenteditable="false">8</td><td width="122"><p>DLF</p></td><td
    width="269"><p>NetNumen DAIPV20.24.40.06</p></td><td width="85"><p>default</p></td><td
    width="201"><p>DAP/MANAGER/DLG</p></td><td width="140"><p>release/14.6.1</p></td><td
    width="585"><p>docker.artnj.zte.com.cn/cci/dap/common-build:180905</p></td><td
    width="209"><p>DAP/demo/Test3</p></td><td width="244"><p>master</p></td></tr></tbody></table><p><br></p></div><p><br></p>
  contentFrom: SPACE
  contentType: ARTICLE
  createBy: 魏然10155493
  createDate: '2025-07-04 14:52:07'
  createNo: '10155493'
  currentVersion: '20250708161115'
  description: ''
  disabled: false
  encryptAlg: KMS
  encrypted: true
  id: 098d8478adec4b00b55bf03ae6375907
  isLeaf: true
  parentId: 3a4a1e87290947d99b95cd76ef6d47c7
  parentPath: 0-a641a4a213b24fd2926ec4ff48727670-628ad62256d84ce2a2b940fec51a9523-3a4a1e87290947d99b95cd76ef6d47c7
  sortNo: 0
  spaceId: 80efb5cf1135447e99b13184a80d5421
  spaceName: 魏然10155493个人空间
  spaceType: 0
  title: 3.2 信息映射表-DAIP示例
  updateBy: 魏然10155493
  updateDate: '2025-07-08 16:11:16'
  updateNo: '10155493'
  useMacro: false
code:
  code: '0000'
  msgId: RetCode.Success
