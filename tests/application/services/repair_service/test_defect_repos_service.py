import pytest
from unittest.mock import Mock, patch
import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parents[4]
sys.path.insert(0, str(BASE_DIR))

from src.application.services.repair_service.defect_repos_service import DefectRepositoryMappingService


class TestDefectRepositoryMappingService:
    """DefectRepositoryMappingService单元测试类"""

    @pytest.fixture
    def mock_ict_handler(self):
        """Mock IcenterTool实例"""
        mock_handler = Mock()
        mock_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        return mock_handler

    @pytest.fixture
    def mock_ict_table_analysis(self):
        """Mock IctTableAnalysis实例"""
        mock_analysis = Mock()
        return mock_analysis

    @pytest.fixture
    def mock_pg_handler(self):
        """Mock DefectReposHandler实例"""
        mock_handler = Mock()
        return mock_handler

    @pytest.fixture
    def service_instance_real_analysis(self, mock_ict_handler, mock_pg_handler):
        """创建测试用的DefectRepositoryMappingService实例 - 使用真实的IctTableAnalysis"""
        with patch('src.application.services.repair_service.defect_repos_service.IcenterTool', return_value=mock_ict_handler), \
             patch('src.application.services.repair_service.defect_repos_service.DefectReposHandler', return_value=mock_pg_handler):
            
            service = DefectRepositoryMappingService(
                user_id='test_user',
                user_pwd='test_pwd',
                ict_sid='test_sid',
                ict_cid='test_cid',
                pg_host='localhost',
                pg_port=5432,
                pg_user='test_user',
                pg_pwd='test_pwd',
                pg_name='test_db',
                workspace_key='TEST'
            )
            return service

    @pytest.fixture
    def service_instance(self, mock_ict_handler, mock_ict_table_analysis, mock_pg_handler):
        """创建测试用的DefectRepositoryMappingService实例"""
        with patch('src.application.services.repair_service.defect_repos_service.IcenterTool', return_value=mock_ict_handler), \
             patch('src.application.services.repair_service.defect_repos_service.IctTableAnalysis', return_value=mock_ict_table_analysis), \
             patch('src.application.services.repair_service.defect_repos_service.DefectReposHandler', return_value=mock_pg_handler):
            
            service = DefectRepositoryMappingService(
                user_id='test_user',
                user_pwd='test_pwd',
                ict_sid='test_sid',
                ict_cid='test_cid',
                pg_host='localhost',
                pg_port=5432,
                pg_user='test_user',
                pg_pwd='test_pwd',
                pg_name='test_db',
                workspace_key='TEST'
            )
            return service

    def test_init(self, mock_ict_handler, mock_ict_table_analysis, mock_pg_handler):
        """测试初始化方法"""
        with patch('src.application.services.repair_service.defect_repos_service.IcenterTool', return_value=mock_ict_handler), \
             patch('src.application.services.repair_service.defect_repos_service.IctTableAnalysis', return_value=mock_ict_table_analysis), \
             patch('src.application.services.repair_service.defect_repos_service.DefectReposHandler', return_value=mock_pg_handler):
            
            service = DefectRepositoryMappingService(
                user_id='test_user',
                user_pwd='test_pwd',
                ict_sid='test_sid',
                ict_cid='test_cid',
                pg_host='localhost',
                pg_port=5432,
                pg_user='test_user',
                pg_pwd='test_pwd',
                pg_name='test_db',
                workspace_key='TEST'
            )
            
            assert service.ict_sid == 'test_sid'
            assert service.ict_cid == 'test_cid'
            assert service.workspace_key == 'TEST'
            assert service.ict_handler == mock_ict_handler
            assert service.ict_table_analysis == mock_ict_table_analysis
            assert service.pg_handler == mock_pg_handler

    def test_extract_field_content_string(self, service_instance):
        """测试_extract_field_content方法 - 字符串类型"""
        result = service_instance._extract_field_content("test_value")
        assert result == "test_value"

    def test_extract_field_content_dict_with_content(self, service_instance):
        """测试_extract_field_content方法 - 包含content字段的字典"""
        field_value = {"content": "test_content"}
        result = service_instance._extract_field_content(field_value)
        assert result == "test_content"

    def test_extract_field_content_dict_without_content(self, service_instance):
        """测试_extract_field_content方法 - 不包含content字段的字典"""
        field_value = {"key": "value"}
        result = service_instance._extract_field_content(field_value)
        assert result == "{'key': 'value'}"

    def test_extract_field_content_object_with_content(self, service_instance):
        """测试_extract_field_content方法 - 包含content属性的对象"""
        class MockObject:
            def __init__(self):
                self.content = "test_content"
        
        field_value = MockObject()
        result = service_instance._extract_field_content(field_value)
        assert result == "test_content"

    def test_extract_field_content_empty_string(self, service_instance):
        """测试_extract_field_content方法 - 空字符串"""
        result = service_instance._extract_field_content("")
        assert result == ""

    def test_extract_field_content_none(self, service_instance):
        """测试_extract_field_content方法 - None值"""
        result = service_instance._extract_field_content(None)
        assert result == "None"

    def test_check_defect_repos_mapping_datas_valid(self, service_instance):
        """测试_check_defect_repos_mapping_datas方法 - 有效数据"""
        valid_data = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image',
            '测试脚本所在的代码库': 'test_script_repo',
            '测试脚本所在的代码库分支': 'test_script_branch'
        }]
        
        # 不应该抛出异常
        service_instance._check_defect_repos_mapping_datas(valid_data)

    def test_check_defect_repos_mapping_datas_empty(self, service_instance):
        """测试_check_defect_repos_mapping_datas方法 - 空数据"""
        with pytest.raises(ValueError, match="缺陷代码库映射信息不能为空"):
            service_instance._check_defect_repos_mapping_datas([])

    def test_check_defect_repos_mapping_datas_missing_required_field(self, service_instance):
        """测试_check_defect_repos_mapping_datas方法 - 缺少必填字段"""
        invalid_data = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            # 缺少'迭代'字段
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image'
        }]
        
        with pytest.raises(ValueError, match="第1条数据缺少必填字段: 迭代"):
            service_instance._check_defect_repos_mapping_datas(invalid_data)

    def test_check_defect_repos_mapping_datas_empty_field(self, service_instance):
        """测试_check_defect_repos_mapping_datas方法 - 空字段值"""
        invalid_data = [{
            '所属子系统': '',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image'
        }]
        
        with pytest.raises(ValueError, match="第1条数据的字段 所属子系统 不能为空"):
            service_instance._check_defect_repos_mapping_datas(invalid_data)

    def test_check_defect_repos_mapping_datas_duplicate(self, service_instance):
        """测试_check_defect_repos_mapping_datas方法 - 重复数据"""
        duplicate_data = [
            {
                '所属子系统': 'test_system',
                '发现版本': 'v1.0',
                '迭代': 'iter1',
                '代码库': 'test_repo1',
                '代码分支': 'main',
                '开发镜像': 'test_image1'
            },
            {
                '所属子系统': 'test_system',
                '发现版本': 'v1.0',
                '迭代': 'iter1',
                '代码库': 'test_repo2',
                '代码分支': 'main',
                '开发镜像': 'test_image2'
            }
        ]
        
        with pytest.raises(ValueError, match="存在重复数据"):
            service_instance._check_defect_repos_mapping_datas(duplicate_data)

    def test_check_defect_repos_mapping_datas_invalid_type(self, service_instance):
        """测试_check_defect_repos_mapping_datas方法 - 无效数据类型"""
        invalid_data = ["not_a_dict"]
        
        with pytest.raises(ValueError, match="第1条数据格式错误，应为字典类型"):
            service_instance._check_defect_repos_mapping_datas(invalid_data)

    def test_convert_icenter_table_data(self, service_instance):
        """测试_convert_icenter_table_data方法"""
        icenter_data = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image',
            '测试脚本所在的代码库': 'test_script_repo',
            '测试脚本所在的代码库分支': 'test_script_branch'
        }]
        
        result = service_instance._convert_icenter_table_data(icenter_data)
        
        expected = [{
            'belong_sub_system': 'test_system',
            'discovery_iversion': 'v1.0',
            'system_iteration': 'iter1',
            'repository': 'test_repo',
            'branch': 'main',
            'dev_image': 'test_image',
            'test_script_repository': 'test_script_repo',
            'test_script_branch': 'test_script_branch'
        }]
        
        assert result == expected

    def test_convert_icenter_table_data_with_empty_optional_fields(self, service_instance):
        """测试_convert_icenter_table_data方法 - 可选字段为空"""
        icenter_data = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image',
            '测试脚本所在的代码库': '',
            '测试脚本所在的代码库分支': ''
        }]
        
        result = service_instance._convert_icenter_table_data(icenter_data)
        
        assert result[0]['test_script_repository'] is None
        assert result[0]['test_script_branch'] is None

    def test_convert_icenter_table_data_missing_optional_fields(self, service_instance):
        """测试_convert_icenter_table_data方法 - 缺少可选字段"""
        icenter_data = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image'
            # 缺少测试脚本相关字段
        }]
        
        result = service_instance._convert_icenter_table_data(icenter_data)
        
        assert result[0]['test_script_repository'] is None
        assert result[0]['test_script_branch'] is None

    def test_get_defect_repos_mapping_from_icenter_page_success(self, service_instance, mock_ict_handler, mock_ict_table_analysis):
        """测试get_defect_repos_mapping_from_icenter_page方法 - 成功获取数据"""
        # 设置Mock返回值
        mock_ict_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        
        mock_ict_table_analysis.search_table.return_value = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image'
        }]
        
        result = service_instance.get_defect_repos_mapping_from_icenter_page()
        
        # 验证调用
        mock_ict_handler.page_read.assert_called_once_with('test_sid', 'test_cid')
        mock_ict_table_analysis.search_table.assert_called_once()
        
        # 验证结果
        assert len(result) == 1
        assert result[0]['belong_sub_system'] == 'test_system'

    def test_get_defect_repos_mapping_from_icenter_page_with_real_data(self, service_instance_real_analysis, mock_ict_handler):
        """测试get_defect_repos_mapping_from_icenter_page方法 - 使用真实YAML数据和真实的IctTableAnalysis"""
        # 加载YAML文件
        yaml_file_path = Path(__file__).parent / "DAIP_defect_repos_mapping_from_icenter_page_demo.yaml"
        with open(yaml_file_path, 'r', encoding='utf-8') as f:
            real_page_data = yaml.safe_load(f)
        
        # 设置Mock返回值
        mock_ict_handler.page_read.return_value = real_page_data
        
        # 使用真实的IctTableAnalysis，不需要mock search_table
        
        result = service_instance_real_analysis.get_defect_repos_mapping_from_icenter_page()
        
        # 验证调用
        mock_ict_handler.page_read.assert_called_once_with('test_sid', 'test_cid')
        
        # 验证结果 - 根据实际YAML文件内容调整期望值
        assert len(result) > 0  # 确保有数据返回
        # 验证返回的数据结构是否正确
        assert 'belong_sub_system' in result[0]
        assert 'discovery_iversion' in result[0]

    def test_get_defect_repos_mapping_from_icenter_page_no_data(self, service_instance, mock_ict_handler, mock_ict_table_analysis):
        """测试get_defect_repos_mapping_from_icenter_page方法 - 无数据"""
        mock_ict_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        
        mock_ict_table_analysis.search_table.return_value = []
        
        with pytest.raises(ValueError, match="未从icenter页面获取到缺陷代码库映射信息"):
            service_instance.get_defect_repos_mapping_from_icenter_page()

    def test_get_defect_repos_mapping_from_icenter_page_dict_data(self, service_instance, mock_ict_handler, mock_ict_table_analysis):
        """测试get_defect_repos_mapping_from_icenter_page方法 - 字典数据"""
        mock_ict_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        
        mock_ict_table_analysis.search_table.return_value = {
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image'
        }
        
        result = service_instance.get_defect_repos_mapping_from_icenter_page()
        
        assert len(result) == 1
        assert result[0]['belong_sub_system'] == 'test_system'

    def test_get_defect_repos_mapping_from_icenter_page_invalid_data_type(self, service_instance, mock_ict_handler, mock_ict_table_analysis):
        """测试get_defect_repos_mapping_from_icenter_page方法 - 无效数据类型"""
        mock_ict_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        
        mock_ict_table_analysis.search_table.return_value = "not_a_list_or_dict"
        
        with pytest.raises(ValueError, match="意外的数据类型"):
            service_instance.get_defect_repos_mapping_from_icenter_page()

    def test_update_defect_repos_mapping_to_pg(self, service_instance, mock_pg_handler):
        """测试update_defect_repos_mapping_to_pg方法"""
        test_data = [{
            'belong_sub_system': 'test_system',
            'discovery_iversion': 'v1.0',
            'system_iteration': 'iter1',
            'repository': 'test_repo',
            'branch': 'main',
            'dev_image': 'test_image'
        }]
        
        service_instance.update_defect_repos_mapping_to_pg(test_data)
        
        mock_pg_handler.update_defect_repos_mapping_to_pg_main.assert_called_once_with('TEST', test_data)

    def test_update_repos_from_icenter_page_main(self, service_instance, mock_ict_handler, mock_ict_table_analysis, mock_pg_handler):
        """测试update_repos_from_icenter_page_main方法"""
        # 设置Mock返回值
        mock_ict_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        
        mock_ict_table_analysis.search_table.return_value = [{
            '所属子系统': 'test_system',
            '发现版本': 'v1.0',
            '迭代': 'iter1',
            '代码库': 'test_repo',
            '代码分支': 'main',
            '开发镜像': 'test_image'
        }]
        
        service_instance.update_repos_from_icenter_page_main()
        
        # 验证调用链
        mock_ict_handler.page_read.assert_called_once()
        mock_ict_table_analysis.search_table.assert_called_once()
        mock_pg_handler.update_defect_repos_mapping_to_pg_main.assert_called_once()

    def test_update_repos_from_icenter_page_main_no_data(self, service_instance, mock_ict_handler, mock_ict_table_analysis, mock_pg_handler):
        """测试update_repos_from_icenter_page_main方法 - 无数据"""
        mock_ict_handler.page_read.return_value = {
            'bo': {'contentBody': '<html><table></table></html>'}
        }
        
        mock_ict_table_analysis.search_table.return_value = []
        
        with pytest.raises(ValueError):
            service_instance.update_repos_from_icenter_page_main()
