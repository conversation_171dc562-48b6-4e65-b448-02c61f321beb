import pytest
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parents[5]
sys.path.insert(0, str(BASE_DIR))

from src.domain.subdomains.repair_agent.database.defect_repos_handler import DefectReposPGHandler


class TestDefectReposHandler:
    """DefectReposHandler单元测试类"""

    @pytest.fixture
    def mock_postgres_db(self):
        """Mock PostgresDB实例"""
        mock_db = Mock()
        return mock_db

    @pytest.fixture
    def handler_instance(self, mock_postgres_db):
        """创建测试用的DefectReposHandler实例"""
        with patch('src.domain.subdomains.repair_agent.database.defect_repos_handler.PostgresDB', return_value=mock_postgres_db):
            handler = DefectReposPGHandler(
                db_host='localhost',
                db_port=5432,
                db_user='test_user',
                db_password='test_pwd',
                db_name='test_db'
            )
            return handler

    def test_init(self, mock_postgres_db):
        """测试初始化方法"""
        with patch('src.domain.subdomains.repair_agent.database.defect_repos_handler.PostgresDB', return_value=mock_postgres_db):
            handler = DefectReposPGHandler(
                db_host='localhost',
                db_port=5432,
                db_user='test_user',
                db_password='test_pwd',
                db_name='test_db'
            )
            
            assert handler.db == mock_postgres_db
            assert handler.table_name == "defect_fix_repository_mapping"

    def test_delete_existing_records(self, handler_instance, mock_postgres_db):
        """测试_delete_existing_records方法"""
        workspace_key = "TEST_WORKSPACE"
        mock_postgres_db.delete_data.return_value = 5
        
        result = handler_instance._delete_existing_records(workspace_key)
        
        mock_postgres_db.delete_data.assert_called_once_with(
            "defect_fix_repository_mapping", 
            {"workspace_key": workspace_key}
        )
        assert result == 5

    def test_insert_new_records(self, handler_instance, mock_postgres_db):
        """测试_insert_new_records方法"""
        workspace_key = "TEST_WORKSPACE"
        defect_repos_mapping_datas = [
            {
                "belong_sub_system": "test_system",
                "discovery_iversion": "v1.0",
                "system_iteration": "iter1",
                "repository": "test_repo",
                "branch": "main",
                "dev_image": "test_image",
                "test_script_repository": "test_script_repo",
                "test_script_branch": "test_script_branch"
            },
            {
                "belong_sub_system": "test_system2",
                "discovery_iversion": "v1.1",
                "system_iteration": "iter2",
                "repository": "test_repo2",
                "branch": "develop",
                "dev_image": "test_image2",
                "test_script_repository": None,
                "test_script_branch": None
            }
        ]
        
        mock_postgres_db.insert_batch_data.return_value = 2
        
        result = handler_instance._insert_new_records(workspace_key, defect_repos_mapping_datas)
        
        # 验证调用参数
        call_args = mock_postgres_db.insert_batch_data.call_args
        assert call_args[0][0] == "defect_fix_repository_mapping"
        
        insert_data_list = call_args[0][1]
        assert len(insert_data_list) == 2
        
        # 验证第一条数据
        first_record = insert_data_list[0]
        assert first_record["workspace_key"] == workspace_key
        assert first_record["belong_sub_system"] == "test_system"
        assert first_record["discovery_iversion"] == "v1.0"
        assert first_record["system_iteration"] == "iter1"
        assert first_record["repository"] == "test_repo"
        assert first_record["branch"] == "main"
        assert first_record["dev_image"] == "test_image"
        assert first_record["test_script_repository"] == "test_script_repo"
        assert first_record["test_script_branch"] == "test_script_branch"
        
        # 验证第二条数据（可选字段为None）
        second_record = insert_data_list[1]
        assert second_record["test_script_repository"] is None
        assert second_record["test_script_branch"] is None
        
        assert result == 2

    def test_insert_new_records_with_missing_optional_fields(self, handler_instance, mock_postgres_db):
        """测试_insert_new_records方法 - 缺少可选字段"""
        workspace_key = "TEST_WORKSPACE"
        defect_repos_mapping_datas = [
            {
                "belong_sub_system": "test_system",
                "discovery_iversion": "v1.0",
                "system_iteration": "iter1",
                "repository": "test_repo",
                "branch": "main",
                "dev_image": "test_image"
                # 缺少测试脚本相关字段
            }
        ]
        
        mock_postgres_db.insert_batch_data.return_value = 1
        
        result = handler_instance._insert_new_records(workspace_key, defect_repos_mapping_datas)
        
        call_args = mock_postgres_db.insert_batch_data.call_args
        insert_data_list = call_args[0][1]
        
        record = insert_data_list[0]
        assert record["test_script_repository"] is None
        assert record["test_script_branch"] is None
        
        assert result == 1

    def test_update_defect_repos_mapping_to_pg_main_with_data(self, handler_instance, mock_postgres_db):
        """测试update_defect_repos_mapping_to_pg_main方法 - 有数据"""
        workspace_key = "TEST_WORKSPACE"
        defect_repos_mapping_datas = [
            {
                "belong_sub_system": "test_system",
                "discovery_iversion": "v1.0",
                "system_iteration": "iter1",
                "repository": "test_repo",
                "branch": "main",
                "dev_image": "test_image"
            }
        ]
        
        mock_postgres_db.delete_data.return_value = 3
        mock_postgres_db.insert_batch_data.return_value = 1
        
        handler_instance.update_defect_repos_mapping_to_pg_main(workspace_key, defect_repos_mapping_datas)
        
        # 验证删除调用
        mock_postgres_db.delete_data.assert_called_once_with(
            "defect_fix_repository_mapping", 
            {"workspace_key": workspace_key}
        )
        
        # 验证插入调用
        mock_postgres_db.insert_batch_data.assert_called_once()

    def test_update_defect_repos_mapping_to_pg_main_empty_data(self, handler_instance, mock_postgres_db):
        """测试update_defect_repos_mapping_to_pg_main方法 - 空数据"""
        workspace_key = "TEST_WORKSPACE"
        defect_repos_mapping_datas = []
        
        mock_postgres_db.delete_data.return_value = 0
        
        handler_instance.update_defect_repos_mapping_to_pg_main(workspace_key, defect_repos_mapping_datas)
        
        # 验证删除调用
        mock_postgres_db.delete_data.assert_called_once_with(
            "defect_fix_repository_mapping", 
            {"workspace_key": workspace_key}
        )
        
        # 验证没有插入调用
        mock_postgres_db.insert_batch_data.assert_not_called()

    def test_get_defect_repos_mapping_by_workspace_with_data(self, handler_instance, mock_postgres_db):
        """测试get_defect_repos_mapping_by_workspace方法 - 有数据"""
        workspace_key = "TEST_WORKSPACE"
        mock_data = [
            {
                "id": 1,
                "workspace_key": workspace_key,
                "belong_sub_system": "test_system",
                "discovery_iversion": "v1.0",
                "system_iteration": "iter1",
                "repository": "test_repo",
                "branch": "main",
                "dev_image": "test_image"
            }
        ]
        
        mock_postgres_db.select_data.return_value = mock_data
        
        result = handler_instance.get_defect_repos_mapping_by_workspace(workspace_key)
        
        mock_postgres_db.select_data.assert_called_once_with(
            "defect_fix_repository_mapping",
            {"workspace_key": workspace_key},
            fetchall=True
        )
        assert result == mock_data

    def test_get_defect_repos_mapping_by_workspace_no_data(self, handler_instance, mock_postgres_db):
        """测试get_defect_repos_mapping_by_workspace方法 - 无数据"""
        workspace_key = "TEST_WORKSPACE"
        mock_postgres_db.select_data.return_value = None
        
        result = handler_instance.get_defect_repos_mapping_by_workspace(workspace_key)
        
        assert result == []

    def test_get_defect_repos_mapping_by_conditions_all_params(self, handler_instance, mock_postgres_db):
        """测试get_defect_repos_mapping_by_conditions方法 - 所有参数"""
        workspace_key = "TEST_WORKSPACE"
        belong_sub_system = "test_system"
        discovery_iversion = "v1.0"
        system_iteration = "iter1"
        
        mock_data = [
            {
                "id": 1,
                "workspace_key": workspace_key,
                "belong_sub_system": belong_sub_system,
                "discovery_iversion": discovery_iversion,
                "system_iteration": system_iteration
            }
        ]
        
        mock_postgres_db.select_data.return_value = mock_data
        
        result = handler_instance.get_defect_repos_mapping_by_conditions(
            workspace_key, belong_sub_system, discovery_iversion, system_iteration
        )
        
        expected_conditions = {
            "workspace_key": workspace_key,
            "belong_sub_system": belong_sub_system,
            "discovery_iversion": discovery_iversion,
            "system_iteration": system_iteration
        }
        
        mock_postgres_db.select_data.assert_called_once_with(
            "defect_fix_repository_mapping",
            expected_conditions,
            fetchall=True
        )
        assert result == mock_data

    def test_get_defect_repos_mapping_by_conditions_partial_params(self, handler_instance, mock_postgres_db):
        """测试get_defect_repos_mapping_by_conditions方法 - 部分参数"""
        workspace_key = "TEST_WORKSPACE"
        belong_sub_system = "test_system"
        
        mock_data = [
            {
                "id": 1,
                "workspace_key": workspace_key,
                "belong_sub_system": belong_sub_system
            }
        ]
        
        mock_postgres_db.select_data.return_value = mock_data
        
        result = handler_instance.get_defect_repos_mapping_by_conditions(
            workspace_key, belong_sub_system
        )
        
        expected_conditions = {
            "workspace_key": workspace_key,
            "belong_sub_system": belong_sub_system
        }
        
        mock_postgres_db.select_data.assert_called_once_with(
            "defect_fix_repository_mapping",
            expected_conditions,
            fetchall=True
        )
        assert result == mock_data

    def test_get_defect_repos_mapping_by_conditions_only_workspace(self, handler_instance, mock_postgres_db):
        """测试get_defect_repos_mapping_by_conditions方法 - 只有workspace参数"""
        workspace_key = "TEST_WORKSPACE"
        
        mock_data = [
            {
                "id": 1,
                "workspace_key": workspace_key
            }
        ]
        
        mock_postgres_db.select_data.return_value = mock_data
        
        result = handler_instance.get_defect_repos_mapping_by_conditions(workspace_key)
        
        expected_conditions = {"workspace_key": workspace_key}
        
        mock_postgres_db.select_data.assert_called_once_with(
            "defect_fix_repository_mapping",
            expected_conditions,
            fetchall=True
        )
        assert result == mock_data

    def test_get_defect_repos_mapping_by_conditions_no_data(self, handler_instance, mock_postgres_db):
        """测试get_defect_repos_mapping_by_conditions方法 - 无数据"""
        workspace_key = "TEST_WORKSPACE"
        mock_postgres_db.select_data.return_value = None
        
        result = handler_instance.get_defect_repos_mapping_by_conditions(workspace_key)
        
        assert result == []

    def test_insert_new_records_with_empty_strings(self, handler_instance, mock_postgres_db):
        """测试_insert_new_records方法 - 空字符串字段"""
        workspace_key = "TEST_WORKSPACE"
        defect_repos_mapping_datas = [
            {
                "belong_sub_system": "test_system",
                "discovery_iversion": "v1.0",
                "system_iteration": "iter1",
                "repository": "test_repo",
                "branch": "main",
                "dev_image": "test_image",
                "test_script_repository": "",
                "test_script_branch": ""
            }
        ]
        
        mock_postgres_db.insert_batch_data.return_value = 1
        
        result = handler_instance._insert_new_records(workspace_key, defect_repos_mapping_datas)
        
        call_args = mock_postgres_db.insert_batch_data.call_args
        insert_data_list = call_args[0][1]
        
        record = insert_data_list[0]
        assert record["test_script_repository"] is None
        assert record["test_script_branch"] is None
        
        assert result == 1

    def test_insert_new_records_with_none_values(self, handler_instance, mock_postgres_db):
        """测试_insert_new_records方法 - None值字段"""
        workspace_key = "TEST_WORKSPACE"
        defect_repos_mapping_datas = [
            {
                "belong_sub_system": "test_system",
                "discovery_iversion": "v1.0",
                "system_iteration": "iter1",
                "repository": "test_repo",
                "branch": "main",
                "dev_image": "test_image",
                "test_script_repository": None,
                "test_script_branch": None
            }
        ]
        
        mock_postgres_db.insert_batch_data.return_value = 1
        
        result = handler_instance._insert_new_records(workspace_key, defect_repos_mapping_datas)
        
        call_args = mock_postgres_db.insert_batch_data.call_args
        insert_data_list = call_args[0][1]
        
        record = insert_data_list[0]
        assert record["test_script_repository"] is None
        assert record["test_script_branch"] is None
        
        assert result == 1
